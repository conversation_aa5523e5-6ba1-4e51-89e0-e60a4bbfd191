# Extracted Text Debug File
# Source File: Full-Stack-Developer.pdf
# Context: jd_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-07-14 19:10:09
# Text Length: 1995 characters
# ================================================

Job Description : Full Stack Developer  
 
Designation  Full Stack Developer  
Responsibilities  • Build highly responsive web applications with engaging user 
experience  
• Design client -side and server -side architecture  
• Write production ready code for both front end and back end 
systems  
• Develop user friendly web front end based on visual designs 
and branding guidelines  
• Conduct thorough testing of the developed applications and fix 
identified bugs  
• Create documentation for applications developed  
Employment 
type Flexible working hours (full -time / part -time) for a period of 1 year 
with monthly remuneration between Rs.15,000 to Rs.30,000 
depending on hours of working and education level  
Location  Bengaluru  
Education  • Students in third or final year of engineering  in Computer 
Science  / Electronics / IT  or who have completed graduation in 
these courses  
• Should have consistent marks above 75% or GPA 3.0/4.0 or 
above in all previous semesters  
Experience level  Experience will be considered, but not necessary  
Essential  skills  • Thorough understanding of UX best practices and 
implementation of simple, engaging user interfaces   
• Knowledge of latest version and practices in  HTML, CSS , PHP, 
Python, JavaScript , REST API  
• Familiarity with web servers (e.g. Apache) , content management 
systems (e.g. Wordpress) and databases (e.g. MySQL, MongoDB),  
• Database design and management  
• Proficiency with Git or other similar version control system s 
• Ability to write clean and well documented code  
• Ability to work in  small teams, where processes need to be 
defined / finetuned  
• Passion for technology and staying up to date with latest trends  
• Drive to achieve results in challenging circumstances  
• Ability to troubleshoot independently  
Desirable skills  • Mobile app / progressive web app development experience  
• Knowledge of React framework  
• A flair for creativity and out of the box thinking  
  
