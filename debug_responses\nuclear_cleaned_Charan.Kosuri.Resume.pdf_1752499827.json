{"experience": [{"company": "Chase Bank", "location": "Plano", "roles": ["Afinalysis", "Design", "Development", "Sprint Planning"], "technologies": ["Java 8/11", "Spring Boot 2.x", "RESTful APIs", "RS", "OAuth 2.0", "Apache Camel", "Kafka", "RabbitMQ", "ActiveMQ", "Kubernetes", "<PERSON>er", "CI/CD", "Terraform", "AWS (Lambda, S3, VPC, IAM, Route 53, EKS, CloudWatch)", "Python (Django, Flask, Fast API)", "Angular 10/11", "React.js", "Express.js", "SCSS"], "methodologies": ["Agile Methodology", "Scrum"]}, {"company": "OG Software Solutions", "location": "Chennai", "roles": ["Jobs Application Server Implementation", "Support", "Development"], "technologies": ["Java 8/11", "<PERSON><PERSON><PERSON>", "WebLogic 8.x/10.x", "Drools BRMS", "Tomcat 5.x/6.x/7.x", "SQL (Oracle 9i/10g/11g)", "Oracle 9i/10g/11g", "<PERSON><PERSON><PERSON>", "WebLogic 8.x/10.x", "Drools BRMS", "Tomcat 5.x/6.x/7.x", "SQL (Oracle 9i/10g/11g)"], "methodologies": ["Agile Methodology", "Scrum"]}, {"company": "Elegant Microweb", "location": "Remote", "roles": ["Front End Development", "Back End Development"], "technologies": ["Shadow DOM", "React", "Redux", "React Router"]}], "skills": {"languages": ["Java", "Python", "JavaScript", "TypeScript"], "frameworks": ["Spring Boot", "React.js", "AngularJS", "React", "Express.js"], "databases": ["Oracle", "MySQL", "<PERSON>", "Redis", "MongoDB"], "tools": ["Git", "<PERSON>", "<PERSON>er", "Kubernetes", "Terraform", "Postman", "Swagger", "Eclipse", "Visual Studio Code"], "methodologies": ["Agile", "Scrum", "Waterfall"], "other": ["RESTful APIs", "SOAP", "GraphQL", "JSON", "XML", "HTML", "CSS", "SCSS", "JavaScript", "TypeScript"]}}