{"timestamp": "20250708_144254_801", "calculation_method": "CGPA", "total_credits_used": 10.0, "processing_time_seconds": 0.011001348495483398, "final_score": 3.15, "fit_category": "Weak Match", "summary": "The candidate is a weak match for this position with a CGPA-style score of 3.1/10. Areas for improvement: Skills, Certifications.", "field_scores": {"skills": {"raw_score": 0.5, "weight": 4.0, "weighted_score": 2.0, "rationale": "Limited skills match - has 1/16 required skills, major skill development needed", "details": {"resume_skills_count": 17, "required_skills_count": 16, "preferred_skills_count": 0, "matched_required": ["Statistical programming tools (e.g., R, Python, SQL)"], "missing_required": ["Operating standard office equipment including utilizing pertinent software applications (e.g., Word, Excel, databases, PowerPoint)", "Planning and managing projects and programs", "Operating within established financial parameters", "Developing effective working relationships", "Preparing and maintaining accurate records and dashboards", "Administering personnel policies and procedures as assigned", "Applying program evaluation and assessment techniques", "Advanced data mining and analysis techniques", "Student data security protocols", "Data visualization tools (e.g., Tableau, D3, AJAX, or jQuery)", "Complex mathematical calculations and projections", "Administrative and technical expertise with Customer Relationship Management (CRM) platforms", "Data warehouse tools", "Machine learning techniques, including classification and outlier detection of data", "Business process improvement tools (e.g., LEAN/Six Sigma or similar)"], "matched_preferred": [], "required_match_ratio": 0.0625, "preferred_match_ratio": 0, "_calculation_steps": ["Step 1: Required skills score = 1/16 × 8 = 0.50", "Step 2: No preferred skills specified, bonus = 0.00", "Step 3: Total score = min(10, 0.50 + 0.00) = 0.50"], "_scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "_explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)"}}, "experience": {"raw_score": 5.0, "weight": 3.0, "weighted_score": 15.0, "rationale": "Moderate experience level - 0 years, some experience gaps", "details": {"candidate_yoe": 0, "required_yoe": null, "experience_ratio": null, "experience_entries_count": 1, "_experience_breakdown": [{"company": "Unknown Company", "position": "Unknown Position", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}], "_calculation_steps": ["Step 1: Could not extract numeric experience from 'Three (3) years of professional-level experience in data mining/analysis, including conducting large-scale data analyses to evaluate and/or present recommendations to innovate processes or resolve issues.'", "Step 2: Analyzing 1 experience entries", "  Entry 1: Unknown Company - Unknown Position (Present) = 0 years (could not parse)", "Step 2 Result: Total candidate experience = 0 years", "Step 3: Calculating experience score", "  ~ No experience requirement specified: Score = 5.0 (neutral)"], "_scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "_explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification"}}, "education": {"raw_score": 6.0, "weight": 2.0, "weighted_score": 12.0, "rationale": "Good education match - has related degree with some relevance", "details": {"education_entries_count": 3, "education_requirements_count": 1, "education_match": false, "partial_match": true, "matched_degree": "Bachelor of Technology (AI ML)", "matched_requirement": "A master’s degree from an accredited college or university in computer science, mathematics, statistics, analytics, economics, or other closely related field.", "match_type": "field_match", "candidate_degrees": ["Bachelor of Technology (AI ML)", "Intermediate", "Matriculation"], "required_degrees": ["A master’s degree from an accredited college or university in computer science, mathematics, statistics, analytics, economics, or other closely related field."], "education_requirements": ["A master’s degree from an accredited college or university in computer science, mathematics, statistics, analytics, economics, or other closely related field."], "top_universities_found": [], "_calculation_steps": ["Step 1: Checking 1 education requirement(s)", "  - Analyzing requirement: 'A master’s degree from an accredited college or university in computer science, mathematics, statistics, analytics, economics, or other closely related field.'", "    Required degree type: master", "    Required field: computer science", "    Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)", "    ~ PARTIAL MATCH: Same field (computer science) but different degree level", "    Candidate degree: 'Intermediate' (Type: intermediate, Field: Unknown)", "    Candidate degree: 'Matriculation' (Type: matriculation, Field: Unknown)", "Step 2: Applying binary scoring system", "  ~ Partial education match (benefit of doubt): Score = 6.0"], "_scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "_explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)"}}, "certifications": {"raw_score": 0.0, "weight": 0.5, "weighted_score": 0.0, "rationale": "No relevant certifications - 1 certifications but none match job requirements", "details": {"total_certifications": 1, "relevant_certifications": [], "relevant_count": 0, "all_certifications": ["Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024. Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype."], "_irrelevant_certifications": ["Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024. Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype."], "_certification_analysis": [{"certification": "Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024. Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype.", "relevant": false, "matched_skills": [], "points_awarded": 0}], "_calculation_steps": ["Step 1: Found 1 certifications in resume", "Step 2: Checking relevance against 16 job skills (16 required + 0 preferred)", "  Cert 1: 'Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024. Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype.' - NOT RELEVANT = +0 points", "Step 3: Score calculation", "  Base score: 0 relevant certs × 2 points = 0", "  Final score: min(10, 0) = 0"], "_scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "_explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10"}}, "location": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate location match - some geographic alignment", "details": {"jd_location": "san diego, ca", "resume_location": "", "experience_locations": [], "location_match_found": false, "has_location_info": false, "_calculation_steps": ["Step 1: Location extraction", "  Job location: 'san diego, ca' (from JD)", "  Resume location: '' (from resume)", "  Experience locations: [] (from work history)", "Step 2: Location matching analysis", "  ~ Insufficient location data: Score = 5.0 (neutral)"], "_scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "_explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}}, "reliability": {"raw_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Moderate job stability - average 0.0 years per company", "details": {"candidate_yoe": 0, "num_companies": 1, "avg_tenure": 0.0, "has_experience_data": false, "_tenure_breakdown": [{"company": "Unknown Company", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}], "_calculation_steps": ["Step 1: Analyzing 1 experience entries for tenure calculation", "  Entry 1: Unknown Company (Present) = 0 years (could not parse)", "Step 1 Result: Total experience = 0 years", "Step 2: Calculating job stability/reliability", "  Total companies: 1", "  Total years: 0", "  ~ Insufficient data for calculation: Score = 5.0 (neutral)"], "_scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "_explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history"}}}}