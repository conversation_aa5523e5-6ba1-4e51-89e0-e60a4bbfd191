{"name": null, "email": null, "phone": null, "education": [{"degree": "Students in third or fifinal year of engineering in Computer Science / Electronics / IT or who have completed graduation in these courses", "institution": null, "year": "third or fifinal year of engineering", "grade": "above 75% or GPA 3.0/4.0 or above in all previous semesters"}], "highest_education": "Students in third or fifinal year of engineering in Computer Science / Electronics / IT or who have completed graduation in these courses", "skills": ["UX best practices", "HTML", "CSS", "PHP", "Python", "JavaScript", "REST API", "Apache", "Wordpress", "MySQL", "MongoDB", "Git", "Clean code", "Well documented code"], "experience": [{"company_name": null, "role": "Full Stack Developer", "duration": "1 year", "key_responsibilities": "Build highly responsive web applications with engaging user interfaces. Thorough understanding of UX best practices and implementation of simple, engaging user interfaces. Knowledge of latest version and practices in HTML, CSS, PHP, Python, JavaScript, REST API. Familiarity with web servers (e.g. Apache), content management systems (e.g. Wordpress) and databases (e.g. MySQL, MongoDB). Database design and management. Proficiency with Git or other similar version control system s. Ability to write clean and well documented code. Ability to work in small teams, where processes need to be defined / finetuned. Passion for technology and staying up to date with latest trends. Drive to achieve results in challenging circumstances. Ability to troubleshoot independently."}], "projects": [], "certifications": [], "domain_of_interest": [], "languages_known": [], "achievements": ["Thorough understanding of UX best practices and implementation of simple, engaging user interfaces", "Knowledge of latest version and practices in HTML, CSS, PHP, Python, JavaScript, REST API", "Familiarity with web servers (e.g. Apache), content management systems (e.g. Wordpress) and databases (e.g. MySQL, MongoDB)", "Proficiency with Git or other similar version control system s", "Ability to write clean and well documented code", "Ability to troubleshoot independently"], "publications": [], "volunteer_experience": [], "references": [], "summary": null, "persofinal_projects": [], "social_media": []}