{"event": "session_start", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "timestamp": "2025-07-09T10:33:53.554492", "message": "New API session started"}
{"event": "request_start", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "0c27bf1e-7d87-4761-88ad-e2f7b34a9314", "endpoint": "/", "timestamp": "2025-07-09T10:33:55.960787", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "0c27bf1e-7d87-4761-88ad-e2f7b34a9314", "endpoint": "/", "timestamp": "2025-07-09T10:33:55.963789", "total_time_seconds": 0.003002166748046875, "status_code": 200, "message": "Request completed in 0.0030s with status 200"}
{"event": "request_start", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "9aa40851-3780-4711-b0a6-05bf2d3aa5a5", "endpoint": "/favicon.ico", "timestamp": "2025-07-09T10:33:56.082082", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "9aa40851-3780-4711-b0a6-05bf2d3aa5a5", "endpoint": "/favicon.ico", "timestamp": "2025-07-09T10:33:56.083592", "total_time_seconds": 0.0015099048614501953, "status_code": 404, "message": "Request completed in 0.0015s with status 404"}
{"event": "request_start", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "a67c45a4-592a-478b-a70d-174952143a3e", "endpoint": "/docs", "timestamp": "2025-07-09T10:34:01.364664", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "a67c45a4-592a-478b-a70d-174952143a3e", "endpoint": "/docs", "timestamp": "2025-07-09T10:34:01.365661", "total_time_seconds": 0.0009968280792236328, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "67083ece-7957-4dfd-ba27-a659d1c9ceaa", "endpoint": "/openapi.json", "timestamp": "2025-07-09T10:34:01.507208", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "67083ece-7957-4dfd-ba27-a659d1c9ceaa", "endpoint": "/openapi.json", "timestamp": "2025-07-09T10:34:01.536549", "total_time_seconds": 0.029340744018554688, "status_code": 200, "message": "Request completed in 0.0293s with status 200"}
{"event": "request_start", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "7ed8c45f-fabe-40de-8414-247475b6488d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-09T10:34:15.493130", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "7ed8c45f-fabe-40de-8414-247475b6488d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-09T10:34:15.534410", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "7ed8c45f-fabe-40de-8414-247475b6488d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-09T10:34:15.534410", "file_size_bytes": 70558, "message": "Custom metric: file_size_bytes=70558"}
{"event": "custom_metric", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "7ed8c45f-fabe-40de-8414-247475b6488d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-09T10:34:15.534410", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "7ed8c45f-fabe-40de-8414-247475b6488d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-09T10:34:15.535409", "extracted_text_length": 1995, "message": "Custom metric: extracted_text_length=1995"}
{"event": "custom_metric", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "7ed8c45f-fabe-40de-8414-247475b6488d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-09T10:34:15.535409", "file_processing_time": 0.03576326370239258, "message": "Custom metric: file_processing_time=0.03576326370239258"}
{"event": "request_complete", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "request_id": "7ed8c45f-fabe-40de-8414-247475b6488d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-09T10:34:34.255774", "total_time_seconds": 18.762644290924072, "status_code": 200, "message": "Request completed in 18.7626s with status 200"}
{"event": "session_end", "session_id": "a8d9f3b6-2088-475b-9b15-1795fa3665dc", "timestamp": "2025-07-09T10:36:43.955966", "message": "API session ended"}
