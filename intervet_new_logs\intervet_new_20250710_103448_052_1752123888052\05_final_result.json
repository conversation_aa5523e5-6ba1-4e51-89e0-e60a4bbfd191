{"total_score": 3.471212121212121, "fit_category": "Weak Match", "summary": "The candidate is a weak match for this position with a CGPA-style score of 3.5/10. Areas for improvement: Education, Certifications.", "skills_score": {"raw_score": 4.303030303030303, "weight": 4.0, "weighted_score": 17.21212121212121, "rationale": "Moderate skills match - has 5/11 required skills, significant skill gaps"}, "experience_score": {"raw_score": 5.0, "weight": 3.0, "weighted_score": 15.0, "rationale": "Moderate experience level - 0 years, some experience gaps"}, "education_score": {"raw_score": 0.0, "weight": 2.0, "weighted_score": 0.0, "rationale": "Education requirements not met - lacks required degree"}, "certifications_score": {"raw_score": 0.0, "weight": 0.5, "weighted_score": 0.0, "rationale": "No certifications listed"}, "location_score": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate location match - some geographic alignment"}, "reliability_score": {"raw_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Moderate job stability - average 0.0 years per company"}, "detailed_rationale": {"skills_match_direct": "Matched 5/11 required skills and 1/3 preferred skills. Matched required skills: HTML, CSS, Python, JavaScript, MySQL. Missing required skills: PHP, REST API, Git, MongoDB, Apache, Wordpress", "experience_match": "No specific experience requirement found in job description", "reliability": "Poor stability: frequent job changes with 0.0 years per company", "location_match": "No location match found with job location (bengaluru)", "academic_match": "Education requirements not met. Candidate: Bachelor of Technology (AI ML), Intermediate, Matriculation, Required: Students in third or final year of engineering in Computer Science / Electronics / IT, Should have consistent marks above 75% or GPA 3.0/4.0 or above in all previous semesters", "alma_mater": "No top-ranked universities found in education history", "certifications": "No relevant certifications found"}, "total_credits_used": 10.0, "calculation_method": "CGPA", "processing_time": 0.01303863525390625}