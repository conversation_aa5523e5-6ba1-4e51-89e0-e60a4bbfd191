#!/usr/bin/env python3
"""
Launcher script for the Enhanced Intervet2 GUI
This script checks dependencies and launches the GUI application
"""

import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['requests', 'tkinter']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """Install missing dependencies"""
    print(f"Installing missing packages: {', '.join(packages)}")
    for package in packages:
        if package == 'tkinter':
            print("Note: tkinter is usually included with Python. If it's missing, you may need to reinstall Python with tkinter support.")
            continue
        
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ Successfully installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def check_server_connection():
    """Check if the API server is accessible"""
    try:
        import requests
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running and accessible")
            return True
        else:
            print(f"⚠️  API server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API server: {e}")
        print("Make sure to start the server with: python main.py")
        return False

def main():
    """Main launcher function"""
    print("🚀 Enhanced Intervet2 GUI Launcher")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python version: {sys.version}")
    
    # Check dependencies
    print("\n📦 Checking dependencies...")
    missing = check_dependencies()
    
    if missing:
        print(f"❌ Missing packages: {', '.join(missing)}")
        
        # Ask user if they want to install missing packages
        response = input("Do you want to install missing packages? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            if not install_dependencies(missing):
                print("❌ Failed to install some dependencies. Please install them manually.")
                sys.exit(1)
        else:
            print("❌ Cannot proceed without required dependencies")
            sys.exit(1)
    else:
        print("✅ All dependencies are installed")
    
    # Check server connection
    print("\n🌐 Checking API server connection...")
    server_running = check_server_connection()
    
    if not server_running:
        response = input("API server is not running. Do you want to continue anyway? (y/n): ").lower().strip()
        if response not in ['y', 'yes']:
            print("❌ Exiting. Please start the API server first.")
            sys.exit(1)
    
    # Launch the GUI
    print("\n🎨 Launching Enhanced Intervet2 GUI...")
    try:
        # Add the current directory to the path so we can import the GUI module
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        # Import and run the GUI
        from enhanced_intervet2_gui import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"❌ Failed to import GUI module: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to launch GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
