{"event": "session_start", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "timestamp": "2025-07-08T13:48:11.243946", "message": "New API session started"}
{"event": "request_start", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "request_id": "98554af7-ded5-43f7-b42f-472919a21304", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:48:13.654751", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "request_id": "98554af7-ded5-43f7-b42f-472919a21304", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:48:13.671750", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "request_id": "98554af7-ded5-43f7-b42f-472919a21304", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:48:13.672750", "file_size_bytes": 72406, "message": "Custom metric: file_size_bytes=72406"}
{"event": "custom_metric", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "request_id": "98554af7-ded5-43f7-b42f-472919a21304", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:48:13.672750", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "request_id": "98554af7-ded5-43f7-b42f-472919a21304", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:48:13.672750", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "request_id": "98554af7-ded5-43f7-b42f-472919a21304", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:48:13.672750", "file_processing_time": 0.013998746871948242, "message": "Custom metric: file_processing_time=0.013998746871948242"}
{"event": "request_complete", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "request_id": "98554af7-ded5-43f7-b42f-472919a21304", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:48:55.545978", "total_time_seconds": 41.89122676849365, "status_code": 200, "message": "Request completed in 41.8912s with status 200"}
{"event": "session_end", "session_id": "2e151b58-a366-4b50-8168-db4200aca0e6", "timestamp": "2025-07-08T14:10:57.181934", "message": "API session ended"}
