{"event": "session_start", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "timestamp": "2025-07-08T14:12:19.787329", "message": "New API session started"}
{"event": "request_start", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "4bf7f35f-4a70-427d-befe-274e6073fcd9", "endpoint": "/", "timestamp": "2025-07-08T14:12:21.306897", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "4bf7f35f-4a70-427d-befe-274e6073fcd9", "endpoint": "/", "timestamp": "2025-07-08T14:12:21.309412", "total_time_seconds": 0.002515077590942383, "status_code": 200, "message": "Request completed in 0.0025s with status 200"}
{"event": "request_start", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "db15ffa4-ed41-4c9c-8e6e-546fc33d345b", "endpoint": "/docs", "timestamp": "2025-07-08T14:12:29.197633", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "db15ffa4-ed41-4c9c-8e6e-546fc33d345b", "endpoint": "/docs", "timestamp": "2025-07-08T14:12:29.197633", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "eb0b8888-90d2-4507-aab2-ddba9500f73f", "endpoint": "/openapi.json", "timestamp": "2025-07-08T14:12:29.263345", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "eb0b8888-90d2-4507-aab2-ddba9500f73f", "endpoint": "/openapi.json", "timestamp": "2025-07-08T14:12:29.280658", "total_time_seconds": 0.017313003540039062, "status_code": 200, "message": "Request completed in 0.0173s with status 200"}
{"event": "request_start", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "082ceafd-1a6b-4888-b316-122b6030ce5d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:12:55.276134", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "082ceafd-1a6b-4888-b316-122b6030ce5d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:12:55.299691", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "082ceafd-1a6b-4888-b316-122b6030ce5d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:12:55.299691", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "082ceafd-1a6b-4888-b316-122b6030ce5d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:12:55.299691", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "082ceafd-1a6b-4888-b316-122b6030ce5d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:12:55.299691", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "082ceafd-1a6b-4888-b316-122b6030ce5d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:12:55.299691", "file_processing_time": 0.020043134689331055, "message": "Custom metric: file_processing_time=0.020043134689331055"}
{"event": "request_complete", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "082ceafd-1a6b-4888-b316-122b6030ce5d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:13:14.107505", "total_time_seconds": 18.83137059211731, "status_code": 200, "message": "Request completed in 18.8314s with status 200"}
{"event": "request_start", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "64591a00-8284-4288-9abc-62d5de678e82", "endpoint": "/jd_parser", "timestamp": "2025-07-08T14:13:20.577590", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "64591a00-8284-4288-9abc-62d5de678e82", "endpoint": "/jd_parser", "timestamp": "2025-07-08T14:13:42.245380", "total_time_seconds": 21.667790174484253, "status_code": 200, "message": "Request completed in 21.6678s with status 200"}
{"event": "request_start", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "70a3b091-7d00-42cf-a607-a9e5324c7091", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:14:10.922464", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "70a3b091-7d00-42cf-a607-a9e5324c7091", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:14:10.924460", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "70a3b091-7d00-42cf-a607-a9e5324c7091", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:14:10.951664", "final_score": 4.423684210526316, "message": "Custom metric: final_score=4.423684210526316"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "70a3b091-7d00-42cf-a607-a9e5324c7091", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:14:10.951664", "fit_category": "Moderate Match", "message": "Custom metric: fit_category=Moderate Match"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "70a3b091-7d00-42cf-a607-a9e5324c7091", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:14:10.951664", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "70a3b091-7d00-42cf-a607-a9e5324c7091", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:14:10.951664", "log_folder": "intervet_new_logs\\intervet_new_20250708_141410_942_1751964250942", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_141410_942_1751964250942"}
{"event": "request_complete", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "request_id": "70a3b091-7d00-42cf-a607-a9e5324c7091", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:14:10.952665", "total_time_seconds": 0.030200481414794922, "status_code": 200, "message": "Request completed in 0.0302s with status 200"}
{"event": "session_end", "session_id": "d2ded66c-d4e9-4bd9-9472-a2fa0c5e4485", "timestamp": "2025-07-08T14:15:16.996465", "message": "API session ended"}
