{"timestamp": "20250708_133613_762", "calculation_method": "CGPA", "total_credits_used": 10.0, "processing_time_seconds": 0.0009996891021728516, "final_score": 6.04, "fit_category": "Good Match", "summary": "The candidate is a good match for this position with a CGPA-style score of 6.0/10. Key strengths: Education. Areas for improvement: Certifications.", "field_scores": {"skills": {"raw_score": 6.8, "weight": 3.0, "weighted_score": 20.4, "rationale": "Good skills match - has 3/5 required skills, some gaps in preferred skills", "details": {"resume_skills_count": 17, "required_skills_count": 5, "preferred_skills_count": 1, "matched_required": ["HTML", "CSS", "Javascript"], "missing_required": ["Media queries", "SDLC"], "matched_preferred": ["React"], "required_match_ratio": 0.6, "preferred_match_ratio": 1.0, "_calculation_steps": ["Step 1: Required skills score = 3/5 × 8 = 4.80", "Step 2: Preferred skills bonus = 1/1 × 2 = 2.00", "Step 3: Total score = min(10, 4.80 + 2.00) = 6.80"], "_scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "_explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)"}}, "experience": {"raw_score": 5.0, "weight": 2.5, "weighted_score": 12.5, "rationale": "Moderate experience level - 0 years, some experience gaps", "details": {"candidate_yoe": 0, "required_yoe": null, "experience_ratio": null, "experience_entries_count": 1, "_experience_breakdown": [{"company": "Unknown Company", "position": "Unknown Position", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}], "_calculation_steps": ["Step 1: No required experience specified in job description", "Step 2: Analyzing 1 experience entries", "  Entry 1: Unknown Company - Unknown Position (Present) = 0 years (could not parse)", "Step 2 Result: Total candidate experience = 0 years", "Step 3: Calculating experience score", "  ~ No experience requirement specified: Score = 5.0 (neutral)"], "_scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "_explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification"}}, "education": {"raw_score": 10.0, "weight": 2.0, "weighted_score": 20.0, "rationale": "Perfect education match - meets all degree requirements", "details": {"education_entries_count": 3, "education_requirements_count": 1, "education_match": true, "partial_match": false, "matched_degree": "Bachelor of Technology (AI ML)", "matched_requirement": "Bachelor's in IT, Computer Science, Software Engineering, or a related field", "match_type": "exact_match", "candidate_degrees": ["Bachelor of Technology (AI ML)", "Intermediate", "Matriculation"], "required_degrees": ["Bachelor's in IT, Computer Science, Software Engineering, or a related field"], "education_requirements": ["Bachelor's in IT, Computer Science, Software Engineering, or a related field"], "top_universities_found": [], "_calculation_steps": ["Step 1: Checking 1 education requirement(s)", "  - Analyzing requirement: 'Bachelor's in IT, Computer Science, Software Engineering, or a related field'", "    Required degree type: bachelor", "    Required field: computer science", "    Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)", "    ✓ EXACT MATCH FOUND: Degree type and field match", "Step 2: Applying binary scoring system", "  ✓ Education requirement met: Score = 10.0"], "_scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "_explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)"}}, "certifications": {"raw_score": 0.0, "weight": 1.0, "weighted_score": 0.0, "rationale": "No certifications listed", "details": {"total_certifications": 0, "relevant_certifications": [], "relevant_count": 0, "all_certifications": [], "_irrelevant_certifications": [], "_certification_analysis": [], "_calculation_steps": ["Step 1: Found 0 certifications in resume", "Step 2: Checking relevance against 6 job skills (5 required + 1 preferred)", "Step 3: Score calculation", "  Base score: 0 relevant certs × 2 points = 0", "  Final score: min(10, 0) = 0"], "_scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "_explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10"}}, "location": {"raw_score": 5.0, "weight": 1.0, "weighted_score": 5.0, "rationale": "Moderate location match - some geographic alignment", "details": {"jd_location": "san francisco, ca", "resume_location": "", "experience_locations": [], "location_match_found": false, "has_location_info": false, "_calculation_steps": ["Step 1: Location extraction", "  Job location: 'san francisco, ca' (from JD)", "  Resume location: '' (from resume)", "  Experience locations: [] (from work history)", "Step 2: Location matching analysis", "  ~ Insufficient location data: Score = 5.0 (neutral)"], "_scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "_explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}}, "reliability": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate job stability - average 0.0 years per company", "details": {"candidate_yoe": 0, "num_companies": 1, "avg_tenure": 0.0, "has_experience_data": false, "_tenure_breakdown": [{"company": "Unknown Company", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}], "_calculation_steps": ["Step 1: Analyzing 1 experience entries for tenure calculation", "  Entry 1: Unknown Company (Present) = 0 years (could not parse)", "Step 1 Result: Total experience = 0 years", "Step 2: Calculating job stability/reliability", "  Total companies: 1", "  Total years: 0", "  ~ Insufficient data for calculation: Score = 5.0 (neutral)"], "_scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "_explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history"}}}}