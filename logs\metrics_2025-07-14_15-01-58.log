{"event": "session_start", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "timestamp": "2025-07-14T15:01:58.165833", "message": "New API session started"}
{"event": "request_start", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "request_id": "2a75f114-d29c-45a5-8ae5-e51b0f445e35", "endpoint": "/", "timestamp": "2025-07-14T15:02:01.230414", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "request_id": "2a75f114-d29c-45a5-8ae5-e51b0f445e35", "endpoint": "/", "timestamp": "2025-07-14T15:02:01.232425", "total_time_seconds": 0.0020105838775634766, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "request_id": "214c2f74-5ed2-4f80-8a56-63b838c49da1", "endpoint": "/favicon.ico", "timestamp": "2025-07-14T15:02:02.324237", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "request_id": "214c2f74-5ed2-4f80-8a56-63b838c49da1", "endpoint": "/favicon.ico", "timestamp": "2025-07-14T15:02:02.325242", "total_time_seconds": 0.0010046958923339844, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "request_id": "b69c1ce5-6402-41c9-b94e-2c652406d729", "endpoint": "/docs", "timestamp": "2025-07-14T15:02:04.585618", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "request_id": "b69c1ce5-6402-41c9-b94e-2c652406d729", "endpoint": "/docs", "timestamp": "2025-07-14T15:02:04.586620", "total_time_seconds": 0.0010023117065429688, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "request_id": "13a565ea-ebef-4127-b206-bd664540ac00", "endpoint": "/openapi.json", "timestamp": "2025-07-14T15:02:05.143706", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "request_id": "13a565ea-ebef-4127-b206-bd664540ac00", "endpoint": "/openapi.json", "timestamp": "2025-07-14T15:02:05.301441", "total_time_seconds": 0.15773439407348633, "status_code": 200, "message": "Request completed in 0.1577s with status 200"}
{"event": "session_end", "session_id": "cdb56318-0dc3-4e4d-8c07-cd30578582f0", "timestamp": "2025-07-14T15:02:41.946981", "message": "API session ended"}
