{"timestamp": "20250710_103448_052", "calculation_method": "CGPA", "total_credits_used": 10.0, "processing_time_seconds": 0.01303863525390625, "final_score": 3.***************, "fit_category": "Weak Match", "summary": "The candidate is a weak match for this position with a CGPA-style score of 3.5/10. Areas for improvement: Education, Certifications.", "field_scores": {"skills": {"raw_score": 4.303030303030303, "weight": 4.0, "weighted_score": 17.21212121212121, "rationale": "Moderate skills match - has 5/11 required skills, significant skill gaps", "details": {"resume_skills_count": 17, "required_skills_count": 11, "preferred_skills_count": 3, "matched_required": ["HTML", "CSS", "Python", "JavaScript", "MySQL"], "missing_required": ["PHP", "REST API", "Git", "MongoDB", "Apache", "Wordpress"], "matched_preferred": ["React framework"], "required_match_ratio": 0.*****************, "preferred_match_ratio": 0.3333333333333333, "_calculation_steps": ["Step 1: Required skills score = 5/11 × 8 = 3.64", "Step 2: Preferred skills bonus = 1/3 × 2 = 0.67", "Step 3: Total score = min(10, 3.64 + 0.67) = 4.30"], "_scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "_explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)"}}, "experience": {"raw_score": 5.0, "weight": 3.0, "weighted_score": 15.0, "rationale": "Moderate experience level - 0 years, some experience gaps", "details": {"candidate_yoe": 0, "required_yoe": null, "experience_ratio": null, "experience_entries_count": 1, "_experience_breakdown": [{"company": "Unknown Company", "position": "Unknown Position", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}], "_calculation_steps": ["Step 1: Could not extract numeric experience from 'Experience will be considered, but not necessary'", "Step 2: Analyzing 1 experience entries", "  Entry 1: Unknown Company - Unknown Position (Present) = 0 years (could not parse)", "Step 2 Result: Total candidate experience = 0 years", "Step 3: Calculating experience score", "  ~ No experience requirement specified: Score = 5.0 (neutral)"], "_scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "_explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification"}}, "education": {"raw_score": 0.0, "weight": 2.0, "weighted_score": 0.0, "rationale": "Education requirements not met - lacks required degree", "details": {"education_entries_count": 3, "education_requirements_count": 2, "education_match": false, "partial_match": false, "matched_degree": "", "matched_requirement": "", "match_type": "", "candidate_degrees": ["Bachelor of Technology (AI ML)", "Intermediate", "Matriculation"], "required_degrees": ["Students in third or final year of engineering in Computer Science / Electronics / IT", "Should have consistent marks above 75% or GPA 3.0/4.0 or above in all previous semesters"], "education_requirements": ["Students in third or final year of engineering in Computer Science / Electronics / IT", "Should have consistent marks above 75% or GPA 3.0/4.0 or above in all previous semesters"], "top_universities_found": [], "_calculation_steps": ["Step 1: Checking 2 education requirement(s)", "  - Analyzing requirement: 'Students in third or final year of engineering in Computer Science / Electronics / IT'", "    Required degree type: Not specified", "    Required field: computer science", "    Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)", "    ✗ SKIPPED: 'Students in third or final year of engineering in Computer Science / Electronics / IT' is not an education requirement (no degree-related terms found)", "    ~ PARTIAL MATCH: Keyword similarity found", "    Candidate degree: 'Intermediate' (Type: intermediate, Field: Unknown)", "    ✗ SKIPPED: 'Students in third or final year of engineering in Computer Science / Electronics / IT' is not an education requirement (no degree-related terms found)", "    ~ PARTIAL MATCH: Keyword similarity found", "    Candidate degree: 'Matriculation' (Type: matriculation, Field: Unknown)", "    ✗ SKIPPED: 'Students in third or final year of engineering in Computer Science / Electronics / IT' is not an education requirement (no degree-related terms found)", "    ~ PARTIAL MATCH: Keyword similarity found", "  - Analyzing requirement: 'Should have consistent marks above 75% or GPA 3.0/4.0 or above in all previous semesters'", "    Required degree type: Not specified", "    Required field: Not specified", "    Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)", "    ✗ SKIPPED: 'Should have consistent marks above 75% or GPA 3.0/4.0 or above in all previous semesters' is not an education requirement (no degree-related terms found)", "    ~ PARTIAL MATCH: Keyword similarity found", "    Candidate degree: 'Intermediate' (Type: intermediate, Field: Unknown)", "    ✗ SKIPPED: 'Should have consistent marks above 75% or GPA 3.0/4.0 or above in all previous semesters' is not an education requirement (no degree-related terms found)", "    ~ PARTIAL MATCH: Keyword similarity found", "    Candidate degree: 'Matriculation' (Type: matriculation, Field: Unknown)", "    ✗ SKIPPED: 'Should have consistent marks above 75% or GPA 3.0/4.0 or above in all previous semesters' is not an education requirement (no degree-related terms found)", "    ~ PARTIAL MATCH: Keyword similarity found", "Step 2: Applying binary scoring system", "  ✗ Education requirements not met: Score = 0.0"], "_scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "_explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)"}}, "certifications": {"raw_score": 0.0, "weight": 0.5, "weighted_score": 0.0, "rationale": "No certifications listed", "details": {"total_certifications": 0, "relevant_certifications": [], "relevant_count": 0, "all_certifications": [], "_irrelevant_certifications": [], "_certification_analysis": [], "_calculation_steps": ["Step 1: Found 0 certifications in resume", "Step 2: Checking relevance against 14 job skills (11 required + 3 preferred)", "Step 3: Score calculation", "  Base score: 0 relevant certs × 2 points = 0", "  Final score: min(10, 0) = 0"], "_scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "_explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10"}}, "location": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate location match - some geographic alignment", "details": {"jd_location": "bengaluru", "resume_location": "", "experience_locations": [], "location_match_found": false, "has_location_info": false, "_calculation_steps": ["Step 1: Location extraction", "  Job location: 'bengaluru' (from JD)", "  Resume location: '' (from resume)", "  Experience locations: [] (from work history)", "Step 2: Location matching analysis", "  ~ Insufficient location data: Score = 5.0 (neutral)"], "_scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "_explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}}, "reliability": {"raw_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Moderate job stability - average 0.0 years per company", "details": {"candidate_yoe": 0, "num_companies": 1, "avg_tenure": 0.0, "has_experience_data": false, "_tenure_breakdown": [{"company": "Unknown Company", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}], "_calculation_steps": ["Step 1: Analyzing 1 experience entries for tenure calculation", "  Entry 1: Unknown Company (Present) = 0 years (could not parse)", "Step 1 Result: Total experience = 0 years", "Step 2: Calculating job stability/reliability", "  Total companies: 1", "  Total years: 0", "  ~ Insufficient data for calculation: Score = 5.0 (neutral)"], "_scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "_explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history"}}}}