#!/usr/bin/env python3
"""
Test script for enhanced features in intervet_new system:
1. Detailed rationale structure
2. GPA/grades extraction in hybrid_resume
3. Highest education qualification extraction
"""

import sys
import os
import json
import time
import requests
from pathlib import Path

# Add the parent directory to the path so we can import from main
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configuration
API_BASE_URL = "http://localhost:8000"
HYBRID_RESUME_ENDPOINT = f"{API_BASE_URL}/hybrid_resume"
JD_PARSER_ENDPOINT = f"{API_BASE_URL}/jd_parser"
INTERVET_NEW_ENDPOINT = f"{API_BASE_URL}/intervet_new"

# Test files
RESUME_FILE = "resumes for testing/Resume-Raman Luhach.pdf"
RESUME_FILE_2 = "resumes for testing/Resume-Hardik Ma<PERSON>hwari.pdf"

def test_hybrid_resume_with_grades():
    """Test hybrid_resume endpoint for GPA/grades extraction"""
    print("=" * 80)
    print("🧪 TESTING HYBRID RESUME WITH GRADES EXTRACTION")
    print("=" * 80)
    
    # Test with first resume
    print(f"\n📄 Testing with: {RESUME_FILE}")
    
    if not os.path.exists(RESUME_FILE):
        print(f"❌ Resume file not found: {RESUME_FILE}")
        return None
    
    try:
        with open(RESUME_FILE, 'rb') as f:
            files = {'file': (RESUME_FILE, f, 'application/pdf')}
            response = requests.post(HYBRID_RESUME_ENDPOINT, files=files, timeout=120)
        
        if response.status_code == 200:
            resume_data = response.json()
            print("✅ Hybrid resume parsing successful!")
            
            # Check education structure
            education = resume_data.get('education', [])
            highest_education = resume_data.get('highest_education')
            
            print(f"\n📚 Education entries found: {len(education)}")
            for i, edu in enumerate(education, 1):
                print(f"  {i}. Degree: {edu.get('degree', 'N/A')}")
                print(f"     Institution: {edu.get('institution', 'N/A')}")
                print(f"     Year: {edu.get('year', 'N/A')}")
                print(f"     Grade: {edu.get('grade', 'N/A')}")
                print()
            
            print(f"🎓 Highest Education: {highest_education}")
            
            return resume_data
        else:
            print(f"❌ Hybrid resume parsing failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing hybrid resume: {e}")
        return None

def test_hybrid_resume_with_sharath():
    """Test hybrid_resume endpoint with another resume to verify grades extraction"""
    print("=" * 80)
    print("🧪 TESTING HYBRID RESUME WITH ANOTHER RESUME")
    print("=" * 80)
    
    print(f"\n📄 Testing with: {RESUME_FILE_2}")
    
    if not os.path.exists(RESUME_FILE_2):
        print(f"❌ Resume file not found: {RESUME_FILE_2}")
        return None
    
    try:
        with open(RESUME_FILE_2, 'rb') as f:
            files = {'file': (RESUME_FILE_2, f, 'application/pdf')}
            response = requests.post(HYBRID_RESUME_ENDPOINT, files=files, timeout=120)
        
        if response.status_code == 200:
            resume_data = response.json()
            print("✅ Hybrid resume parsing successful!")
            
            # Check education structure
            education = resume_data.get('education', [])
            highest_education = resume_data.get('highest_education')
            
            print(f"\n📚 Education entries found: {len(education)}")
            for i, edu in enumerate(education, 1):
                print(f"  {i}. Degree: {edu.get('degree', 'N/A')}")
                print(f"     Institution: {edu.get('institution', 'N/A')}")
                print(f"     Year: {edu.get('year', 'N/A')}")
                print(f"     Grade: {edu.get('grade', 'N/A')}")
                print()
            
            print(f"🎓 Highest Education: {highest_education}")
            
            # Check if grades are properly extracted
            grades_found = [edu.get('grade') for edu in education if edu.get('grade')]
            print(f"📊 Grades extracted: {grades_found}")
            
            return resume_data
        else:
            print(f"❌ Hybrid resume parsing failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing hybrid resume: {e}")
        return None

def create_sample_jd():
    """Create a sample JD for testing"""
    return {
        "job_title": "Frontend Developer",
        "company": "Tech Corp",
        "location": "San Francisco, CA",
        "required_skills": ["HTML", "CSS", "Javascript", "Media queries", "SDLC"],
        "preferred_skills": ["React"],
        "education_requirements": ["Bachelor's in IT, Computer Science, Software Engineering, or a related field"],
        "experience_requirements": "1 years",
        "job_description": "We are looking for a Frontend Developer with strong skills in web technologies."
    }

def test_intervet_new_detailed_rationale(resume_data):
    """Test intervet_new endpoint for detailed rationale structure"""
    print("=" * 80)
    print("🧪 TESTING INTERVET_NEW DETAILED RATIONALE")
    print("=" * 80)
    
    if not resume_data:
        print("❌ No resume data provided for testing")
        return
    
    # Create sample JD
    jd_data = create_sample_jd()
    
    # Test intervet_new
    try:
        payload = {
            "resume_json": resume_data,
            "jd_json": jd_data
        }
        
        response = requests.post(INTERVET_NEW_ENDPOINT, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Intervet_new evaluation successful!")
            
            # Check detailed rationale structure
            detailed_rationale = result.get('detailed_rationale', {})
            
            print("\n📋 DETAILED RATIONALE STRUCTURE:")
            print("-" * 50)
            
            expected_fields = [
                'skills_match_direct',
                'experience_match', 
                'reliability',
                'location_match',
                'academic_match',
                'alma_mater',
                'certifications'
            ]
            
            for field in expected_fields:
                value = detailed_rationale.get(field, 'MISSING')
                print(f"✓ {field}: {value}")
                print()
            
            # Check if skills_match_subjective is NOT present (should be removed)
            if 'skills_match_subjective' in detailed_rationale:
                print("❌ ERROR: skills_match_subjective should not be present!")
            else:
                print("✅ Confirmed: skills_match_subjective correctly removed")
            
            print(f"\n📊 Total Score: {result.get('total_score', 'N/A')}")
            print(f"🎯 Fit Category: {result.get('fit_category', 'N/A')}")
            
            return result
        else:
            print(f"❌ Intervet_new evaluation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing intervet_new: {e}")
        return None

def test_sharath_kumar_resume():
    """Test the problematic Sharath Kumar resume specifically"""
    print("=" * 80)
    print("🧪 TESTING SHARATH KUMAR RESUME (PROBLEMATIC CASE)")
    print("=" * 80)

    sharath_file = "resumes for testing/Resume-Sharath Kumar.R.pdf"

    if not os.path.exists(sharath_file):
        print(f"❌ Sharath Kumar resume not found: {sharath_file}")
        return None

    try:
        with open(sharath_file, 'rb') as f:
            files = {'file': (sharath_file, f, 'application/pdf')}
            response = requests.post(HYBRID_RESUME_ENDPOINT, files=files, timeout=120)

        if response.status_code == 200:
            resume_data = response.json()
            print("✅ Sharath Kumar resume parsing successful!")

            # Check if we got meaningful data (not empty JSON)
            if resume_data.get('name') and resume_data.get('name') != 'Unknown':
                print(f"✅ Name extracted: {resume_data.get('name')}")
            else:
                print("❌ Name not extracted or is 'Unknown'")

            # Check education
            education = resume_data.get('education', [])
            print(f"📚 Education entries: {len(education)}")
            for i, edu in enumerate(education, 1):
                print(f"  {i}. {edu.get('degree', 'N/A')} - Grade: {edu.get('grade', 'N/A')}")

            # Check skills
            skills = resume_data.get('skills', [])
            print(f"🛠️ Skills count: {len(skills)}")

            # Check experience
            experience = resume_data.get('experience', [])
            print(f"💼 Experience entries: {len(experience)}")

            # Check for character encoding issues
            full_text = json.dumps(resume_data)
            if 'ﬁ' in full_text or 'Procient' in full_text or 'eciency' in full_text:
                print("❌ Character encoding issues still present!")
            else:
                print("✅ No character encoding issues detected")

            return resume_data
        else:
            print(f"❌ Sharath Kumar resume parsing failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Error testing Sharath Kumar resume: {e}")
        return None

def test_academic_match_fix():
    """Test the academic match rationale fix"""
    print("=" * 80)
    print("🧪 TESTING ACADEMIC MATCH RATIONALE FIX")
    print("=" * 80)

    # Create a test case that would previously cause the wrong match
    test_resume = {
        "name": "Test Candidate",
        "education": [
            {"degree": "Intermediate", "institution": "Test School", "year": "2020", "grade": "85%"}
        ],
        "skills": ["HTML", "CSS"],
        "experience": [],
        "projects": [],
        "certifications": []
    }

    test_jd = {
        "job_title": "Developer",
        "education_requirements": ["Complete understanding of application development life cycle"],
        "required_skills": ["HTML", "CSS", "JavaScript"],
        "experience_requirements": "2 years"
    }

    try:
        payload = {
            "resume_json": test_resume,
            "jd_json": test_jd
        }

        response = requests.post(INTERVET_NEW_ENDPOINT, json=payload, timeout=120)

        if response.status_code == 200:
            result = response.json()
            academic_match = result.get('detailed_rationale', {}).get('academic_match', '')

            print(f"📋 Academic match rationale: {academic_match}")

            # Check if the fix worked - should NOT match these incompatible requirements
            if "Education requirements met" in academic_match and "Intermediate" in academic_match and "Complete understanding" in academic_match:
                print("❌ Academic match issue still present - 'Intermediate' incorrectly matched with 'Complete understanding'")
                return False
            elif "Education requirements not met" in academic_match:
                print("✅ Academic match fix working correctly - incompatible requirements properly rejected")
                return True
            else:
                print("✅ Academic match fix working correctly - no false positive match")
                return True
        else:
            print(f"❌ Academic match test failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error testing academic match: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 STARTING COMPREHENSIVE DEBUGGING TEST")
    print("=" * 80)

    # Test 1: Hybrid resume with grades extraction
    print("\n📋 TEST 1: Basic hybrid resume functionality")
    resume_data = test_hybrid_resume_with_grades()

    # Test 2: Hybrid resume with another resume
    print("\n📋 TEST 2: Second resume test")
    resume_data_2 = test_hybrid_resume_with_sharath()

    # Test 3: Sharath Kumar specific test
    print("\n📋 TEST 3: Sharath Kumar problematic resume")
    sharath_data = test_sharath_kumar_resume()

    # Test 4: Academic match fix
    print("\n📋 TEST 4: Academic match rationale fix")
    academic_fix_works = test_academic_match_fix()

    # Test 5: Intervet_new detailed rationale
    print("\n📋 TEST 5: Detailed rationale structure")
    if resume_data:
        test_intervet_new_detailed_rationale(resume_data)

    # Summary
    print("\n" + "=" * 80)
    print("🏁 COMPREHENSIVE TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Basic resume parsing: {'PASS' if resume_data else 'FAIL'}")
    print(f"✅ Second resume parsing: {'PASS' if resume_data_2 else 'FAIL'}")
    print(f"✅ Sharath Kumar resume: {'PASS' if sharath_data else 'FAIL'}")
    print(f"✅ Academic match fix: {'PASS' if academic_fix_works else 'FAIL'}")
    print("=" * 80)

if __name__ == "__main__":
    main()
