=== RAW RESPONSE ===
Length: 3483
Source: Resume-<PERSON><PERSON>.pdf
Timestamp: 1751964194.1030016
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (AI ML)",
            "institution": "Newton School of Technology , Rishihood University",
            "year": "2023 - 2027",
            "grade": "9.18/10.0"
        },
        {
            "degree": "Intermediate",
            "institution": "Mother India Sr Sec School Marot, Jhajjar , Haryana",
            "year": "2021 - 2022",
            "grade": "90.0%"
        },
        {
            "degree": "Matriculation",
            "institution": "R E D Sr Sec School Chhuchhakwas ,Jhajjar , Haryana",
            "year": "2019 - 2020",
            "grade": "95.8%"
        }
    ],
    "highest_education": "Bachelor of Technology (AI ML)",
    "skills": [
        "SQL",
        "Java",
        "JavaScript",
        "CSS",
        "HTML",
        "Python",
        "React",
        "MySQL",
        "NodeJS",
        "Prisma ORM",
        "Tailwind",
        "Data Structure",
        "Communication Skills",
        "Research",
        "Decision-making",
        "Team Building",
        "Leadership"
    ],
    "experience": [
        {
            "company_name": "Google Developer Groups (GDG) Rishihood University",
            "role": "Tech Lead",
            "duration": "Present",
            "key_responsibilities": "Led a team of students in organizing tech talks and workshops, mentored junior developers, and assisted in the development and maintenance of projects."
        }
    ],
    "projects": [
        {
            "name": "Tech Talks",
            "description": "Developed \"Tech Talks\", a blogging site for tech content. Users can register, log in, create , access and comment on tech blogs . Tech Stack :React ,Node .js,HTML /CSS"
        },
        {
            "name": "Zee5 Clone",
            "description": "Created a Zee5 clone using React, HTML/CSS, and JavaScript. Users register, sign in securely, and access a library of movies with intuitive UI. Feature : Movie rendering , Authentication"
        }
    ],
    "certifications": [
        "Robotics Workshop , Newton School of Technology, Rishihood University ( Link ) January 2024: Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functional gesture-controlled wheelchair prototype."
    ],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [
        "Solved more then 400 questions on LeetCode",
        "Open source contribution inWikiMedia foundation and plone /volto",
        "1053 Rank inIEEEXtreme contest",
        "Holds highest ratings of 1592 on CodeChef ,1618 on LeetCode , and 1211 on Codeforces",
        "Attended and completed a university robotics workshop , creating a gesture-controlled wheelchair",
        "Achieved 2nd place in an inter-university kabaddi tournament"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Dedicated and enthusiastic Front End Developer with a strong aptitude for coding and a passion for creating exceptional user experiences.",
    "personal_projects": [],
    "social_media": [
        "https://www.linkedin.com/in/ramanluhach",
        "https://github.com/ramanluhach",
        "https://codechef.com/users/ramanluhach",
        "https://codeforces.com/api/user/123456789",
        "https://leetcode.com/users/ramanluhach/"
    ]
}
```