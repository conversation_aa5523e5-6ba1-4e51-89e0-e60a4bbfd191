#!/usr/bin/env python3
"""
Test script to test the individual functions used in the enhanced /intervet2 endpoint
This tests the functions directly without needing the server to be running
"""

import sys
import os
import time
from pathlib import Path

# Add the parent directory to the path so we can import from main.py
sys.path.append(str(Path(__file__).parent.parent))

# Import the functions we need to test
from main import (
    extract_text_from_file,
    extract_sections_regex,
    parse_sections_with_gemma,
    normalize_resume_data,
    parse_jd,
    calculate_candidate_job_fit_new,
    WeightageConfig
)

def test_resume_parsing_pipeline():
    """Test the resume parsing pipeline used in enhanced /intervet2"""
    print("🧪 Testing Resume Parsing Pipeline")
    print("=" * 50)
    
    # Get a test resume file
    resume_dir = Path("resumes for testing")
    if not resume_dir.exists():
        print(f"❌ Resume directory not found: {resume_dir}")
        return False
    
    resume_files = list(resume_dir.glob("*.pdf")) + list(resume_dir.glob("*.docx"))
    if not resume_files:
        print("❌ No resume files found")
        return False
    
    resume_file = resume_files[0]
    print(f"📄 Testing with: {resume_file.name}")
    
    try:
        # Step 1: Extract text
        print("📄 Step 1: Extracting text...")
        start_time = time.time()
        file_type = "pdf" if resume_file.suffix == ".pdf" else "docx"
        extracted_text = extract_text_from_file(
            str(resume_file),
            file_type,
            source_filename=resume_file.name,
            context="test"
        )
        print(f"✅ Text extraction completed in {time.time() - start_time:.2f}s")
        print(f"   Extracted {len(extracted_text)} characters")
        
        # Step 2: Extract sections using regex
        print("🔍 Step 2: Extracting sections using regex...")
        start_time = time.time()
        sections, confidence_scores = extract_sections_regex(extracted_text, resume_file.name, "")
        print(f"✅ Section extraction completed in {time.time() - start_time:.2f}s")
        print(f"   Found {len(sections)} sections")
        sections_with_content = [s for s in sections.values() if s and s.strip()]
        print(f"   Sections with content: {len(sections_with_content)}")
        
        # Step 3: Parse sections with LLM
        print("🤖 Step 3: Parsing sections with LLM...")
        start_time = time.time()
        structured_data = parse_sections_with_gemma(sections, resume_file.name)
        print(f"✅ LLM parsing completed in {time.time() - start_time:.2f}s")
        
        # Check if we got an error
        if structured_data.get("error"):
            print(f"⚠️  LLM parsing returned error: {structured_data.get('error')}")
        else:
            print(f"   Parsed data keys: {list(structured_data.keys())}")
        
        # Step 4: Normalize data
        print("📊 Step 4: Normalizing data...")
        start_time = time.time()
        normalized_data = normalize_resume_data(structured_data, convert_skills_to_dict_format=True)
        print(f"✅ Data normalization completed in {time.time() - start_time:.2f}s")
        
        # Display results
        print(f"\n📋 Resume Parsing Results:")
        print(f"   Name: {normalized_data.get('name', 'Unknown')}")
        print(f"   Email: {normalized_data.get('email', 'Not found')}")
        print(f"   Skills: {len(normalized_data.get('skills', []))}")
        print(f"   Experience: {len(normalized_data.get('experience', []))}")
        print(f"   Education: {len(normalized_data.get('education', []))}")
        
        return normalized_data
        
    except Exception as e:
        print(f"❌ Resume parsing pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_jd_parsing_pipeline():
    """Test the JD parsing pipeline used in enhanced /intervet2"""
    print("\n🧪 Testing JD Parsing Pipeline")
    print("=" * 50)
    
    # Get a test JD file
    jd_dir = Path("jds for testing")
    if not jd_dir.exists():
        print(f"❌ JD directory not found: {jd_dir}")
        return False
    
    jd_files = list(jd_dir.glob("*.pdf")) + list(jd_dir.glob("*.docx"))
    if not jd_files:
        print("❌ No JD files found")
        return False
    
    jd_file = jd_files[0]
    print(f"📄 Testing with: {jd_file.name}")
    
    try:
        # Step 1: Extract text
        print("📄 Step 1: Extracting text...")
        start_time = time.time()
        file_type = "pdf" if jd_file.suffix == ".pdf" else "docx"
        extracted_text = extract_text_from_file(
            str(jd_file),
            file_type,
            source_filename=jd_file.name,
            context="test"
        )
        print(f"✅ Text extraction completed in {time.time() - start_time:.2f}s")
        print(f"   Extracted {len(extracted_text)} characters")
        
        # Step 2: Parse JD
        print("🤖 Step 2: Parsing JD with LLM...")
        start_time = time.time()
        jd_data = parse_jd(extracted_text, source_filename=jd_file.name)
        print(f"✅ JD parsing completed in {time.time() - start_time:.2f}s")
        
        # Display results
        print(f"\n📋 JD Parsing Results:")
        print(f"   Job Title: {jd_data.get('job_title', 'Unknown')}")
        print(f"   Company: {jd_data.get('company_name', 'Not specified')}")
        print(f"   Required skills: {len(jd_data.get('required_skills', []))}")
        print(f"   Preferred skills: {len(jd_data.get('preferred_skills', []))}")
        print(f"   Required experience: {jd_data.get('required_experience', 'Not specified')}")
        
        return jd_data
        
    except Exception as e:
        print(f"❌ JD parsing pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_evaluation_pipeline(resume_data, jd_data):
    """Test the evaluation pipeline used in enhanced /intervet2"""
    print("\n🧪 Testing Evaluation Pipeline")
    print("=" * 50)
    
    if not resume_data or not jd_data:
        print("❌ Cannot test evaluation without resume and JD data")
        return False
    
    try:
        # Test with default weightage
        print("🎯 Testing with default weightage...")
        start_time = time.time()
        result = calculate_candidate_job_fit_new(resume_data, jd_data)
        print(f"✅ Evaluation completed in {time.time() - start_time:.2f}s")
        
        print(f"\n📊 Evaluation Results:")
        print(f"   Final Score: {result.total_score:.2f}/10")
        print(f"   Fit Category: {result.fit_category}")
        print(f"   Summary: {result.summary}")

        # Test with custom weightage
        print("\n🎯 Testing with custom weightage...")
        custom_weightage = WeightageConfig(
            skills=4.0,
            experience=3.0,
            education=2.0,
            certifications=1.0,
            location=0.5,
            reliability=0.5
        )

        start_time = time.time()
        result_custom = calculate_candidate_job_fit_new(resume_data, jd_data, custom_weightage)
        print(f"✅ Custom evaluation completed in {time.time() - start_time:.2f}s")

        print(f"\n📊 Custom Evaluation Results:")
        print(f"   Final Score: {result_custom.total_score:.2f}/10")
        print(f"   Fit Category: {result_custom.fit_category}")
        print(f"   Summary: {result_custom.summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ Evaluation pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Testing Enhanced /intervet2 Functions")
    print("=" * 70)
    
    # Test resume parsing
    resume_data = test_resume_parsing_pipeline()
    
    # Test JD parsing
    jd_data = test_jd_parsing_pipeline()
    
    # Test evaluation
    evaluation_success = test_evaluation_pipeline(resume_data, jd_data)
    
    print("\n" + "=" * 70)
    print("📊 Test Summary:")
    print(f"   Resume parsing: {'✅ PASSED' if resume_data else '❌ FAILED'}")
    print(f"   JD parsing: {'✅ PASSED' if jd_data else '❌ FAILED'}")
    print(f"   Evaluation: {'✅ PASSED' if evaluation_success else '❌ FAILED'}")
    
    if resume_data and jd_data and evaluation_success:
        print("\n🎉 All function tests passed! The enhanced /intervet2 logic is working correctly.")
        return True
    else:
        print("\n❌ Some function tests failed. Check the logs above for details.")
        return False

if __name__ == "__main__":
    main()
