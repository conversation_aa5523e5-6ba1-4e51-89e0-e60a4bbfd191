{"total_score": 6.04, "fit_category": "Good Match", "summary": "The candidate is a good match for this position with a CGPA-style score of 6.0/10. Key strengths: Education. Areas for improvement: Certifications.", "skills_score": {"raw_score": 6.8, "weight": 3.0, "weighted_score": 20.4, "rationale": "Good skills match - has 3/5 required skills, some gaps in preferred skills"}, "experience_score": {"raw_score": 5.0, "weight": 2.5, "weighted_score": 12.5, "rationale": "Moderate experience level - 0 years, some experience gaps"}, "education_score": {"raw_score": 10.0, "weight": 2.0, "weighted_score": 20.0, "rationale": "Perfect education match - meets all degree requirements"}, "certifications_score": {"raw_score": 0.0, "weight": 1.0, "weighted_score": 0.0, "rationale": "No relevant certifications - 1 certifications but none match job requirements"}, "location_score": {"raw_score": 5.0, "weight": 1.0, "weighted_score": 5.0, "rationale": "Moderate location match - some geographic alignment"}, "reliability_score": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate job stability - average 0.0 years per company"}, "detailed_rationale": {"skills_match_direct": "Matched 3/5 required skills and 1/1 preferred skills. Matched required skills: HTML, CSS, Javascript. Missing required skills: Media queries, SDLC", "experience_match": "No specific experience requirement found in job description", "reliability": "Poor stability: frequent job changes with 0.0 years per company", "location_match": "No location match found with job location (san francisco, ca)", "academic_match": "Education requirements met: 'Bachelor of Technology (AI ML)' matches requirement 'Bachelor's in IT, Computer Science, Software Engineering, or a related field'", "alma_mater": "No top-ranked universities found in education history", "certifications": "No relevant certifications found"}, "total_credits_used": 10.0, "calculation_method": "CGPA", "processing_time": 0.0029997825622558594}