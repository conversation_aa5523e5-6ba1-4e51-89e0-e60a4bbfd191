# Resume Parser GUI

A lightweight, feature-rich tkinter application for parsing resumes using the Gemma API.

## Features

### 🚀 **Core Features**
- **Easy File Upload**: Drag & drop or browse for PDF, DOCX, DOC, and TXT files
- **Real-time Processing**: Live progress indication during API calls
- **Instant Results**: Formatted JSON display with syntax highlighting
- **One-Click Copy**: Copy results to clipboard for easy pasting into Excel
- **Export Options**: Save as JSON, CSV, or Excel formats

### 💡 **Innovative Features**
- **Batch Processing**: Process multiple resumes simultaneously
- **Resume Comparison**: Side-by-side comparison of two resumes
- **Skills Analysis**: Detailed breakdown and visualization of extracted skills
- **Auto-Save Settings**: Remembers your API URL and preferences
- **Connection Testing**: Verify API connectivity before processing

### 📊 **Export Capabilities**
- **JSON Export**: Raw structured data
- **CSV Export**: Flattened data for spreadsheet import
- **Excel Export**: Professional formatted spreadsheets
- **Batch Export**: Export multiple resume results at once

## Installation

### 1. Install Dependencies
```bash
# Install required packages
pip install -r gui_requirements.txt

# Or install manually
pip install requests pyperclip

# Optional (for enhanced features)
pip install pandas openpyxl
```

### 2. Install tkinter (if not available)
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# macOS (with Homebrew)
brew install python-tk

# Windows: tkinter is included with Python
```

## Usage

### Quick Start
```bash
# Check dependencies and launch
python launch_gui.py

# Or launch directly
python resume_parser_gui.py
```

### Configuration
1. **Set API URL**: Enter your Gemma API endpoint (default: http://localhost:8000)
2. **Test Connection**: Click "Test Connection" to verify API availability
3. **Configure Options**: 
   - Enable/disable skills dictionary format
   - Enable/disable auto-copy to clipboard

### Basic Workflow
1. **Select File**: Click "Browse" or drag & drop a resume file
2. **Configure Options**: Set your preferences
3. **Parse Resume**: Click "Parse Resume" and wait for processing
4. **View Results**: Review the formatted JSON output
5. **Copy/Export**: Use the action buttons to copy or export results

## Advanced Features

### 🔄 **Batch Processing**
Process multiple resumes at once:
1. Click "Batch Process"
2. Select multiple resume files
3. Monitor progress in real-time
4. Export all results to Excel or JSON

### ⚖️ **Resume Comparison**
Compare two resumes side-by-side:
1. Click "Compare Resumes"
2. Select exactly 2 resume files
3. View parsed results side-by-side
4. Identify differences and similarities

### 📈 **Skills Analysis**
Analyze extracted skills:
1. Process a resume first
2. Click "Skills Analysis"
3. View skills in different formats:
   - **List View**: All skills in a scrollable list
   - **Statistics**: Detailed breakdown and counts
4. Export skills data separately

## API Configuration

### Local Development
```
API URL: http://localhost:8000
```

### Cloud Deployment
```
API URL: http://your-ec2-public-dns
# or
API URL: http://your-domain.com
```

### Custom Port
```
API URL: http://localhost:8080
```

## File Formats Supported

| Format | Extension | Notes |
|--------|-----------|-------|
| PDF | .pdf | Most common format |
| Word | .docx, .doc | Microsoft Word documents |
| Text | .txt | Plain text resumes |

## Export Formats

### JSON Export
- Raw structured data
- Perfect for API integration
- Preserves all data types

### CSV Export
- Flattened key-value pairs
- Easy Excel import
- Good for data analysis

### Excel Export
- Professional formatting
- Multiple sheets for complex data
- Requires pandas and openpyxl

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| Ctrl+O | Browse for file |
| Ctrl+Enter | Parse resume |
| Ctrl+C | Copy all results |
| Ctrl+S | Save results |
| F5 | Test connection |

## Troubleshooting

### Common Issues

**1. "Cannot connect to API"**
- Check if the API server is running
- Verify the API URL is correct
- Test connection using the "Test Connection" button

**2. "tkinter not found"**
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# macOS
brew install python-tk
```

**3. "Module not found" errors**
```bash
pip install -r gui_requirements.txt
```

**4. "Excel export failed"**
```bash
pip install pandas openpyxl
```

**5. Slow processing**
- Large files take longer to process
- Check your internet connection
- Consider using a smaller model on the API

### Performance Tips

1. **Use Local API**: Faster than cloud deployment
2. **Batch Processing**: More efficient for multiple files
3. **Close Other Applications**: Free up system resources
4. **Check File Size**: Smaller files process faster

## Configuration File

The app automatically saves settings to `resume_parser_config.json`:

```json
{
  "api_url": "http://localhost:8000",
  "skills_dict_format": true,
  "auto_copy": true
}
```

## Integration with Excel

### Method 1: Auto-Copy
1. Enable "Auto-copy result to clipboard"
2. Process resume
3. Switch to Excel
4. Paste (Ctrl+V)

### Method 2: Direct Export
1. Process resume
2. Click "Export Excel"
3. Open the exported file

### Method 3: CSV Import
1. Export as CSV
2. Open Excel
3. Import CSV file with proper formatting

## API Endpoints Used

| Endpoint | Purpose | Method |
|----------|---------|--------|
| `/` | Connection test | GET |
| `/resume` | Resume parsing | POST |

## Security Notes

- API credentials are not stored
- Files are only sent to your specified API
- No data is stored permanently by the GUI
- All processing happens on your API server

## Contributing

Feel free to enhance the GUI with additional features:
- Dark mode theme
- More export formats
- Advanced filtering
- Resume scoring
- Template generation

## License

This GUI application is provided as-is for use with the Gemma API.
