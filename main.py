import json
import os
import re
import tempfile
import logging
import time
from datetime import datetime
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Request, Response, Path, Query
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import BaseModel, Field  # <-- Required to define request schema
import PyPDF2
import docx  # For DOCX file processing
from typing import Dict, List, Optional, Literal, Any, Callable, Tuple, Set

# Import metrics logging
from metrics_logger import RequestMetrics, log_model_metrics, log_file_processing_metrics, shutdown_metrics_logger

# Import prompt logging
from prompt_logger import log_llm_call, cleanup_old_logs, get_log_stats

# Configure logging with a more user-friendly format
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler()
    ]
)

# Create a custom filter to simplify some log messages
class UserFriendlyFilter(logging.Filter):
    def filter(self, record):
        # Simplify common log messages
        if "Sending prompt to" in record.msg:
            record.msg = "🤖 Processing with Gemma 3:4b model..."
        elif "Received response from" in record.msg:
            record.msg = "✅ Received model response in {:.2f}s".format(
                float(record.msg.split("in ")[1].split("s")[0])
            )
        elif "Extracting text from PDF" in record.msg or "Extracting text from DOCX" in record.msg:
            record.msg = "📄 Extracting text from document..."
        elif "Successfully extracted" in record.msg and "characters from" in record.msg:
            record.msg = "✅ Successfully extracted document text"
        elif "Parsing resume with" in record.msg:
            record.msg = "🔍 Analyzing resume content..."
        elif "Parsing job description with" in record.msg:
            record.msg = "🔍 Analyzing job description content..."
        elif "Starting resume parsing" in record.msg:
            record.msg = "🚀 Starting resume analysis..."
        elif "Starting job description parsing" in record.msg:
            record.msg = "🚀 Starting job description analysis..."
        elif "Normalizing" in record.msg:
            record.msg = "📊 Organizing extracted data..."

        return True

# Get logger and add the filter
logger = logging.getLogger("gemma-api")
logger.addFilter(UserFriendlyFilter())

# Fix SSL certificate issue for ollama client
os.environ.pop('SSL_CERT_FILE', None)

# Now import ollama after fixing the SSL issue
import ollama

# Change this to any Ollama model you have downloaded
# Examples: "llama3.1:8b", "mistral:7b", "qwen2.5:7b", "gemma3:4b"
MODEL_NAME = "gemma3:4b"

# JSON Self-Healing Configuration
ENABLE_LLM_JSON_REPAIR = True  # Set to False to disable LLM self-healing
JSON_REPAIR_TIMEOUT = 30  # Timeout for JSON repair requests (seconds)
JSON_REPAIR_MAX_TOKENS = 2000  # Max tokens for JSON repair

# Define detailed resume schema models
class EducationEntry(BaseModel):
    degree: str
    institution: str
    year: str
    gpa: Optional[str] = None
    location: Optional[str] = None
    major: Optional[str] = None
    minor: Optional[str] = None
    achievements: Optional[List[str]] = None
    courses: Optional[List[str]] = None

class ExperienceEntry(BaseModel):
    company_name: str
    role: str
    duration: str
    key_responsibilities: str
    location: Optional[str] = None
    achievements: Optional[List[str]] = None
    technologies_used: Optional[List[str]] = None
    team_size: Optional[str] = None
    industry: Optional[str] = None

class ProjectEntry(BaseModel):
    name: str
    description: Optional[str] = None
    duration: Optional[str] = None
    technologies_used: Optional[List[str]] = None
    url: Optional[str] = None
    role: Optional[str] = None
    achievements: Optional[List[str]] = None

class CertificationEntry(BaseModel):
    name: str
    issuer: Optional[str] = None
    date: Optional[str] = None
    expiry: Optional[str] = None
    url: Optional[str] = None

class SocialMediaEntry(BaseModel):
    platform: str
    url: str
    username: Optional[str] = None

class LanguageEntry(BaseModel):
    name: str
    proficiency: Optional[str] = None

class PublicationEntry(BaseModel):
    title: str
    publisher: Optional[str] = None
    date: Optional[str] = None
    url: Optional[str] = None
    authors: Optional[List[str]] = None
    description: Optional[str] = None

class AchievementEntry(BaseModel):
    title: str
    date: Optional[str] = None
    issuer: Optional[str] = None
    description: Optional[str] = None

class VolunteerEntry(BaseModel):
    organization: str
    role: str
    duration: Optional[str] = None
    description: Optional[str] = None

class ResumeResponse(BaseModel):
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    summary: Optional[str] = None
    location: Optional[str] = None
    education: List[EducationEntry] = []
    skills: Dict[str, str] = {}  # Skill name -> Context where it was mentioned
    experience: List[ExperienceEntry] = []
    projects: List[ProjectEntry] = []
    certifications: List[CertificationEntry] = []
    languages: List[LanguageEntry] = []
    social_media: List[SocialMediaEntry] = []
    publications: List[PublicationEntry] = []
    achievements: List[AchievementEntry] = []
    volunteer_experience: List[VolunteerEntry] = []
    domain_of_interest: List[str] = []
    references: List[Dict[str, str]] = []
    confidence_score: float = 0.0  # Overall confidence in the parsing
    confidence_details: Dict[str, float] = {}  # Detailed breakdown of confidence scores by field

class BenefitEntry(BaseModel):
    title: str
    description: Optional[str] = None

class RequirementEntry(BaseModel):
    title: str
    description: Optional[str] = None
    is_mandatory: Optional[bool] = True

class JobDescriptionResponse(BaseModel):
    job_title: str
    company_name: Optional[str] = None
    location: Optional[str] = None
    job_type: Optional[str] = None  # Full-time, Part-time, Contract, etc.
    work_mode: Optional[str] = None  # Remote, Hybrid, On-site
    department: Optional[str] = None
    summary: Optional[str] = None
    responsibilities: List[str] = []
    required_skills: List[str] = []
    preferred_skills: List[str] = []
    required_experience: Optional[str] = None  # e.g., "3+ years"
    education_requirements: List[str] = []
    salary_range: Optional[str] = None
    benefits: List[BenefitEntry] = []
    requirements: List[RequirementEntry] = []
    application_deadline: Optional[str] = None
    posting_date: Optional[str] = None
    contact_information: Optional[Dict[str, str]] = None
    company_description: Optional[str] = None
    industry: Optional[str] = None
    career_level: Optional[str] = None  # Entry, Mid, Senior
    confidence_score: float = 0.0  # Overall confidence in the parsing

    # Additional fields
    class Config:
        extra = "allow"  # Allow additional fields beyond the predefined schema
        schema_extra = {
            "example": {
                "name": "John Doe",
                "email": "<EMAIL>",
                "phone": "************",
                "summary": "Experienced software engineer with 5+ years in machine learning and AI.",
                "location": "San Francisco, CA",
                "education": [
                    {
                        "degree": "B.Tech Computer Science",
                        "institution": "Example University",
                        "year": "2018-2022",
                        "gpa": "3.8/4.0",
                        "location": "San Francisco, CA",
                        "major": "Computer Science",
                        "courses": ["Machine Learning", "Data Structures", "Algorithms"]
                    }
                ],
                "skills": {
                    "Python": "Developed multiple ML models using Python and scikit-learn",
                    "Machine Learning": "Implemented recommendation systems using collaborative filtering",
                    "NLP": "Built text classification models for sentiment analysis"
                },
                "experience": [
                    {
                        "company_name": "Example Corp",
                        "role": "ML Engineer",
                        "duration": "2022-Present",
                        "key_responsibilities": "Developed ML models for recommendation systems",
                        "location": "San Francisco, CA",
                        "technologies_used": ["Python", "TensorFlow", "PyTorch"]
                    }
                ],
                "projects": [
                    {
                        "name": "Resume Parser",
                        "description": "Built an AI-powered resume parser using NLP techniques",
                        "technologies_used": ["Python", "spaCy", "FastAPI"]
                    }
                ],
                "certifications": [
                    {
                        "name": "AWS Certified ML Specialist",
                        "issuer": "Amazon Web Services",
                        "date": "2023"
                    }
                ],
                "languages": [
                    {"name": "English", "proficiency": "Native"},
                    {"name": "Spanish", "proficiency": "Intermediate"}
                ],
                "social_media": [
                    {"platform": "LinkedIn", "url": "https://linkedin.com/in/johndoe"},
                    {"platform": "GitHub", "url": "https://github.com/johndoe"}
                ],
                "domain_of_interest": ["AI", "ML", "NLP"],
                "confidence_score": 0.92
            }
        }

app = FastAPI(
    title="Gemma 3:4B API",
    description="API for interacting with Gemma 3:4B model and parsing resumes",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create a metrics middleware
class MetricsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Extract the endpoint path
        path = request.url.path

        # Create a metrics tracker for this request
        metrics = RequestMetrics(endpoint=path)

        # Store metrics in request state for access in route handlers
        request.state.metrics = metrics

        try:
            # Call the next middleware or route handler
            response = await call_next(request)

            # Mark request as complete with success status
            metrics.mark_complete(status_code=response.status_code)

            return response

        except Exception as e:
            # Mark request as complete with error
            metrics.mark_complete(status_code=500, error=str(e))
            raise

# Add the metrics middleware
app.add_middleware(MetricsMiddleware)

# Register lifespan context for startup/shutdown events
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(_: FastAPI):  # Use _ to indicate unused variable
    # Startup: nothing to do
    yield
    # Shutdown: close metrics logger
    shutdown_metrics_logger()

# Update app with lifespan
app.router.lifespan_context = lifespan

class GenerateRequest(BaseModel):
    prompt: str
    history: str = ""  # optional default value

class JDQuestionRequest(BaseModel):
    resume_json: Dict[str, Any] = Field(..., description="Resume data in JSON format, typically obtained from the /resume endpoint")
    technical_questions: int = Field(..., ge=0, le=10, description="Scale 0-10 for technical questions")
    past_experience_questions: int = Field(..., ge=0, le=10, description="Scale 0-10 for past experience questions")
    case_study_questions: int = Field(..., ge=0, le=10, description="Scale 0-10 for case study questions")
    situation_handling_questions: int = Field(..., ge=0, le=10, description="Scale 0-10 for situation handling questions")
    personality_test_questions: int = Field(..., ge=0, le=10, description="Scale 0-10 for personality test questions")

class IntervetRequest(BaseModel):
    resume_json: Dict[str, Any] = Field(..., description="Resume data in JSON format, typically obtained from the /resume endpoint")
    jd_json: Dict[str, Any] = Field(..., description="Job description data in JSON format, typically obtained from the /jd_parser endpoint")

    class Config:
        schema_extra = {
            "example": {
                "resume_json": {
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "phone": "+1234567890",
                    "education": [
                        {"degree": "B.Tech Computer Science", "institution": "Example University", "year": "2018-2022"}
                    ],
                    "skills": ["Python", "Machine Learning", "FastAPI", "SQL"],
                    "experience": [
                        {"company_name": "Tech Corp", "role": "Software Engineer", "duration": "2022-Present",
                         "key_responsibilities": "Developed APIs and ML models"}
                    ],
                    "projects": ["Resume Parser", "Recommendation System"],
                    "domain_of_interest": ["AI", "Web Development"]
                },
                "jd_json": {
                    "job_title": "Full Stack Developer",
                    "required_skills": ["Python", "JavaScript", "SQL"],
                    "required_experience": "2+ years",
                    "education_requirements": ["Bachelor's degree in Computer Science or related field"]
                }
            }
        }

class InterfixResponse(BaseModel):
    offer_in_hand: Optional[float] = None
    notice_period: Optional[str] = None
    expected_salary: Optional[float] = None
    reason_to_switch: Optional[str] = None
    preferred_time_for_interview: Optional[str] = None
    preferred_date_for_interview: Optional[str] = None

class InterfixRequest(BaseModel):
    summary: str = Field(..., description="Summary or transcript of VAPI response (AI call agent)")

    class Config:
        schema_extra = {
            "example": {
                "summary": "The call was an automated HR screening for an AI-powered full stack developer position. The candidate indicated they have a 2-month notice period, expect a salary of 1 lakh rupees monthly, and are seeking to change jobs primarily to relocate to the company's location. They expressed flexibility for both in-office and remote work arrangements."
            }
        }

# New models for /intervet_new endpoint
class WeightageConfig(BaseModel):
    skills: float = Field(default=3.0, ge=0.0, le=10.0, description="Credit weight for skills matching (0-10)")
    experience: float = Field(default=2.5, ge=0.0, le=10.0, description="Credit weight for experience matching (0-10)")
    education: float = Field(default=2.0, ge=0.0, le=10.0, description="Credit weight for education matching (0-10)")
    certifications: float = Field(default=1.0, ge=0.0, le=10.0, description="Credit weight for certifications (0-10)")
    location: float = Field(default=1.0, ge=0.0, le=10.0, description="Credit weight for location matching (0-10)")
    reliability: float = Field(default=0.5, ge=0.0, le=10.0, description="Credit weight for job stability/reliability (0-10)")

    class Config:
        json_schema_extra = {
            "example": {
                "skills": 3.0,
                "experience": 2.5,
                "education": 2.0,
                "certifications": 1.0,
                "location": 1.0,
                "reliability": 0.5
            }
        }

class IntervetNewRequest(BaseModel):
    resume_json: Dict[str, Any] = Field(..., description="Resume data in JSON format, typically obtained from the /resume endpoint")
    jd_json: Dict[str, Any] = Field(..., description="Job description data in JSON format, typically obtained from the /jd_parser endpoint")
    weightage: Optional[WeightageConfig] = Field(default=None, description="Optional weightage configuration for scoring. If not provided, default equal weights will be used.")

    class Config:
        json_schema_extra = {
            "example": {
                "resume_json": {
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "phone": "+1234567890",
                    "education": [
                        {"degree": "B.Tech Computer Science", "institution": "Example University", "year": "2018-2022"}
                    ],
                    "skills": ["Python", "Machine Learning", "FastAPI", "SQL"],
                    "experience": [
                        {"company_name": "Tech Corp", "role": "Software Engineer", "duration": "2022-Present",
                         "key_responsibilities": "Developed APIs and ML models"}
                    ],
                    "projects": ["Resume Parser", "Recommendation System"],
                    "domain_of_interest": ["AI", "Web Development"]
                },
                "jd_json": {
                    "job_title": "Full Stack Developer",
                    "required_skills": ["Python", "JavaScript", "SQL"],
                    "required_experience": "2+ years",
                    "education_requirements": ["Bachelor's degree in Computer Science or related field"]
                },
                "weightage": {
                    "skills": 4.0,
                    "experience": 3.0,
                    "education": 2.0,
                    "certifications": 0.5,
                    "location": 0.5,
                    "reliability": 0.0
                }
            }
        }

class FieldScore(BaseModel):
    raw_score: float = Field(..., description="Raw score for this field (0-10)")
    weight: float = Field(..., description="Credit weight assigned to this field")
    weighted_score: float = Field(..., description="Weight * raw_score")
    rationale: str = Field(..., description="User-friendly explanation of the score for HR/hiring managers")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional calculation details (for backend logging only)")

class FieldScoreAPI(BaseModel):
    """Clean field score model for API responses (excludes calculation details)"""
    raw_score: float = Field(..., description="Raw score for this field (0-10)")
    weight: float = Field(..., description="Credit weight assigned to this field")
    weighted_score: float = Field(..., description="Weight * raw_score")
    rationale: str = Field(..., description="User-friendly explanation of the score for HR/hiring managers")

class DetailedRationale(BaseModel):
    """Detailed rationale structure for comprehensive candidate evaluation"""
    skills_match_direct: str = Field(..., description="Detailed explanation of skills matching (combines both required and preferred skills)")
    experience_match: str = Field(..., description="Detailed explanation of experience level matching")
    reliability: str = Field(..., description="Detailed explanation of job stability/reliability assessment")
    location_match: str = Field(..., description="Detailed explanation of location matching")
    academic_match: str = Field(..., description="Detailed explanation of education requirements matching")
    alma_mater: str = Field(..., description="Detailed explanation of alma mater/university ranking assessment")
    certifications: str = Field(..., description="Detailed explanation of certifications assessment")

class IntervetNewResponse(BaseModel):
    total_score: float = Field(..., description="Final CGPA-style score out of 10")
    fit_category: str = Field(..., description="Overall fit category (Excellent/Strong/Good/Moderate/Weak Match)")
    summary: str = Field(..., description="Summary of the candidate-job fit evaluation")

    # Individual field scores (clean API versions without calculation details)
    skills_score: FieldScoreAPI = Field(..., description="Skills matching score details")
    experience_score: FieldScoreAPI = Field(..., description="Experience matching score details")
    education_score: FieldScoreAPI = Field(..., description="Education matching score details")
    certifications_score: FieldScoreAPI = Field(..., description="Certifications score details")
    location_score: FieldScoreAPI = Field(..., description="Location matching score details")
    reliability_score: FieldScoreAPI = Field(..., description="Job stability/reliability score details")

    # Detailed rationale structure
    detailed_rationale: DetailedRationale = Field(..., description="Comprehensive detailed rationale for all evaluation criteria")

    # Calculation metadata
    total_credits_used: float = Field(..., description="Total credits/weights used in calculation")
    calculation_method: str = Field(default="CGPA", description="Scoring method used")
    processing_time: float = Field(..., description="Time taken to process the request in seconds")

    class Config:
        json_schema_extra = {
            "example": {
                "total_score": 7.8,
                "fit_category": "Strong Match",
                "summary": "The candidate is a strong match for this position with a CGPA-style score of 7.8/10. Key strengths: Skills, Experience. Areas for improvement: Location.",
                "skills_score": {
                    "raw_score": 8.5,
                    "weight": 3.0,
                    "weighted_score": 25.5,
                    "rationale": "Excellent skills match - has 8/10 required skills and strong technical foundation",
                    "details": {"matched_skills": ["Python", "JavaScript", "SQL"], "missing_skills": ["React", "Node.js"]}
                },
                "total_credits_used": 10.0,
                "calculation_method": "CGPA",
                "processing_time": 1.23
            }
        }

# New response model for the enhanced /intervet2 endpoint
class Intervet2Response(BaseModel):
    # Parsed data from individual endpoints
    resume_data: Dict[str, Any] = Field(..., description="Parsed resume data from hybrid_resume endpoint")
    jd_data: Dict[str, Any] = Field(..., description="Parsed job description data from jd_parser endpoint")

    # Final evaluation result from intervet_new
    evaluation_result: IntervetNewResponse = Field(..., description="CGPA-style evaluation result from intervet_new endpoint")

    # Processing metadata
    processing_times: Dict[str, float] = Field(..., description="Processing times for each step")
    total_processing_time: float = Field(..., description="Total time taken for the entire process")

    class Config:
        json_schema_extra = {
            "example": {
                "resume_data": {
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "skills": {"Python": "Used in multiple projects", "JavaScript": "Frontend development"},
                    "experience": [{"company_name": "Tech Corp", "role": "Developer", "duration": "2022-Present"}]
                },
                "jd_data": {
                    "job_title": "Full Stack Developer",
                    "required_skills": ["Python", "JavaScript", "SQL"],
                    "required_experience": "2+ years"
                },
                "evaluation_result": {
                    "total_score": 7.8,
                    "fit_category": "Strong Match",
                    "summary": "Strong candidate match"
                },
                "processing_times": {
                    "resume_parsing": 2.5,
                    "jd_parsing": 1.8,
                    "evaluation": 1.2
                },
                "total_processing_time": 5.5
            }
        }

class SectionExtractionResponse(BaseModel):
    filename: str
    extraction_method: str  # "multiple_calls" or "single_call"
    sections_extracted: Dict[str, str] = {}  # Section name -> extracted content
    extraction_stats: Dict[str, Any] = {}  # Statistics about the extraction
    confidence_scores: Dict[str, float] = {}  # Confidence per section
    overall_confidence: float = 0.0
    processing_time: float = 0.0
    errors: List[str] = []

def get_response(prompt: str, timeout_seconds: int = 60, max_tokens: int = 1000, image_path: str = None, request_metrics: RequestMetrics = None, endpoint: str = "unknown", context: str = None, call_type: str = "main") -> str:
    try:
        logger.info(f"Sending prompt to {MODEL_NAME} with {timeout_seconds}s timeout")

        # Initialize response
        response = ""

        # Set up the stream with timeout
        from concurrent.futures import ThreadPoolExecutor

        start_time = time.time()

        # Create a function to process the stream with timeout tracking
        def process_stream():
            nonlocal response
            try:
                # Prepare options
                options = {"num_predict": max_tokens}

                # If image path is provided, use it in the prompt
                if image_path and os.path.exists(image_path):
                    logger.info(f"Including image from path: {image_path}")

                    # For Gemma 3, we can use the format that includes image paths directly
                    # The format may vary depending on the Ollama version and model
                    try:
                        # First attempt: Use the images parameter (newer Ollama versions)
                        result = ollama.generate(
                            model=MODEL_NAME,
                            prompt=prompt,
                            images=[image_path],
                            stream=False,
                            options=options
                        )
                        return result
                    except Exception as img_error:
                        logger.warning(f"Error using images parameter: {img_error}. Trying alternative method...")

                        # Second attempt: Use a special format in the prompt (older Ollama versions)
                        # This is a fallback method that might work with some Ollama configurations
                        with open(image_path, 'rb') as img_file:
                            import base64
                            img_data = base64.b64encode(img_file.read()).decode('utf-8')

                            # Create a special prompt format that includes the image data
                            # This format might work with some Ollama configurations
                            special_prompt = f"[img]{img_data}[/img]\n{prompt}"

                            result = ollama.generate(
                                model=MODEL_NAME,
                                prompt=special_prompt,
                                stream=False,
                                options=options
                            )
                            return result
                else:
                    # Use non-streaming mode with text-only prompt
                    result = ollama.generate(
                        model=MODEL_NAME,
                        prompt=prompt,
                        stream=False,
                        options=options
                    )
                    return result
            except Exception as e:
                logger.error(f"Error in model generation: {e}")
                raise

        # Use ThreadPoolExecutor to run with a strict timeout
        with ThreadPoolExecutor() as executor:
            future = executor.submit(process_stream)
            try:
                result = future.result(timeout=timeout_seconds)
                processing_time = time.time() - start_time
                logger.info(f"Received response from {MODEL_NAME} in {processing_time:.2f}s")

                # Extract the response text
                response_text = result["response"]

                # Log the LLM call for debugging
                try:
                    metadata = {
                        "timeout_seconds": timeout_seconds,
                        "max_tokens": max_tokens,
                        "processing_time": processing_time,
                        "has_image": image_path is not None,
                        "prompt_length": len(prompt),
                        "response_length": len(response_text)
                    }

                    # Add token usage if available
                    if "eval_count" in result:
                        metadata["eval_count"] = result["eval_count"]
                    if "prompt_eval_count" in result:
                        metadata["prompt_eval_count"] = result["prompt_eval_count"]
                    if "total_duration" in result:
                        metadata["model_total_duration"] = result["total_duration"]

                    log_llm_call(
                        prompt=prompt,
                        response=response_text,
                        model_name=MODEL_NAME,
                        endpoint=endpoint,
                        context=context,
                        call_type=call_type,
                        metadata=metadata
                    )
                except Exception as log_error:
                    logger.warning(f"Failed to log LLM call: {log_error}")

                # Log metrics if a request_metrics object was provided
                if request_metrics:
                    # Mark first byte received
                    request_metrics.mark_first_byte()

                    # Log model metrics
                    log_model_metrics(
                        request_metrics=request_metrics,
                        model_name=MODEL_NAME,
                        prompt_length=len(prompt),
                        response_length=len(response_text),
                        processing_time=processing_time,
                        error=None
                    )

                    # Add token usage if available in the response
                    if "eval_count" in result:
                        request_metrics.add_metric("eval_count", result["eval_count"])
                    if "prompt_eval_count" in result:
                        request_metrics.add_metric("prompt_eval_count", result["prompt_eval_count"])
                    if "total_duration" in result:
                        request_metrics.add_metric("model_total_duration", result["total_duration"])

                return response_text

            except TimeoutError:
                logger.warning(f"Response generation timed out after {timeout_seconds}s")

                # Log the timeout error
                try:
                    log_llm_call(
                        prompt=prompt,
                        response="",
                        model_name=MODEL_NAME,
                        endpoint=endpoint,
                        context=context,
                        call_type=call_type,
                        metadata={"timeout_seconds": timeout_seconds, "max_tokens": max_tokens},
                        error=f"Timeout after {timeout_seconds}s"
                    )
                except Exception as log_error:
                    logger.warning(f"Failed to log timeout error: {log_error}")

                # Log error metrics if a request_metrics object was provided
                if request_metrics:
                    log_model_metrics(
                        request_metrics=request_metrics,
                        model_name=MODEL_NAME,
                        prompt_length=len(prompt),
                        response_length=0,
                        processing_time=timeout_seconds,
                        error=f"Timeout after {timeout_seconds}s"
                    )

                raise HTTPException(
                    status_code=504,
                    detail=f"Response generation timed out after {timeout_seconds} seconds. Try reducing the complexity of your request."
                )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating response: {e}")

        # Log the general error
        try:
            log_llm_call(
                prompt=prompt,
                response="",
                model_name=MODEL_NAME,
                endpoint=endpoint,
                context=context,
                call_type=call_type,
                metadata={"timeout_seconds": timeout_seconds, "max_tokens": max_tokens},
                error=str(e)
            )
        except Exception as log_error:
            logger.warning(f"Failed to log general error: {log_error}")

        # Log error metrics if a request_metrics object was provided
        if request_metrics:
            log_model_metrics(
                request_metrics=request_metrics,
                model_name=MODEL_NAME,
                prompt_length=len(prompt),
                response_length=0,
                processing_time=time.time() - start_time if 'start_time' in locals() else 0,
                error=str(e)
            )

        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

def extract_text_from_pdf(file_path: str) -> str:
    """Extract text from a PDF file."""
    try:
        logger.info(f"Extracting text from PDF: {file_path}")
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            total_pages = len(pdf_reader.pages)
            logger.info(f"PDF has {total_pages} pages")

            for page_num in range(total_pages):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
                else:
                    logger.warning(f"No text extracted from page {page_num + 1}")

        if not text.strip():
            logger.error("No text could be extracted from the PDF")
            raise HTTPException(status_code=400, detail="Could not extract text from the PDF. The file might be scanned or protected.")

        # Fix common character encoding issues, especially ligatures
        logger.info("Applying character encoding fixes to extracted text")
        char_replacements = {
            'ﬁ': 'fi',
            'ﬂ': 'fl',
            'ﬀ': 'ff',
            'ﬃ': 'ffi',
            'ﬄ': 'ffl',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '–': '-',
            '—': '-',
            '…': '...',
            '°': ' degrees',
            '©': '(c)',
            '®': '(r)',
            '™': '(tm)'
        }

        original_length = len(text)
        for old_char, new_char in char_replacements.items():
            if old_char in text:
                text = text.replace(old_char, new_char)
                logger.info(f"Replaced '{old_char}' with '{new_char}' in extracted text")

        if len(text) != original_length:
            logger.info(f"Character encoding fixes applied: {original_length} -> {len(text)} characters")

        logger.info(f"Successfully extracted {len(text)} characters from PDF")
        return text
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing the PDF file: {str(e)}")


def extract_text_from_docx(file_path: str) -> str:
    """Extract text from a DOCX file."""
    try:
        logger.info(f"Extracting text from DOCX: {file_path}")
        doc = docx.Document(file_path)

        # Extract text from paragraphs
        paragraphs_text = [paragraph.text for paragraph in doc.paragraphs if paragraph.text.strip()]
        logger.info(f"Extracted {len(paragraphs_text)} paragraphs from DOCX")

        # Extract text from tables
        tables_text = []
        for table in doc.tables:
            for row in table.rows:
                row_text = [cell.text for cell in row.cells if cell.text.strip()]
                if row_text:
                    tables_text.append(' | '.join(row_text))

        if tables_text:
            logger.info(f"Extracted text from {len(doc.tables)} tables in DOCX")

        # Combine all text
        all_text = '\n'.join(paragraphs_text + tables_text)

        if not all_text.strip():
            logger.error("No text could be extracted from the DOCX file")
            raise HTTPException(status_code=400, detail="Could not extract text from the DOCX file. The file might be empty or corrupted.")

        # Fix common character encoding issues, especially ligatures
        logger.info("Applying character encoding fixes to extracted DOCX text")
        char_replacements = {
            'ﬁ': 'fi',
            'ﬂ': 'fl',
            'ﬀ': 'ff',
            'ﬃ': 'ffi',
            'ﬄ': 'ffl',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '–': '-',
            '—': '-',
            '…': '...',
            '°': ' degrees',
            '©': '(c)',
            '®': '(r)',
            '™': '(tm)'
        }

        original_length = len(all_text)
        for old_char, new_char in char_replacements.items():
            if old_char in all_text:
                all_text = all_text.replace(old_char, new_char)
                logger.info(f"Replaced '{old_char}' with '{new_char}' in extracted DOCX text")

        if len(all_text) != original_length:
            logger.info(f"Character encoding fixes applied to DOCX: {original_length} -> {len(all_text)} characters")

        logger.info(f"Successfully extracted {len(all_text)} characters from DOCX")
        return all_text
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting text from DOCX: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing the DOCX file: {str(e)}")


def extract_text_from_image(file_path: str) -> str:
    """Extract text from a file by treating it as an image and using the LLM's vision capabilities."""
    logger.info(f"Attempting to extract text from file as image: {file_path}")

    try:
        # Create a prompt that asks the model to extract all text from the image
        image_prompt = """
        This document contains important text. Please extract ALL text content from this image.
        Format your response as plain text only, preserving the structure as much as possible.
        Include ALL text visible in the document, including:
        - Headers and titles
        - Bullet points and numbered lists
        - Dates and contact information
        - Skills, requirements, and qualifications
        - Any other textual information present

        Do not add any commentary, just extract the text content exactly as it appears.
        Preserve formatting like bullet points, section headers, and paragraph breaks as much as possible.
        """

        # Get response with a longer timeout for image processing
        response = get_response(
            prompt=image_prompt,
            timeout_seconds=120,
            max_tokens=2000,
            image_path=file_path,
            endpoint="image_extraction",
            context=os.path.basename(file_path) if file_path else "unknown",
            call_type="image_extract"
        )

        # Clean up the response to remove any potential commentary
        text = response.strip()

        # Check if we got a meaningful response
        if len(text) < 50:  # Arbitrary threshold for meaningful content
            logger.warning(f"Image-based text extraction returned very little text ({len(text)} chars)")
            raise ValueError("Image-based text extraction returned insufficient content")

        logger.info(f"Successfully extracted {len(text)} characters using image-based extraction")
        return text

    except Exception as e:
        logger.error(f"Error in image-based text extraction: {e}")
        raise ValueError(f"Failed to extract text using image-based approach: {str(e)}")

def extract_text_from_file(file_path: str, file_type: Literal["pdf", "docx"], request_metrics: RequestMetrics = None, source_filename: str = None, context: str = "unknown") -> str:
    """Extract text from a file based on its type with waterfall fallback mechanism.

    This function implements a waterfall mechanism for text extraction:
    1. First, it tries to extract text using the standard method based on file type
    2. If that fails, it falls back to treating the file as an image and using the LLM's vision capabilities
    3. Saves the extracted text to a debug file for analysis

    Args:
        file_path: Path to the file to extract text from
        file_type: Type of file ("pdf" or "docx")
        request_metrics: Optional metrics tracker
        source_filename: Original filename for debug file naming
        context: Context for extraction ("resume" or "jd")
    """
    start_time = time.time()
    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
    extraction_method = "unknown"
    extracted_text = ""

    try:
        # First attempt: Use standard extraction based on file type
        if file_type == "pdf":
            extracted_text = extract_text_from_pdf(file_path)
            extraction_method = "pdf_text"
        elif file_type == "docx":
            extracted_text = extract_text_from_docx(file_path)
            extraction_method = "docx_text"
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

        # Save extracted text for debugging (if we have a source filename)
        if source_filename and extracted_text:
            try:
                save_extracted_text(extracted_text, source_filename, context, extraction_method)
            except Exception as save_error:
                logger.warning(f"Failed to save extracted text for debugging: {save_error}")

        # Log file processing metrics if request_metrics is provided
        if request_metrics:
            processing_time = time.time() - start_time
            log_file_processing_metrics(
                request_metrics=request_metrics,
                file_type=file_type,
                file_size=file_size,
                extraction_method=extraction_method,
                text_length=len(extracted_text),
                processing_time=processing_time
            )

        return extracted_text

    except Exception as primary_error:
        # If standard extraction fails, log the error and try image-based extraction
        logger.warning(f"Standard text extraction failed: {primary_error}. Attempting image-based extraction as fallback.")

        try:
            # Second attempt: Use image-based extraction as fallback
            extracted_text = extract_text_from_image(file_path)
            extraction_method = "image_based"

            # Save extracted text for debugging (if we have a source filename)
            if source_filename and extracted_text:
                try:
                    save_extracted_text(extracted_text, source_filename, context, extraction_method)
                except Exception as save_error:
                    logger.warning(f"Failed to save extracted text for debugging: {save_error}")

            # Log file processing metrics if request_metrics is provided
            if request_metrics:
                processing_time = time.time() - start_time
                log_file_processing_metrics(
                    request_metrics=request_metrics,
                    file_type=file_type,
                    file_size=file_size,
                    extraction_method=extraction_method,
                    text_length=len(extracted_text),
                    processing_time=processing_time
                )

            return extracted_text

        except Exception as fallback_error:
            # If both methods fail, raise a comprehensive error
            logger.error(f"Both standard and image-based text extraction failed. Primary error: {primary_error}, Fallback error: {fallback_error}")

            # Log failure metrics if request_metrics is provided
            if request_metrics:
                processing_time = time.time() - start_time
                log_file_processing_metrics(
                    request_metrics=request_metrics,
                    file_type=file_type,
                    file_size=file_size,
                    extraction_method="failed",
                    text_length=0,
                    processing_time=processing_time
                )
                request_metrics.add_metric("extraction_error", f"{primary_error} | {fallback_error}")

            raise HTTPException(
                status_code=400,
                detail=f"Could not extract text from the file using any available method. The file might be corrupted, protected, or in an unsupported format."
            )

def calculate_resume_confidence_score(parsed_data: Dict) -> float:
    """
    Calculate a confidence score for the parsed resume data.

    This function evaluates how confident the model is in the correctness of the extracted information.
    It analyzes the structure, consistency, and clarity of the parsed data to estimate
    the likelihood that the information was correctly extracted.

    The scoring system works as follows:
    1. Each field has a weight based on how reliably it can be extracted
    2. Fields are scored based on structural correctness and consistency
    3. The final score represents the model's confidence in the extraction accuracy
    4. A higher score means higher confidence in the correctness of the extracted data

    Returns:
        float: overall_confidence_score
    """
    # Define weights for different fields based on extraction reliability
    field_weights = {
        "name": 0.10,  # Names are usually clear and easy to extract
        "email": 0.10,  # Emails have a standard format and are easy to identify
        "phone": 0.08,  # Phone numbers have patterns but can vary in format
        "summary": 0.05,  # Summaries can be ambiguous
        "location": 0.07,  # Locations are usually clear
        "education": 0.12,  # Education sections are usually well-structured
        "skills": 0.15,  # Skills can be scattered or in different formats
        "experience": 0.15,  # Experience is complex and can be ambiguous
        "projects": 0.08,  # Projects can be mixed with experience
        "certifications": 0.05,  # Certifications are usually clear
        "languages": 0.05  # Languages are usually in a standard format
    }

    # Calculate confidence based on structural correctness and consistency
    score = 0.0
    total_weight = 0.0
    detailed_scores = {}

    for field, weight in field_weights.items():
        total_weight += weight
        field_score = 0.0

        if field not in parsed_data:
            detailed_scores[field] = 0.0
            continue

        field_value = parsed_data[field]

        # Check if field has a value
        if field_value is None:
            detailed_scores[field] = 0.0
            continue

        # For string fields - check if they match expected patterns
        if isinstance(field_value, str):
            if field_value.strip():
                if field == "email":
                    # Check if it looks like an email (contains @ and .)
                    if "@" in field_value and "." in field_value.split("@")[1]:
                        field_score = 0.95  # High confidence for well-formed emails
                    else:
                        field_score = 0.3   # Low confidence for malformed emails
                elif field == "phone":
                    # Check if it looks like a phone number (contains digits and common separators)
                    if re.search(r'[\d\(\)\+\-\s\.]{7,}', field_value) and sum(c.isdigit() for c in field_value) >= 7:
                        field_score = 0.9   # High confidence for well-formed phone numbers
                    else:
                        field_score = 0.4   # Lower confidence for unusual formats
                else:
                    # For other string fields, confidence is based on length and content
                    # Very short or very long values might be less reliable
                    length = len(field_value.strip())
                    if 2 <= length <= 100:
                        field_score = 0.85  # Reasonable length, higher confidence
                    else:
                        field_score = 0.6   # Unusual length, lower confidence
            else:
                field_score = 0.0

        # For list fields - check structure and consistency
        elif isinstance(field_value, list):
            if len(field_value) > 0:
                if field == "education":
                    # Check structure of education entries
                    structure_scores = []
                    for entry in field_value:
                        if isinstance(entry, dict):
                            # Check for expected keys in education entries
                            has_degree = bool(entry.get("degree", "").strip())
                            has_institution = bool(entry.get("institution", "").strip())
                            has_year = bool(entry.get("year", "").strip())

                            # Calculate structure score for this entry
                            entry_score = (has_degree + has_institution + has_year) / 3
                            structure_scores.append(entry_score)

                    # Average structure score across all entries
                    if structure_scores:
                        field_score = sum(structure_scores) / len(structure_scores)
                        # Adjust based on number of entries (more entries = more complex extraction)
                        field_score = field_score * (0.7 + 0.3 / (1 + 0.1 * len(field_value)))
                    else:
                        field_score = 0.5  # Moderate confidence for unusual structure

                elif field == "experience":
                    # Check structure of experience entries
                    structure_scores = []
                    for entry in field_value:
                        if isinstance(entry, dict):
                            # Check for expected keys in experience entries
                            has_company = bool(entry.get("company_name", "").strip())
                            has_role = bool(entry.get("role", "").strip())
                            has_duration = bool(entry.get("duration", "").strip())
                            has_responsibilities = bool(entry.get("key_responsibilities", "").strip())

                            # Calculate structure score for this entry
                            entry_score = (has_company + has_role + has_duration + has_responsibilities) / 4
                            structure_scores.append(entry_score)

                    # Average structure score across all entries
                    if structure_scores:
                        field_score = sum(structure_scores) / len(structure_scores)
                        # Adjust based on number of entries (more entries = more complex extraction)
                        field_score = field_score * (0.7 + 0.3 / (1 + 0.1 * len(field_value)))
                    else:
                        field_score = 0.5  # Moderate confidence for unusual structure

                elif field == "skills":
                    # For skills list, check if entries look like skills (typically 1-4 words)
                    valid_skills = 0
                    for skill in field_value:
                        if isinstance(skill, str):
                            words = len(skill.split())
                            if 1 <= words <= 4 and len(skill) <= 50:
                                valid_skills += 1

                    # Calculate confidence based on proportion of valid-looking skills
                    if field_value:
                        field_score = valid_skills / len(field_value)
                        # Adjust based on number of skills (very few or very many skills might be less reliable)
                        if len(field_value) < 3 or len(field_value) > 30:
                            field_score *= 0.8
                    else:
                        field_score = 0.0

                else:
                    # For other list fields, base confidence on consistency of structure
                    if all(isinstance(item, str) for item in field_value):
                        field_score = 0.8  # High confidence for consistent string lists
                    elif all(isinstance(item, dict) for item in field_value):
                        field_score = 0.75  # Good confidence for consistent dict lists
                    else:
                        field_score = 0.5  # Lower confidence for mixed-type lists
            else:
                field_score = 0.7  # Empty list might be correct (no data) or incorrect (missed data)

        # For dict fields (skills when in dictionary format)
        elif isinstance(field_value, dict):
            if len(field_value) > 0:
                if field == "skills":
                    # For skills dict, check if keys look like skills and values provide context
                    valid_entries = 0
                    for skill, context in field_value.items():
                        if isinstance(skill, str) and isinstance(context, str):
                            skill_words = len(skill.split())
                            if 1 <= skill_words <= 4 and len(skill) <= 50 and context.strip():
                                valid_entries += 1

                    # Calculate confidence based on proportion of valid-looking skill entries
                    if field_value:
                        field_score = valid_entries / len(field_value)
                    else:
                        field_score = 0.0
                else:
                    # For other dict fields, moderate confidence
                    field_score = 0.7
            else:
                field_score = 0.5  # Empty dict might be correct or incorrect

        # Add to total score
        score += weight * field_score
        detailed_scores[field] = round(field_score * 100, 1)  # Store as percentage

    # Normalize score
    if total_weight > 0:
        normalized_score = score / total_weight
    else:
        normalized_score = 0.0

    return round(normalized_score, 2)  # Return only the overall score


def calculate_jd_confidence_score(parsed_data: Dict) -> float:
    """
    Calculate a confidence score for the parsed job description data.

    This function evaluates how confident the model is in the correctness of the extracted information.
    It analyzes the structure, consistency, and clarity of the parsed data to estimate
    the likelihood that the information was correctly extracted.

    The scoring system works as follows:
    1. Each field has a weight based on how reliably it can be extracted
    2. Fields are scored based on structural correctness and consistency
    3. The final score represents the model's confidence in the extraction accuracy
    4. A higher score means higher confidence in the correctness of the extracted data

    Returns:
        float: overall_confidence_score
    """
    # Define weights for different fields based on extraction reliability
    field_weights = {
        "job_title": 0.12,  # Job titles are usually clear and prominent
        "company_name": 0.10,  # Company names are usually clear
        "location": 0.08,  # Locations are usually clear
        "job_type": 0.05,  # Job types can be ambiguous
        "work_mode": 0.05,  # Work modes can be ambiguous
        "summary": 0.08,  # Summaries can be mixed with other content
        "responsibilities": 0.15,  # Responsibilities are complex and can be scattered
        "required_skills": 0.15,  # Skills can be mixed with responsibilities
        "preferred_skills": 0.10,  # Preferred skills can be ambiguous
        "required_experience": 0.07,  # Experience requirements can be embedded in text
        "education_requirements": 0.05  # Education requirements are usually clear
    }

    # Calculate confidence based on structural correctness and consistency
    score = 0.0
    total_weight = 0.0
    detailed_scores = {}

    for field, weight in field_weights.items():
        total_weight += weight
        field_score = 0.0

        if field not in parsed_data:
            detailed_scores[field] = 0.0
            continue

        field_value = parsed_data[field]

        # Check if field has a value
        if field_value is None:
            detailed_scores[field] = 0.7  # Null might be correct (no data in JD)
            continue

        # For string fields - check if they match expected patterns
        if isinstance(field_value, str):
            if field_value.strip():
                if field == "job_title":
                    # Job titles are usually concise
                    words = len(field_value.split())
                    if 1 <= words <= 10:
                        field_score = 0.9  # High confidence for reasonable job titles
                    else:
                        field_score = 0.6  # Lower confidence for unusually long titles

                elif field == "required_experience":
                    # Check if it looks like an experience requirement (contains years/months and numbers)
                    if re.search(r'\d+\s*(?:year|yr|month|mo|yrs|years|months)', field_value.lower()):
                        field_score = 0.9  # High confidence for well-formed experience requirements
                    else:
                        field_score = 0.6  # Lower confidence for unusual formats

                else:
                    # For other string fields, confidence is based on length and content
                    length = len(field_value.strip())
                    if field in ["company_name", "location", "job_type", "work_mode"] and length <= 50:
                        field_score = 0.85  # Reasonable length for these fields
                    elif field == "summary" and 20 <= length <= 500:
                        field_score = 0.8   # Reasonable length for summary
                    else:
                        field_score = 0.7   # Default confidence
            else:
                field_score = 0.0

        # For list fields - check structure and consistency
        elif isinstance(field_value, list):
            if len(field_value) > 0:
                if field in ["responsibilities", "required_skills", "preferred_skills"]:
                    # Check if entries look like responsibilities or skills (complete phrases/sentences)
                    valid_entries = 0
                    for entry in field_value:
                        if isinstance(entry, str):
                            # Responsibilities and skills should be reasonably sized text
                            if 3 <= len(entry.strip()) <= 200:
                                valid_entries += 1

                    # Calculate confidence based on proportion of valid-looking entries
                    if field_value:
                        field_score = valid_entries / len(field_value)

                        # Adjust based on number of entries
                        if field == "responsibilities" and not (3 <= len(field_value) <= 15):
                            field_score *= 0.9  # Unusual number of responsibilities
                        elif field == "required_skills" and not (2 <= len(field_value) <= 20):
                            field_score *= 0.9  # Unusual number of required skills
                    else:
                        field_score = 0.0

                elif field == "education_requirements":
                    # Education requirements are usually short phrases
                    valid_entries = 0
                    for entry in field_value:
                        if isinstance(entry, str):
                            words = len(entry.split())
                            if 2 <= words <= 15:
                                valid_entries += 1

                    # Calculate confidence based on proportion of valid-looking entries
                    if field_value:
                        field_score = valid_entries / len(field_value)
                    else:
                        field_score = 0.0

                else:
                    # For other list fields, base confidence on consistency
                    if all(isinstance(item, str) for item in field_value):
                        field_score = 0.8  # High confidence for consistent string lists
                    elif all(isinstance(item, dict) for item in field_value):
                        field_score = 0.75  # Good confidence for consistent dict lists
                    else:
                        field_score = 0.5  # Lower confidence for mixed-type lists
            else:
                field_score = 0.7  # Empty list might be correct (no data) or incorrect (missed data)

        # For dict fields or complex objects
        elif isinstance(field_value, dict) or (isinstance(field_value, list) and all(isinstance(item, dict) for item in field_value)):
            # Complex structures like benefits or requirements
            field_score = 0.75  # Moderate confidence for complex structures

        # Add to total score
        score += weight * field_score
        detailed_scores[field] = round(field_score * 100, 1)  # Store as percentage

    # Normalize score
    if total_weight > 0:
        normalized_score = score / total_weight
    else:
        normalized_score = 0.0

    return round(normalized_score, 2)  # Return only the overall score

def extract_single_section(text: str, section_name: str, source_filename: str, conv_folder: str = "", call_number: int = 0) -> Tuple[str, float]:
    """Extract a single section from resume text using LLM."""

    # Define section-specific prompts for literal extraction
    section_prompts = {
        "summary": """Look for a section in the resume with headings like "SUMMARY", "PROFESSIONAL SUMMARY", "OBJECTIVE", "CAREER OBJECTIVE", "PROFILE", or similar.
Extract ONLY the text that appears directly under that specific section heading. This should be a brief overview or objective statement.
Do not include contact information, work experience, or other sections. Return exactly what is written under the summary section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.""",

        "education": """Look for a section in the resume with headings like "EDUCATION", "ACADEMIC BACKGROUND", "QUALIFICATIONS", or "EDUCATIONAL QUALIFICATIONS".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.""",

        "experience": """Look for a section in the resume with headings like "EXPERIENCE", "WORK EXPERIENCE", "PROFESSIONAL EXPERIENCE", "EMPLOYMENT HISTORY", "CAREER HISTORY", or "EMPLOYMENT".
Extract ONLY the text that appears directly under that specific section heading. This should include job titles, company names, dates, and job responsibilities.
Do not include information from other sections like achievements, projects, skills, or summary. Return exactly what is written under the work experience section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.""",

        "skills": """Look for a section in the resume with headings like "SKILLS", "TECHNICAL SKILLS", "CORE COMPETENCIES", "TECHNOLOGIES", or "EXPERTISE".
For skills section only: Extract and compile ALL skills mentioned throughout the entire resume, including technical skills, programming languages, tools, frameworks, and soft skills.
Present them as a comprehensive list. This is the only section where you should gather information from the entire document.""",

        "projects": """Look for a section in the resume with headings like "PROJECTS", "PERSONAL PROJECTS", "ACADEMIC PROJECTS", or "KEY PROJECTS".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.""",

        "certifications": """Look for a section in the resume with headings like "CERTIFICATIONS", "CERTIFICATES", "LICENSES", "PROFESSIONAL CERTIFICATIONS", or "CREDENTIALS".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.""",

        "achievements": """Look for a section in the resume with headings like "ACHIEVEMENTS", "AWARDS", "HONORS", "ACCOMPLISHMENTS", "RECOGNITION", or "ACCOLADES".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections like experience or projects.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.""",

        "languages": """Look for a section in the resume with headings like "LANGUAGES", "LANGUAGE SKILLS", "LINGUISTIC ABILITIES", or "SPOKEN LANGUAGES".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'."""
    }

    if section_name not in section_prompts:
        return "INVALID_SECTION", 0.0

    prompt = f"""{section_prompts[section_name]}

Resume Text:
{text}

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content"""

    try:
        start_time = time.time()
        response = get_response(
            prompt=prompt,
            timeout_seconds=45,
            max_tokens=800,
            endpoint="/section",
            context=f"{source_filename}_{section_name}",
            call_type="section_extraction"
        )
        processing_time = time.time() - start_time

        # Clean the response
        cleaned_response = response.strip()

        # Calculate confidence based on response quality
        confidence = 0.0
        if cleaned_response and cleaned_response != "NOT_FOUND":
            # Basic confidence scoring
            if len(cleaned_response) > 20:  # Reasonable content length
                confidence = 0.8
                if section_name in cleaned_response.lower():  # Contains section keywords
                    confidence += 0.1
                if len(cleaned_response.split()) > 5:  # Multiple words
                    confidence += 0.1
            else:
                confidence = 0.5  # Short but present

        confidence = min(confidence, 1.0)  # Cap at 1.0

        # Log this call to the conversation folder
        if conv_folder and call_number > 0:
            log_section_extraction_call(
                prompt=prompt,
                response=cleaned_response,
                section_name=section_name,
                source_filename=source_filename,
                conv_folder=conv_folder,
                call_number=call_number,
                confidence=confidence,
                processing_time=processing_time
            )

        logger.info(f"Extracted {section_name} section in {processing_time:.2f}s (confidence: {confidence:.2f})")
        return cleaned_response, confidence

    except Exception as e:
        error_msg = str(e)

        # Log the error to conversation folder
        if conv_folder and call_number > 0:
            log_section_extraction_call(
                prompt=prompt,
                response="",
                section_name=section_name,
                source_filename=source_filename,
                conv_folder=conv_folder,
                call_number=call_number,
                confidence=0.0,
                processing_time=time.time() - start_time if 'start_time' in locals() else 0.0,
                error=error_msg
            )

        logger.error(f"Error extracting {section_name} section: {e}")
        return f"ERROR: {error_msg}", 0.0

def extract_basic_info_smart(text: str) -> Tuple[str, float]:
    """
    Smart extraction of basic info (name, email, phone, role) from resume header.

    This function extracts the header section that typically contains contact information
    and role/title, which usually appears before any formal section headers.
    """

    # Find the first formal section header to determine where basic info ends
    section_patterns = [
        r'(?i)(?:^|\n)\s*(?:summary|objective|profile|professional\s+summary|career\s+objective|about\s+me)\s*:?\s*\n',
        r'(?i)(?:^|\n)\s*(?:education|academic|qualifications|educational\s+background)\s*:?\s*\n',
        r'(?i)(?:^|\n)\s*(?:experience|work\s+experience|employment|professional\s+experience|career\s+history)\s*:?\s*\n',
        r'(?i)(?:^|\n)\s*(?:skills|technical\s+skills|core\s+competencies|technologies|expertise)\s*:?\s*\n',
        r'(?i)(?:^|\n)\s*(?:projects|personal\s+projects|key\s+projects|notable\s+projects)\s*:?\s*\n',
        r'(?i)(?:^|\n)\s*(?:certifications|certificates|professional\s+certifications|licenses)\s*:?\s*\n',
        r'(?i)(?:^|\n)\s*(?:achievements|accomplishments|awards|honors|recognition)\s*:?\s*\n',
        r'(?i)(?:^|\n)\s*(?:languages|language\s+skills|linguistic\s+skills)\s*:?\s*\n'
    ]

    # Find the earliest section header
    first_section_pos = len(text)
    for pattern in section_patterns:
        match = re.search(pattern, text)
        if match:
            first_section_pos = min(first_section_pos, match.start())

    # Extract header content (everything before first formal section)
    if first_section_pos < len(text):
        header_content = text[:first_section_pos].strip()
    else:
        # If no formal sections found, take first 15 lines as fallback
        lines = text.split('\n')
        header_content = '\n'.join(lines[:15]).strip()

    # Clean up the header content
    header_content = re.sub(r'\n\s*\n\s*\n', '\n\n', header_content)  # Remove excessive newlines
    header_content = re.sub(r'^\s+|\s+$', '', header_content, flags=re.MULTILINE)  # Remove leading/trailing spaces

    # Calculate confidence based on content quality and patterns found
    confidence = 0.6  # Base confidence

    if header_content:
        # Check for common patterns that indicate good basic info extraction
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        phone_pattern = r'(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}'
        name_indicators = ['linkedin', 'github', 'email', 'phone', '@', 'developer', 'engineer', 'manager']

        if re.search(email_pattern, header_content):
            confidence += 0.2
        if re.search(phone_pattern, header_content):
            confidence += 0.15
        if any(indicator in header_content.lower() for indicator in name_indicators):
            confidence += 0.1
        if len(header_content.split('\n')) >= 2:  # Multi-line header
            confidence += 0.05
        if 20 <= len(header_content) <= 500:  # Reasonable length
            confidence += 0.1

        confidence = min(confidence, 1.0)

    return header_content, confidence

def parse_basic_info_components(basic_info_text: str) -> Dict[str, str]:
    """
    Parse basic info text into structured components (name, email, phone, role).

    Returns a dictionary with extracted components.
    """
    components = {
        'name': '',
        'email': '',
        'phone': '',
        'role': ''
    }

    if not basic_info_text:
        return components

    lines = basic_info_text.split('\n')

    # Extract email
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    email_match = re.search(email_pattern, basic_info_text)
    if email_match:
        components['email'] = email_match.group()

    # Extract phone number
    phone_patterns = [
        r'(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}',  # US format
        r'(?:\+?91[-.\s]?)?[0-9]{10}',  # Indian format
        r'(?:\+?[0-9]{1,3}[-.\s]?)?[0-9]{3,4}[-.\s]?[0-9]{3,4}[-.\s]?[0-9]{3,4}'  # General international
    ]

    for pattern in phone_patterns:
        phone_match = re.search(pattern, basic_info_text)
        if phone_match:
            components['phone'] = phone_match.group().strip()
            break

    # Extract name (usually first non-empty line that doesn't contain email/phone/common keywords)
    skip_keywords = ['email', 'phone', 'linkedin', 'github', 'address', 'location', '@', '+', 'www.']

    for line in lines:
        line = line.strip()
        if line and len(line) > 2:
            # Skip lines with email, phone, or common keywords
            if any(keyword in line.lower() for keyword in skip_keywords):
                continue
            # Skip lines that are mostly symbols or numbers
            if len(re.sub(r'[^a-zA-Z\s]', '', line)) < len(line) * 0.6:
                continue
            # This is likely the name
            components['name'] = line
            break

    # Extract role/title (look for job titles, usually after name)
    role_keywords = [
        'developer', 'engineer', 'manager', 'analyst', 'consultant', 'architect', 'lead', 'senior',
        'junior', 'associate', 'specialist', 'coordinator', 'director', 'executive', 'officer',
        'scientist', 'researcher', 'designer', 'programmer', 'administrator', 'technician'
    ]

    for line in lines:
        line = line.strip()
        if line and line != components['name']:
            # Check if line contains role keywords
            if any(keyword in line.lower() for keyword in role_keywords):
                # Skip if it contains email or phone
                if not any(skip in line.lower() for skip in ['email', 'phone', '@', '+']):
                    components['role'] = line
                    break

    return components

def extract_sections_regex(text: str, source_filename: str, conv_folder: str = "") -> Tuple[Dict[str, str], Dict[str, float]]:
    """Extract sections from resume text using regex pattern matching."""

    sections = {}
    confidence_scores = {}

    # First, extract basic info (header section)
    basic_info, basic_info_confidence = extract_basic_info_smart(text)
    sections['basic_info'] = basic_info
    confidence_scores['basic_info'] = basic_info_confidence

    # Parse basic info into structured components
    basic_components = parse_basic_info_components(basic_info)

    # Add individual components as separate sections for easy access
    for component_name, component_value in basic_components.items():
        if component_value:  # Only add if value exists
            sections[f'basic_{component_name}'] = component_value
            # Confidence based on pattern matching success
            if component_name == 'email' and '@' in component_value:
                confidence_scores[f'basic_{component_name}'] = 0.95
            elif component_name == 'phone' and any(c.isdigit() for c in component_value):
                confidence_scores[f'basic_{component_name}'] = 0.9
            elif component_name == 'name' and len(component_value.split()) >= 2:
                confidence_scores[f'basic_{component_name}'] = 0.85
            elif component_name == 'role' and len(component_value) > 5:
                confidence_scores[f'basic_{component_name}'] = 0.8
            else:
                confidence_scores[f'basic_{component_name}'] = 0.7

    # Common section header patterns (case insensitive)
    section_patterns = {
        'summary': r'(?i)(?:^|\n)\s*(?:summary|objective|profile|professional\s+summary|career\s+objective|about\s+me)\s*:?\s*\n',
        'education': r'(?i)(?:^|\n)\s*(?:education|academic|qualifications|educational\s+background)\s*:?\s*\n',
        'experience': r'(?i)(?:^|\n)\s*(?:experience|work\s+experience|employment|professional\s+experience|career\s+history)\s*:?\s*\n',
        'skills': r'(?i)(?:^|\n)\s*(?:skills|technical\s+skills|core\s+competencies|technologies|expertise)\s*:?\s*\n',
        'projects': r'(?i)(?:^|\n)\s*(?:projects|personal\s+projects|key\s+projects|notable\s+projects)\s*:?\s*\n',
        'certifications': r'(?i)(?:^|\n)\s*(?:certifications|certificates|professional\s+certifications|licenses)\s*:?\s*\n',
        'achievements': r'(?i)(?:^|\n)\s*(?:achievements|accomplishments|awards|honors|recognition)\s*:?\s*\n',
        'languages': r'(?i)(?:^|\n)\s*(?:languages|language\s+skills|linguistic\s+skills)\s*:?\s*\n'
    }

    # Find all section headers with their positions
    section_positions = []
    for section_name, pattern in section_patterns.items():
        matches = list(re.finditer(pattern, text))
        for match in matches:
            section_positions.append({
                'name': section_name,
                'start': match.end(),  # Start after the header
                'header_start': match.start(),
                'header_text': match.group().strip()
            })

    # Sort sections by their position in the text
    section_positions.sort(key=lambda x: x['start'])

    # Extract content between sections
    for i, section in enumerate(section_positions):
        section_name = section['name']
        content_start = section['start']

        # Find the end position (start of next section or end of text)
        if i + 1 < len(section_positions):
            content_end = section_positions[i + 1]['header_start']
        else:
            content_end = len(text)

        # Extract content between this section and the next
        content = text[content_start:content_end].strip()

        # Clean up the content
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)  # Remove excessive newlines
        content = re.sub(r'^\s+|\s+$', '', content, flags=re.MULTILINE)  # Remove leading/trailing spaces

        # Calculate confidence based on content quality
        confidence = 0.0
        if content:
            confidence = 0.7  # Base confidence for regex extraction

            # Increase confidence based on content characteristics
            if len(content) > 20:
                confidence += 0.1
            if len(content.split()) > 5:
                confidence += 0.1
            if section_name.lower() in content.lower():
                confidence += 0.05
            if '\n' in content:  # Multi-line content
                confidence += 0.05

            confidence = min(confidence, 1.0)

        sections[section_name] = content
        confidence_scores[section_name] = confidence

    # Handle skills specially - compile from entire resume if not found in dedicated section
    if 'skills' not in sections or not sections['skills']:
        skills_content = extract_skills_from_entire_text(text)
        if skills_content:
            sections['skills'] = skills_content
            confidence_scores['skills'] = 0.6  # Lower confidence for compiled skills

    return sections, confidence_scores

def extract_skills_from_entire_text(text: str) -> str:
    """Extract skills mentioned throughout the entire resume text."""

    # Common skill-related keywords and patterns
    skill_patterns = [
        r'(?i)(?:programming\s+languages?|languages?)\s*:?\s*([^\n]+)',
        r'(?i)(?:technologies?|tools?)\s*:?\s*([^\n]+)',
        r'(?i)(?:frameworks?|libraries?)\s*:?\s*([^\n]+)',
        r'(?i)(?:databases?|db)\s*:?\s*([^\n]+)',
        r'(?i)(?:platforms?|environments?)\s*:?\s*([^\n]+)',
    ]

    skills_found = []

    for pattern in skill_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            # Clean and split the skills
            skills_line = match.strip()
            if skills_line:
                # Split by common delimiters
                individual_skills = re.split(r'[,;|•·\-\n]+', skills_line)
                for skill in individual_skills:
                    skill = skill.strip()
                    if skill and len(skill) > 1 and len(skill) < 50:
                        skills_found.append(skill)

    # Remove duplicates while preserving order
    unique_skills = []
    seen = set()
    for skill in skills_found:
        if skill.lower() not in seen:
            unique_skills.append(skill)
            seen.add(skill.lower())

    return '\n'.join(unique_skills) if unique_skills else ""

def extract_all_sections_single_call(text: str, source_filename: str, conv_folder: str = "") -> Tuple[Dict[str, str], Dict[str, float]]:
    """Extract all sections from resume text using a single LLM call."""

    prompt = f"""You are an expert resume parser. Extract sections from this resume by finding section headings and copying the content that appears directly under each heading.

IMPORTANT EXTRACTION RULES:
1. Look for section headings (like "SUMMARY", "EDUCATION", "EXPERIENCE", etc.)
2. Extract ONLY the text that appears directly under each specific section heading
3. Do NOT mix content from different sections
4. Do NOT include information that appears under other section headings
5. For SKILLS section only: Compile ALL skills mentioned throughout the entire resume
6. Copy the content exactly as it appears, nothing more, nothing less

Return the response in this exact format:

[SUMMARY]
(Extract only from summary/objective/profile section or 'NOT_FOUND' if not present)

[EDUCATION]
(Extract only from education/academic section or 'NOT_FOUND' if not present)

[EXPERIENCE]
(Extract only from work experience/employment section - job titles, companies, dates, responsibilities or 'NOT_FOUND' if not present)

[SKILLS]
(Compile ALL skills from entire resume - this is the only exception to the rule)

[PROJECTS]
(Extract only from projects section or 'NOT_FOUND' if not present)

[CERTIFICATIONS]
(Extract only from certifications/licenses section or 'NOT_FOUND' if not present)

[ACHIEVEMENTS]
(Extract only from achievements/awards section or 'NOT_FOUND' if not present)

[LANGUAGES]
(Extract only from languages section or 'NOT_FOUND' if not present)

Resume Text:
{text}

Remember: Be literal and section-specific. If a project is listed under "ACHIEVEMENTS", do NOT include it in "PROJECTS". Only include content that appears directly under the relevant section heading."""

    try:
        start_time = time.time()
        response = get_response(
            prompt=prompt,
            timeout_seconds=90,
            max_tokens=2000,
            endpoint="/section2",
            context=f"{source_filename}_all_sections",
            call_type="all_sections_extraction"
        )
        processing_time = time.time() - start_time

        # Parse the response to extract sections
        sections = {}
        confidence_scores = {}

        # Define section markers
        section_markers = [
            "SUMMARY", "EDUCATION", "EXPERIENCE", "SKILLS",
            "PROJECTS", "CERTIFICATIONS", "ACHIEVEMENTS", "LANGUAGES"
        ]

        # Split response by section markers
        current_section = None
        current_content = []

        lines = response.split('\n')
        for line in lines:
            line = line.strip()

            # Check if this line is a section marker
            section_found = None
            for marker in section_markers:
                if f"[{marker}]" in line.upper():
                    section_found = marker.lower()
                    break

            if section_found:
                # Save previous section if exists
                if current_section and current_content:
                    content = '\n'.join(current_content).strip()
                    sections[current_section] = content if content != "NOT_FOUND" else ""

                    # Calculate confidence for this section
                    if content and content != "NOT_FOUND":
                        confidence = 0.7  # Base confidence for single call
                        if len(content) > 20:
                            confidence += 0.2
                        if len(content.split()) > 5:
                            confidence += 0.1
                    else:
                        confidence = 0.0
                    confidence_scores[current_section] = min(confidence, 1.0)

                # Start new section
                current_section = section_found
                current_content = []
            else:
                # Add line to current section content
                if current_section and line:
                    current_content.append(line)

        # Don't forget the last section
        if current_section and current_content:
            content = '\n'.join(current_content).strip()
            sections[current_section] = content if content != "NOT_FOUND" else ""

            if content and content != "NOT_FOUND":
                confidence = 0.7
                if len(content) > 20:
                    confidence += 0.2
                if len(content.split()) > 5:
                    confidence += 0.1
            else:
                confidence = 0.0
            confidence_scores[current_section] = min(confidence, 1.0)

        # Ensure all expected sections are present
        for marker in section_markers:
            section_key = marker.lower()
            if section_key not in sections:
                sections[section_key] = ""
                confidence_scores[section_key] = 0.0

        # Log this single call to the conversation folder
        if conv_folder:
            try:
                # Calculate overall confidence for logging
                valid_confidences = [score for score in confidence_scores.values() if score > 0]
                overall_confidence = sum(valid_confidences) / len(valid_confidences) if valid_confidences else 0.0

                # Create a comprehensive response for logging
                log_response = "EXTRACTED SECTIONS:\n\n"
                for section_name, content in sections.items():
                    log_response += f"[{section_name.upper()}]\n"
                    if content and content.strip():
                        log_response += content.strip() + "\n\n"
                    else:
                        log_response += "NOT_FOUND\n\n"

                log_section_extraction_call(
                    prompt=prompt,
                    response=log_response,
                    section_name="all_sections",
                    source_filename=source_filename,
                    conv_folder=conv_folder,
                    call_number=1,
                    confidence=overall_confidence,
                    processing_time=processing_time
                )
            except Exception as log_error:
                logger.warning(f"Failed to log single call extraction: {log_error}")

        logger.info(f"Extracted all sections in {processing_time:.2f}s using single call")
        return sections, confidence_scores

    except Exception as e:
        error_msg = str(e)

        # Log the error to conversation folder
        if conv_folder:
            try:
                log_section_extraction_call(
                    prompt=prompt,
                    response="",
                    section_name="all_sections",
                    source_filename=source_filename,
                    conv_folder=conv_folder,
                    call_number=1,
                    confidence=0.0,
                    processing_time=time.time() - start_time if 'start_time' in locals() else 0.0,
                    error=error_msg
                )
            except Exception as log_error:
                logger.warning(f"Failed to log error for single call extraction: {log_error}")

        logger.error(f"Error extracting all sections: {e}")
        # Return empty sections with zero confidence
        empty_sections = {
            "summary": "", "education": "", "experience": "", "skills": "",
            "projects": "", "certifications": "", "achievements": "", "languages": ""
        }
        zero_confidence = {key: 0.0 for key in empty_sections.keys()}
        return empty_sections, zero_confidence

def convert_skills_to_dict(parsed_data: Dict) -> Dict:
    """Convert skills from list to dictionary with context."""
    if "skills" not in parsed_data or not isinstance(parsed_data["skills"], list):
        return parsed_data

    # Get the original skills list
    skills_list = parsed_data["skills"]

    # Create a dictionary with context from experience, projects, etc.
    skills_dict = {}

    # Extract context from experience
    experience_context = {}
    if "experience" in parsed_data and isinstance(parsed_data["experience"], list):
        for exp in parsed_data["experience"]:
            if isinstance(exp, dict):
                # Extract text from experience entries
                exp_text = ""
                for _, value in exp.items():  # Use _ to indicate unused variable
                    if isinstance(value, str):
                        exp_text += value + " "

                # Check which skills are mentioned in this experience
                for skill in skills_list:
                    if isinstance(skill, str) and skill.lower() in exp_text.lower():
                        # Use the role and company as context
                        role = exp.get("role", "")
                        company = exp.get("company_name", "")
                        if role and company:
                            experience_context[skill] = f"Used as {role} at {company}"

    # Extract context from projects
    project_context = {}
    if "projects" in parsed_data and isinstance(parsed_data["projects"], list):
        for proj in parsed_data["projects"]:
            if isinstance(proj, dict) and "description" in proj:
                proj_desc = proj.get("description", "")
                if isinstance(proj_desc, str):
                    for skill in skills_list:
                        if isinstance(skill, str) and skill.lower() in proj_desc.lower():
                            project_context[skill] = f"Used in project: {proj.get('name', 'Unknown project')}"

    # Combine contexts, prioritizing experience over projects
    for skill in skills_list:
        if isinstance(skill, str):
            if skill in experience_context:
                skills_dict[skill] = experience_context[skill]
            elif skill in project_context:
                skills_dict[skill] = project_context[skill]
            else:
                skills_dict[skill] = "Mentioned in resume"

    # Replace the skills list with the dictionary
    parsed_data["skills"] = skills_dict

    return parsed_data

def create_extracted_text_filename(source_filename: str, context: str) -> str:
    """
    Create a filename for saving extracted text based on the source file.

    Args:
        source_filename: Original filename (e.g., "mehak_jain.pdf")
        context: Either "resume" or "jd" to determine folder

    Returns:
        Full path to the text file where extracted text should be saved
    """
    # Determine the appropriate folder
    if context.lower() in ["resume", "resume_extracted_text"]:
        folder_name = "resume_extracted_text"
    elif context.lower() in ["jd", "job_description", "jd_extracted_text"]:
        folder_name = "jd_extracted_text"
    else:
        folder_name = "extracted_text"  # Fallback folder

    # Create folder if it doesn't exist
    os.makedirs(folder_name, exist_ok=True)

    # Extract the base name without extension
    base_name = os.path.splitext(os.path.basename(source_filename))[0]

    # Clean the filename for safe filesystem usage
    import re
    safe_name = re.sub(r'[^\w\-_\.]', '_', base_name)

    # Create the text filename
    text_filename = f"{safe_name}.txt"

    # Return full path
    return os.path.join(folder_name, text_filename)

def save_extracted_text(extracted_text: str, source_filename: str, context: str, extraction_method: str = "unknown") -> str:
    """
    Save extracted text to a file for debugging and analysis.

    Args:
        extracted_text: The text that was extracted from the document
        source_filename: Original filename (e.g., "mehak_jain.pdf")
        context: Either "resume" or "jd" to determine folder
        extraction_method: Method used for extraction (e.g., "pdf_text", "image_based", "docx_text")

    Returns:
        Path to the saved file
    """
    try:
        # Get the filename for saving
        save_path = create_extracted_text_filename(source_filename, context)

        # Create metadata header
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        metadata_header = f"""# Extracted Text Debug File
# Source File: {source_filename}
# Context: {context}
# Extraction Method: {extraction_method}
# Timestamp: {timestamp}
# Text Length: {len(extracted_text)} characters
# ================================================

"""

        # Combine metadata and extracted text
        full_content = metadata_header + extracted_text

        # Save to file
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(full_content)

        logger.info(f"Saved extracted text to: {save_path}")
        return save_path

    except Exception as e:
        logger.error(f"Failed to save extracted text: {e}")
        return ""

def save_section_extraction(sections: Dict[str, str], source_filename: str, extraction_method: str, stats: Dict[str, Any]):
    """Save extracted sections to a file for analysis."""
    try:
        # Create sections directory if it doesn't exist
        sections_dir = "resume sections extracted"
        os.makedirs(sections_dir, exist_ok=True)

        # Create filename with timestamp and source info
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_filename = re.sub(r'[^\w\-_\.]', '_', source_filename)
        sections_filename = f"{timestamp}_{safe_filename}_{extraction_method}_sections.txt"
        sections_path = os.path.join(sections_dir, sections_filename)

        # Save the extracted sections
        with open(sections_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("RESUME SECTION EXTRACTION RESULTS\n")
            f.write("=" * 80 + "\n")
            f.write(f"Source File: {source_filename}\n")
            f.write(f"Extraction Method: {extraction_method}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Total Sections Extracted: {len(sections)}\n")

            # Add statistics
            if stats:
                f.write(f"Processing Time: {stats.get('processing_time', 'N/A')} seconds\n")
                f.write(f"Total LLM Calls: {stats.get('total_calls', 'N/A')}\n")
                f.write(f"Overall Confidence: {stats.get('overall_confidence', 'N/A')}\n")

            f.write("=" * 80 + "\n\n")

            # Write each section
            for section_name, content in sections.items():
                f.write(f"[{section_name.upper()}]\n")
                f.write("-" * 40 + "\n")
                if content and content.strip():
                    f.write(content.strip() + "\n")
                else:
                    f.write("No content extracted for this section.\n")
                f.write("\n" + "=" * 40 + "\n\n")

        logger.info(f"Saved section extraction results to: {sections_path}")
        return sections_path

    except Exception as e:
        logger.warning(f"Failed to save section extraction results: {e}")
        return None

def create_conversation_folder(source_filename: str, extraction_method: str) -> str:
    """Create a conversation folder for tracking all LLM calls for a resume."""
    try:
        # Create base conversation directory
        conv_base_dir = "conv"
        os.makedirs(conv_base_dir, exist_ok=True)

        # Create timestamp and safe filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_filename = re.sub(r'[^\w\-_\.]', '_', os.path.splitext(source_filename)[0])

        # Create conversation folder name
        conv_folder_name = f"{timestamp}_{safe_filename}_{extraction_method}_conversation"
        conv_folder_path = os.path.join(conv_base_dir, conv_folder_name)

        # Create the folder
        os.makedirs(conv_folder_path, exist_ok=True)

        # Create a summary file for this conversation
        summary_file = os.path.join(conv_folder_path, "conversation_summary.txt")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("RESUME SECTION EXTRACTION CONVERSATION\n")
            f.write("=" * 80 + "\n")
            f.write(f"Source File: {source_filename}\n")
            f.write(f"Extraction Method: {extraction_method}\n")
            f.write(f"Started: {datetime.now().isoformat()}\n")
            f.write(f"Conversation Folder: {conv_folder_path}\n")
            f.write("=" * 80 + "\n\n")
            f.write("This folder contains all LLM calls made for this resume extraction.\n")
            f.write("Each section extraction is logged as a separate file.\n\n")

        logger.info(f"Created conversation folder: {conv_folder_path}")
        return conv_folder_path

    except Exception as e:
        logger.warning(f"Failed to create conversation folder: {e}")
        return ""

def log_section_extraction_call(
    prompt: str,
    response: str,
    section_name: str,
    source_filename: str,
    conv_folder: str,
    call_number: int,
    confidence: float,
    processing_time: float,
    error: str = None
):
    """Log a section extraction LLM call to the conversation folder."""
    try:
        if not conv_folder or not os.path.exists(conv_folder):
            logger.warning("Conversation folder not available for logging")
            return

        # Create filename for this specific call
        safe_filename = re.sub(r'[^\w\-_\.]', '_', os.path.splitext(source_filename)[0])
        call_filename = f"call_{call_number:02d}_{section_name}_{safe_filename}.txt"
        call_path = os.path.join(conv_folder, call_filename)

        # Log the call
        with open(call_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write(f"SECTION EXTRACTION CALL #{call_number} - {section_name.upper()}\n")
            f.write("=" * 80 + "\n")
            f.write(f"Source File: {source_filename}\n")
            f.write(f"Section: {section_name}\n")
            f.write(f"Call Number: {call_number}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Processing Time: {processing_time:.2f} seconds\n")
            f.write(f"Confidence Score: {confidence:.2f}\n")
            f.write(f"Model: {MODEL_NAME}\n")

            if error:
                f.write(f"Error: {error}\n")

            f.write("=" * 80 + "\n\n")

            # Prompt section
            f.write("[PROMPT]\n")
            f.write(f"Length: {len(prompt)} characters\n")
            f.write("-" * 40 + "\n")
            f.write(prompt)
            f.write("\n" + "-" * 40 + "\n\n")

            # Response section
            f.write("[RESPONSE]\n")
            if response:
                f.write(f"Length: {len(response)} characters\n")
                f.write(f"Confidence: {confidence:.2f}\n")
                f.write("-" * 40 + "\n")
                f.write(response)
                f.write("\n" + "-" * 40 + "\n")
            else:
                f.write("No response received\n")

            f.write("\n" + "=" * 80 + "\n")

        logger.debug(f"Logged section extraction call to: {call_path}")

    except Exception as e:
        logger.warning(f"Failed to log section extraction call: {e}")

def finalize_conversation(
    conv_folder: str,
    sections: Dict[str, str],
    confidence_scores: Dict[str, float],
    stats: Dict[str, Any],
    extraction_method: str
):
    """Finalize the conversation by adding summary results."""
    try:
        if not conv_folder or not os.path.exists(conv_folder):
            return

        # Create final summary file
        summary_file = os.path.join(conv_folder, "final_results_summary.txt")

        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("FINAL EXTRACTION RESULTS SUMMARY\n")
            f.write("=" * 80 + "\n")
            f.write(f"Extraction Method: {extraction_method}\n")
            f.write(f"Completed: {datetime.now().isoformat()}\n")
            f.write(f"Processing Time: {stats.get('processing_time', 'N/A')} seconds\n")
            f.write(f"Total LLM Calls: {stats.get('total_calls', 'N/A')}\n")
            f.write(f"Overall Confidence: {stats.get('overall_confidence', 'N/A')}\n")
            f.write(f"Sections Found: {stats.get('sections_found', 'N/A')}\n")
            f.write("=" * 80 + "\n\n")

            # Section results summary
            f.write("SECTION EXTRACTION RESULTS:\n")
            f.write("-" * 40 + "\n")

            for section_name, content in sections.items():
                confidence = confidence_scores.get(section_name, 0.0)
                status = "✅ SUCCESS" if content and content.strip() and not content.startswith("ERROR:") and content != "NOT_FOUND" else "❌ FAILED"
                content_length = len(content) if content else 0

                f.write(f"{section_name.upper()}: {status}\n")
                f.write(f"  Confidence: {confidence:.2f}\n")
                f.write(f"  Content Length: {content_length} characters\n")
                if content and len(content) > 100:
                    f.write(f"  Preview: {content[:100]}...\n")
                elif content:
                    f.write(f"  Content: {content}\n")
                f.write("\n")

            f.write("=" * 80 + "\n")
            f.write("All individual LLM calls are logged in separate files in this folder.\n")
            f.write("Check the numbered call files for detailed prompt/response logs.\n")

        logger.info(f"Finalized conversation summary: {summary_file}")

    except Exception as e:
        logger.warning(f"Failed to finalize conversation: {e}")

def create_debug_filename(context: str, source_filename: str = None) -> str:
    """
    Create a meaningful debug filename based on the source file and context.
    Handles duplicate names with incremental numbering.
    """
    debug_folder = "debug_json_files"

    if source_filename:
        # Extract the base name without extension
        base_name = os.path.splitext(os.path.basename(source_filename))[0]
        # Get the file extension to include in the debug name
        file_ext = os.path.splitext(source_filename)[1].lower()

        # Clean the filename for safe filesystem usage
        import re
        safe_name = re.sub(r'[^\w\-_\.]', '_', base_name)

        # Create base debug filename
        if file_ext in ['.pdf', '.docx', '.doc', '.txt']:
            ext_name = file_ext[1:]  # Remove the dot
            base_debug_name = f"{safe_name}({ext_name})"
        else:
            base_debug_name = f"{safe_name}(unknown)"
    else:
        # Fallback to timestamp-based naming
        base_debug_name = f"malformed_{context}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # Check for existing files and add incremental numbers
    counter = 0
    debug_filename = f"{base_debug_name}.json"

    while os.path.exists(os.path.join(debug_folder, debug_filename)):
        counter += 1
        debug_filename = f"{base_debug_name}{counter}.json"

    return debug_filename

def fix_malformed_json(json_str: str, json_err: json.JSONDecodeError, context: str = "unknown", source_filename: str = None) -> Dict:
    """
    Advanced JSON fixing function with comprehensive debugging and repair strategies.
    """
    logger.error(f"JSON decode error in {context} at line {json_err.lineno}, column {json_err.colno}, char {json_err.pos}: {json_err.msg}")

    # Get the problematic line for debugging
    lines = json_str.splitlines()
    if json_err.lineno <= len(lines):
        error_line = lines[json_err.lineno - 1]
        logger.error(f"Error line content: {error_line}")

        # Show context around the error
        start_line = max(0, json_err.lineno - 3)
        end_line = min(len(lines), json_err.lineno + 2)
        logger.error("Context around error:")
        for i in range(start_line, end_line):
            marker = " >>> " if i == json_err.lineno - 1 else "     "
            logger.error(f"{marker}Line {i+1}: {lines[i]}")

    # Save the malformed JSON for debugging with meaningful filename
    debug_filename = create_debug_filename(context, source_filename)
    try:
        # Ensure debug folder exists
        debug_folder = "debug_json_files"
        os.makedirs(debug_folder, exist_ok=True)

        debug_filepath = os.path.join(debug_folder, debug_filename)
        with open(debug_filepath, 'w', encoding='utf-8') as f:
            f.write(json_str)
        logger.info(f"Saved malformed JSON to {debug_filepath} for debugging")
    except Exception as e:
        logger.warning(f"Could not save debug file: {e}")

    logger.info("Attempting LLM self-healing of malformed JSON...")

    # Strategy 1: LLM Self-Healing - Send broken JSON back to model for fixing
    if ENABLE_LLM_JSON_REPAIR:
        try:
            fixed_json = llm_fix_malformed_json(json_str, context)
            if fixed_json:
                parsed_data = json.loads(fixed_json)
                logger.info("✅ Successfully fixed JSON using LLM self-healing")
                return parsed_data
        except json.JSONDecodeError as e:
            logger.warning(f"LLM self-healing failed: {e}")
        except Exception as e:
            logger.warning(f"LLM self-healing error: {e}")
    else:
        logger.info("LLM self-healing is disabled")

    # Strategy 2: Advanced bracket/brace balancing
    logger.info("Attempting advanced bracket balancing...")
    try:
        fixed_json = balance_json_brackets(json_str)
        parsed_data = json.loads(fixed_json)
        logger.info("✅ Successfully fixed JSON with bracket balancing")
        return parsed_data
    except json.JSONDecodeError as e:
        logger.warning(f"Bracket balancing failed: {e}")

    # Strategy 3: Partial extraction with regex
    logger.info("Attempting partial data extraction...")
    fallback_data = extract_partial_json_data(json_str, context)

    return fallback_data

def llm_json_self_healing(malformed_output: str, context: str = "resume") -> str:
    """
    JSON Self-Healing System: Takes malformed LLM output and reformats it to exact schema.
    This is more intelligent than simple repair - it understands the data and restructures it.
    """

    prompt = f"""
CRITICAL INSTRUCTION: You must return ONLY a JSON object. NO markdown, NO code blocks, NO explanations.

You are a JSON formatting specialist. The data below contains resume information but may have formatting issues. Extract ALL the information and return it as a clean JSON object.

FORBIDDEN RESPONSES:
- Do NOT use ```json or ``` or any markdown
- Do NOT add explanations before or after the JSON
- Do NOT use code blocks or formatting
- Do NOT start with anything other than {{
- Do NOT end with anything other than }}

REQUIRED JSON SCHEMA (return exactly this structure):
{{
    "name": "string (full name of person)",
    "email": "string or null",
    "phone": "string or null",
    "education": [
        {{
            "degree": "string",
            "institution": "string",
            "year": "string"
        }}
    ],
    "skills": ["string", "string"],
    "experience": [
        {{
            "company_name": "string",
            "role": "string",
            "duration": "string",
            "key_responsibilities": "string"
        }}
    ],
    "projects": [
        {{
            "name": "string",
            "description": "string"
        }}
    ],
    "certifications": ["string"],
    "domain_of_interest": ["string"],
    "languages_known": ["string"],
    "achievements": ["string"],
    "publications": ["string"],
    "volunteer_experience": ["string"],
    "references": ["string"],
    "summary": "string or null",
    "personal_projects": ["string"],
    "social_media": ["string"]
}}

RULES:
1. Extract ALL information from the input data
2. Use empty arrays [] for missing sections
3. Remove any markdown formatting from the input
4. Preserve all actual data content
5. Return ONLY the JSON object

INPUT DATA TO REFORMAT:
{malformed_output}

JSON object (start with {{ immediately):
"""

    try:
        response = get_response(
            prompt,
            timeout_seconds=JSON_REPAIR_TIMEOUT,
            max_tokens=JSON_REPAIR_MAX_TOKENS,
            endpoint="json_self_healing",
            context="malformed_output",
            call_type="reformat"
        )

        if response and response.strip():
            return response.strip()
        else:
            logger.error("JSON self-healing returned empty response")
            return None

    except Exception as e:
        logger.error(f"JSON self-healing failed: {e}")
        return None

def llm_fix_malformed_json(malformed_json: str, context: str = "unknown") -> str:
    """
    Use the LLM itself to fix malformed JSON by sending it back with specific repair instructions.
    """
    logger.info(f"Sending malformed {context} JSON back to LLM for self-healing")

    # Create a focused prompt for JSON repair that preserves all original data

    repair_prompt = f"""You are a JSON repair specialist. Your task is to fix the malformed JSON below and return ONLY the repaired valid JSON.

BROKEN JSON TO FIX:
{malformed_json}

REPAIR INSTRUCTIONS:
1. Fix all JSON syntax errors (missing commas, unescaped quotes, bracket mismatches)
2. Ensure all string values are properly quoted and escaped
3. Remove any trailing commas before closing brackets/braces
4. Add missing commas between array elements and object properties
5. Balance all opening and closing brackets/braces
6. Preserve ALL the original data content exactly as it appears
7. Do NOT change any field names, values, or structure
8. Do NOT add new fields or remove existing fields
9. ONLY fix the syntax errors to make it valid JSON

CRITICAL OUTPUT RULES:
- Return ONLY the fixed JSON with the SAME data
- Do NOT add any explanations, comments, or extra text
- Do NOT use markdown code blocks, backticks, or any formatting
- Do NOT start with ```json or end with ```
- Do NOT add any text before or after the JSON
- Keep all the original field names and values exactly the same
- Only fix syntax errors like missing commas, brackets, quotes
- The output should contain the SAME information as the input, just with correct JSON syntax
- Start directly with {{ and end directly with }}

Return ONLY the fixed JSON (no markdown, no explanations):"""

    try:
        # Use configured timeout and max tokens for JSON repair
        response = get_response(
            repair_prompt,
            timeout_seconds=JSON_REPAIR_TIMEOUT,
            max_tokens=JSON_REPAIR_MAX_TOKENS,
            endpoint="json_repair",
            context="malformed_json",
            call_type="repair"
        )

        # Clean the response
        fixed_json = response.strip()

        # Remove any markdown formatting that might have been added
        if "```json" in fixed_json:
            fixed_json = fixed_json.split("```json")[1].split("```")[0].strip()
        elif "```" in fixed_json:
            fixed_json = fixed_json.split("```")[1].split("```")[0].strip()

        # Remove any leading/trailing text that's not JSON
        if fixed_json.find('{') >= 0 and fixed_json.rfind('}') >= 0:
            start = fixed_json.find('{')
            end = fixed_json.rfind('}') + 1
            fixed_json = fixed_json[start:end]

        # Validate that it's actually valid JSON
        try:
            json.loads(fixed_json)
            logger.info(f"LLM successfully repaired {context} JSON (length: {len(fixed_json)})")
            return fixed_json
        except json.JSONDecodeError as e:
            logger.warning(f"LLM repair produced invalid JSON: {e}")
            return None

    except Exception as e:
        logger.error(f"Error during LLM JSON repair: {e}")
        return None

def balance_json_brackets(json_str: str) -> str:
    """
    Balance brackets and braces in JSON string.
    """
    # Count brackets and braces
    open_braces = json_str.count('{')
    close_braces = json_str.count('}')
    open_brackets = json_str.count('[')
    close_brackets = json_str.count(']')

    # Add missing closing brackets/braces
    if open_braces > close_braces:
        json_str += '}' * (open_braces - close_braces)

    if open_brackets > close_brackets:
        json_str += ']' * (open_brackets - close_brackets)

    # Remove extra closing brackets/braces from the end
    while json_str.endswith('}}') and json_str.count('}') > json_str.count('{'):
        json_str = json_str[:-1]

    while json_str.endswith(']]') and json_str.count(']') > json_str.count('['):
        json_str = json_str[:-1]

    return json_str

def extract_partial_json_data(json_str: str, context: str) -> Dict:
    """
    Extract partial data from malformed JSON using regex patterns.
    """
    logger.info(f"Extracting partial data from malformed {context} JSON")

    # Base fallback structure
    if context == "resume":
        fallback_data = {
            "name": "Unknown",
            "email": None,
            "phone": None,
            "education": [],
            "highest_education": None,
            "skills": [],
            "experience": [],
            "projects": [],
            "certifications": [],
            "domain_of_interest": [],
            "languages_known": [],
            "social_media": [],
            "achievements": [],
            "publications": [],
            "volunteer_experience": [],
            "references": [],
            "summary": None,
            "error": "Failed to parse resume data due to JSON formatting issues",
            "raw_json_length": len(json_str)
        }
    elif context == "job_description":
        fallback_data = {
            "job_title": "Unknown",
            "company_name": None,
            "location": None,
            "job_type": None,
            "work_mode": None,
            "department": None,
            "summary": None,
            "responsibilities": [],
            "required_skills": [],
            "preferred_skills": [],
            "required_experience": None,
            "education_requirements": [],
            "salary_range": None,
            "benefits": [],
            "requirements": [],
            "application_deadline": None,
            "posting_date": None,
            "industry": None,
            "career_level": None,
            "error": "Failed to parse job description data due to JSON formatting issues",
            "raw_json_length": len(json_str)
        }
    else:
        fallback_data = {
            "error": f"Failed to parse {context} data due to JSON formatting issues",
            "raw_json_length": len(json_str)
        }

    # Extract basic string fields based on context
    if context == "resume":
        string_patterns = {
            "name": r'"name"\s*:\s*"([^"]+)"',
            "email": r'"email"\s*:\s*"([^"]+@[^"]+)"',
            "phone": r'"phone"\s*:\s*"([^"]+)"',
            "summary": r'"summary"\s*:\s*"([^"]+)"'
        }
        array_patterns = {
            "skills": r'"skills"\s*:\s*\[(.*?)\]',
            "education": r'"education"\s*:\s*\[(.*?)\]',
            "experience": r'"experience"\s*:\s*\[(.*?)\]'
        }
    elif context == "job_description":
        string_patterns = {
            "job_title": r'"job_title"\s*:\s*"([^"]+)"',
            "company_name": r'"company_name"\s*:\s*"([^"]+)"',
            "location": r'"location"\s*:\s*"([^"]+)"',
            "summary": r'"summary"\s*:\s*"([^"]+)"',
            "required_experience": r'"required_experience"\s*:\s*"([^"]+)"'
        }
        array_patterns = {
            "required_skills": r'"required_skills"\s*:\s*\[(.*?)\]',
            "preferred_skills": r'"preferred_skills"\s*:\s*\[(.*?)\]',
            "responsibilities": r'"responsibilities"\s*:\s*\[(.*?)\]',
            "education_requirements": r'"education_requirements"\s*:\s*\[(.*?)\]'
        }
    else:
        string_patterns = {}
        array_patterns = {}

    # Extract string fields
    for field, pattern in string_patterns.items():
        match = re.search(pattern, json_str, re.IGNORECASE)
        if match and field in fallback_data:
            fallback_data[field] = match.group(1)
            logger.info(f"Extracted {field}: {match.group(1)[:50]}...")

    # Extract array fields
    for field, pattern in array_patterns.items():
        match = re.search(pattern, json_str, re.DOTALL | re.IGNORECASE)
        if match and field in fallback_data:
            array_content = match.group(1)
            if field in ["skills", "required_skills", "preferred_skills"]:
                # Extract skills as simple strings
                skills = re.findall(r'"([^"]+)"', array_content)
                if skills:
                    fallback_data[field] = skills[:20]  # Limit to 20 skills
                    logger.info(f"Extracted {len(skills)} {field}")
            elif field in ["responsibilities", "education_requirements"]:
                # Extract responsibilities/requirements as simple strings
                items = re.findall(r'"([^"]+)"', array_content)
                if items:
                    fallback_data[field] = items[:10]  # Limit to 10 items
                    logger.info(f"Extracted {len(items)} {field}")
            else:
                # For complex arrays, try to count objects
                object_count = array_content.count('{')
                if object_count > 0:
                    fallback_data[field] = [{"extracted": f"Found {object_count} items but could not parse"}]
                    logger.info(f"Found {object_count} {field} items")

    logger.info(f"Created fallback data structure with partial information")
    return fallback_data

def parse_resume_with_gemma(resume_text: str, source_filename: str = None) -> Dict:
    """Parse resume text using the Gemma model."""
    logger.info("Parsing resume with Gemma model")

    prompt = f"""
    You are an expert resume parser. Your task is to extract ALL structured information from the resume text below.

    Follow these guidelines:
    1. Extract ALL information that is explicitly mentioned in the resume text.
    2. Format your response as a valid JSON object with EXACTLY the following structure:

    {{
        "name": "Full Name",
        "email": "<EMAIL>" or null,
        "phone": "+1234567890" or null,
        "education": [
            {{
                "degree": "Full Degree Name (Including Specialization)",
                "institution": "Institution Name",
                "year": "Year or Date Range"
            }}
        ],
        "skills": ["Skill 1", "Skill 2", "Skill 3", ...],
        "experience": [
            {{
                "company_name": "Company Name with Location if mentioned",
                "role": "Job Title",
                "duration": "Date Range",
                "key_responsibilities": "Detailed description of responsibilities and achievements"
            }}
        ],
        "projects": [
            {{
                "name": "Project Name",
                "description": "Detailed project description including technologies used"
            }}
        ],
        "certifications": [],
        "domain_of_interest": ["Interest 1", "Interest 2", ...],
        "languages_known": ["Language 1", "Language 2", ...],
        "achievements": [],
        "publications": [],
        "volunteer_experience": [],
        "references": [],
        "summary": null,
        "personal_projects": [],
        "social_media": ["platform1.com/username", "platform2.com/username"]
    }}

    3. For arrays, if no information is available, use an empty array []
    4. For string fields, if no information is available, use null
    5. Do not make up or infer information that is not explicitly stated in the resume
    6. Ensure the JSON is properly formatted and valid
    7. IMPORTANT: Keep skills as a simple array of strings, not as objects or dictionaries
    8. IMPORTANT: For experience entries, include all details in the key_responsibilities field as a single string with line breaks (\n)
    9. IMPORTANT: For projects, include all details in the description field as a single string with line breaks (\n)
    10. IMPORTANT: Make sure all JSON is valid - check for missing commas, extra commas, proper quotes, and proper nesting of objects and arrays

    Resume text:
    {resume_text}

    Respond ONLY with the JSON object, nothing else. Do not include explanations, markdown formatting, or code blocks.
    """

    try:
        # Use a 120 second timeout for resume parsing
        response = get_response(
            prompt,
            timeout_seconds=120,
            endpoint="/resume",
            context=source_filename or "unknown_resume",
            call_type="main"
        )

        # Log the raw response for debugging
        logger.info(f"Raw response length: {len(response)}")
        if len(response) < 500:
            logger.info(f"Raw response content: {response}")
        else:
            logger.info(f"Raw response preview: {response[:500]}...")

        # Try to extract JSON from the response
        json_str = response.strip()

        # Remove any markdown formatting if present
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()

        # Try to find the JSON object if there's additional text
        if json_str.find('{') >= 0 and json_str.rfind('}') >= 0:
            start = json_str.find('{')
            end = json_str.rfind('}') + 1
            json_str = json_str[start:end]

        # Log the extracted JSON string for debugging
        logger.info(f"Extracted JSON string length: {len(json_str)}")
        if len(json_str) < 500:
            logger.info(f"Extracted JSON string: {json_str}")
        else:
            logger.info(f"Extracted JSON string preview: {json_str[:500]}...")

        # Try to parse the JSON string into a Python dictionary
        try:
            parsed_data = json.loads(json_str)
            return parsed_data
        except json.JSONDecodeError as json_err:
            # Enhanced JSON debugging and fixing
            return fix_malformed_json(json_str, json_err, "resume", source_filename)

    except Exception as e:
        logger.error(f"Error in Gemma parsing: {e}")
        # Return a minimal valid structure instead of raising an exception
        return {
            "name": "Unknown",
            "email": None,
            "phone": None,
            "education": [],
            "highest_education": None,
            "skills": [],
            "experience": [],
            "projects": [],
            "certifications": [],
            "domain_of_interest": [],
            "languages_known": [],
            "social_media": [],
            "achievements": [],
            "publications": [],
            "volunteer_experience": [],
            "references": [],
            "summary": None,
            "error": "Failed to parse resume data",
            "details": str(e)
        }


def parse_jd_with_gemma(jd_text: str, source_filename: str = None) -> Dict:
    """Parse job description text using the Gemma model."""
    logger.info("Parsing job description with Gemma model")

    prompt = f"""
    You are an expert job description parser. Your task is to extract ALL structured information from the job description text below.

    Follow these guidelines:
    1. Extract ALL information that is explicitly mentioned in the job description text.
    2. Format your response as a valid JSON object with EXACTLY the following structure:

    {{
        "job_title": "Full Job Title",
        "company_name": "Company Name" or null,
        "location": "Job Location" or null,
        "job_type": "Full-time/Part-time/Contract/etc." or null,
        "work_mode": "Remote/Hybrid/On-site" or null,
        "department": "Department Name" or null,
        "summary": "Brief job summary or overview" or null,
        "responsibilities": [
            "Responsibility 1",
            "Responsibility 2",
            ...
        ],
        "required_skills": [
            "Required Skill 1",
            "Required Skill 2",
            ...
        ],
        "preferred_skills": [
            "Preferred Skill 1",
            "Preferred Skill 2",
            ...
        ],
        "required_experience": "Experience requirement (e.g., '3+ years')" or null,
        "education_requirements": [
            "Education Requirement 1",
            "Education Requirement 2",
            ...
        ],
        "education_details": {{
            "degree_level": "Bachelor's/Master's/PhD/etc." or null,
            "field_of_study": "Computer Science/Engineering/etc." or null,
            "is_required": true or false,
            "alternatives": "Alternative education paths if mentioned" or null
        }},
        "salary_range": "Salary information if mentioned" or null,
        "benefits": [
            {{
                "title": "Benefit Title",
                "description": "Benefit Description" or null
            }},
            ...
        ],
        "requirements": [
            {{
                "title": "Requirement Title",
                "description": "Requirement Description" or null,
                "is_mandatory": true or false
            }},
            ...
        ],
        "application_deadline": "Application deadline if mentioned" or null,
        "posting_date": "Job posting date if mentioned" or null,
        "industry": "Industry type if mentioned" or null,
        "career_level": "Entry/Mid/Senior level if mentioned" or null
    }}

    3. For arrays, if no information is available, use an empty array []
    4. For string fields, if no information is available, use null
    5. Do not make up or infer information that is not explicitly stated in the job description
    6. Ensure the JSON is properly formatted and valid
    7. IMPORTANT: Distinguish between required skills and preferred/nice-to-have skills
    8. IMPORTANT: For responsibilities and skills, list each item separately in the array
    9. IMPORTANT: If years of experience are mentioned for specific skills, include that in the skill description
    10. IMPORTANT: Make sure all JSON is valid - check for missing commas, extra commas, proper quotes, and proper nesting of objects and arrays
    11. IMPORTANT: Be thorough in extracting ALL skills mentioned in the job description, even if they are embedded in paragraphs
    12. IMPORTANT: For education requirements, be comprehensive and include both degree levels (Bachelor's, Master's, etc.) and fields of study (Computer Science, Engineering, etc.)
    13. IMPORTANT: Pay special attention to abbreviations like CSE, IT, AIDA, etc. and include them in the appropriate fields

    Job Description text:
    {jd_text}

    Respond ONLY with the JSON object, nothing else. Do not include explanations, markdown formatting, or code blocks.
    """

    try:
        # Use a 120 second timeout for JD parsing
        response = get_response(
            prompt,
            timeout_seconds=120,
            endpoint="/jd_parser",
            context=source_filename or "unknown_jd",
            call_type="main"
        )

        # Log the raw response for debugging
        logger.info(f"Raw JD response length: {len(response)}")
        if len(response) < 500:
            logger.info(f"Raw JD response content: {response}")
        else:
            logger.info(f"Raw JD response preview: {response[:500]}...")

        # Try to extract JSON from the response
        json_str = response.strip()

        # Remove any markdown formatting if present
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()

        # Try to find the JSON object if there's additional text
        if json_str.find('{') >= 0 and json_str.rfind('}') >= 0:
            start = json_str.find('{')
            end = json_str.rfind('}') + 1
            json_str = json_str[start:end]

        # Log the extracted JSON string for debugging
        logger.info(f"Extracted JD JSON string length: {len(json_str)}")
        if len(json_str) < 500:
            logger.info(f"Extracted JD JSON string: {json_str}")
        else:
            logger.info(f"Extracted JD JSON string preview: {json_str[:500]}...")

        # Try to parse the JSON string into a Python dictionary
        try:
            parsed_data = json.loads(json_str)
            return parsed_data
        except json.JSONDecodeError as json_err:
            # Enhanced JSON debugging and fixing for JD
            return fix_malformed_json(json_str, json_err, "job_description", source_filename)

    except Exception as e:
        logger.error(f"Error in JD parsing: {e}")
        # Return a minimal valid structure instead of raising an exception
        return {
            "job_title": "Unknown",
            "company_name": None,
            "location": None,
            "job_type": None,
            "work_mode": None,
            "department": None,
            "summary": None,
            "responsibilities": [],
            "required_skills": [],
            "preferred_skills": [],
            "required_experience": None,
            "education_requirements": [],
            "education_details": {
                "degree_level": None,
                "field_of_study": None,
                "is_required": True,
                "alternatives": None
            },
            "salary_range": None,
            "benefits": [],
            "requirements": [],
            "application_deadline": None,
            "posting_date": None,
            "industry": None,
            "career_level": None,
            "error": "Failed to parse job description data",
            "details": str(e)
        }



def normalize_resume_data(parsed_data: Dict, convert_skills_to_dict_format: bool = False) -> Dict:
    """Normalize parsed resume data to a consistent schema."""
    logger.info("Normalizing resume data to consistent schema")

    # Define the fixed schema fields
    normalized_data = {
        "name": "",
        "email": None,
        "phone": None,
        "education": [],
        "highest_education": None,
        "skills": [] if not convert_skills_to_dict_format else {},
        "experience": [],
        "projects": [],
        "certifications": [],
        "domain_of_interest": [],
        "languages_known": [],
        "social_media": [],
        "achievements": [],
        "publications": [],
        "volunteer_experience": [],
        "references": [],
        "summary": None,
        "confidence_score": 0.0,
        "confidence_details": {}
    }

    # Copy data from parsed_data to normalized_data for fields that exist in both
    for field in normalized_data.keys():
        if field in parsed_data and field not in ["confidence_score", "confidence_details"]:
            # Special handling for skills if converting to dictionary
            if field == "skills" and convert_skills_to_dict_format:
                if isinstance(parsed_data[field], list):
                    # Convert skills list to dictionary
                    skills_dict = convert_skills_to_dict({
                        "skills": parsed_data[field],
                        "experience": parsed_data.get("experience", []),
                        "projects": parsed_data.get("projects", [])
                    })["skills"]
                    normalized_data[field] = skills_dict
                elif isinstance(parsed_data[field], dict):
                    normalized_data[field] = parsed_data[field]
            else:
                normalized_data[field] = parsed_data[field]

    # Handle special case for languages vs languages_known
    if "languages" in parsed_data and "languages_known" not in parsed_data:
        normalized_data["languages_known"] = parsed_data["languages"]

    # Ensure name is a string
    if normalized_data["name"] is None:
        normalized_data["name"] = ""

    # Calculate confidence score
    confidence_score = calculate_resume_confidence_score(normalized_data)
    normalized_data["confidence_score"] = confidence_score

    return normalized_data

def normalize_jd_data(parsed_data: Dict) -> Dict:
    """Normalize parsed job description data to a consistent schema."""
    logger.info("Normalizing job description data to consistent schema")

    # Define the fixed schema fields
    normalized_data = {
        "job_title": "",
        "company_name": None,
        "location": None,
        "job_type": None,
        "work_mode": None,
        "department": None,
        "summary": None,
        "responsibilities": [],
        "required_skills": [],
        "preferred_skills": [],
        "required_experience": None,
        "education_requirements": [],
        "education_details": {
            "degree_level": None,
            "field_of_study": None,
            "is_required": True,
            "alternatives": None
        },
        "salary_range": None,
        "benefits": [],
        "requirements": [],
        "application_deadline": None,
        "posting_date": None,
        "contact_information": None,
        "company_description": None,
        "industry": None,
        "career_level": None,
        "confidence_score": 0.0,
        "confidence_details": {}
    }

    # Copy data from parsed_data to normalized_data for fields that exist in both
    for field in normalized_data.keys():
        if field in parsed_data and field not in ["confidence_score", "confidence_details"]:
            normalized_data[field] = parsed_data[field]

    # Ensure job_title is a string
    if normalized_data["job_title"] is None:
        normalized_data["job_title"] = ""

    # Calculate confidence score
    confidence_score = calculate_jd_confidence_score(normalized_data)
    normalized_data["confidence_score"] = confidence_score

    return normalized_data


def parse_call_summary_with_gemma(summary_text: str, request_metrics: RequestMetrics = None) -> Dict:
    """Parse call summary text using the Gemma model to extract interview information."""
    logger.info("Parsing call summary with Gemma model")

    prompt = f"""
    You are an expert at extracting structured information from call summaries or transcripts. Your task is to extract specific interview-related information from the call summary below.

    Extract the following information if mentioned in the summary. If any information is NOT mentioned or cannot be determined, return null for that field:

    1. offer_in_hand: Any current job offer amount (as a number, no currency symbols)
    2. notice_period: Current notice period (as a string, e.g., "2 months", "30 days", "immediate")
    3. expected_salary: Expected or desired salary (as a number, no currency symbols)
    4. reason_to_switch: Main reason for wanting to change jobs (as a string)
    5. preferred_time_for_interview: Preferred time for interview (as a string, e.g., "10:00 AM", "morning", "afternoon")
    6. preferred_date_for_interview: Preferred date for interview (as a string in YYYY-MM-DD format if specific date mentioned, or general preference like "next week")

    IMPORTANT RULES:
    - Only extract information that is explicitly mentioned in the summary
    - If information is not mentioned, use null (not empty string or placeholder)
    - For salary amounts, extract only the numeric value (e.g., if "1 lakh" is mentioned, return 100000)
    - Be precise and conservative - don't infer information that isn't clearly stated
    - Return the response as a valid JSON object only

    Call Summary:
    {summary_text}

    Return only a JSON object with the extracted information:
    """

    try:
        # Get response from the model
        response = get_response(
            prompt,
            timeout_seconds=60,
            max_tokens=500,
            request_metrics=request_metrics,
            endpoint="/interfix",
            context="vapi_summary",
            call_type="main"
        )

        # Clean the response to extract JSON
        response = response.strip()

        # Remove any markdown formatting if present
        if response.startswith("```json"):
            response = response[7:]
        if response.startswith("```"):
            response = response[3:]
        if response.endswith("```"):
            response = response[:-3]

        response = response.strip()

        # Parse the JSON response
        try:
            parsed_data = json.loads(response)
            logger.info("Successfully parsed call summary")
            return parsed_data
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {response}")
            # Return empty structure if JSON parsing fails
            return {
                "offer_in_hand": None,
                "notice_period": None,
                "expected_salary": None,
                "reason_to_switch": None,
                "preferred_time_for_interview": None,
                "preferred_date_for_interview": None
            }

    except Exception as e:
        logger.error(f"Error in call summary parsing: {e}")
        # Return empty structure if any error occurs
        return {
            "offer_in_hand": None,
            "notice_period": None,
            "expected_salary": None,
            "reason_to_switch": None,
            "preferred_time_for_interview": None,
            "preferred_date_for_interview": None
        }


def parse_resume(resume_text: str, convert_skills_to_dict_format: bool = False, source_filename: str = None) -> Dict:
    """Use Gemma model to parse resume text."""
    logger.info("Starting resume parsing")

    try:
        # Use the primary LLM-based parsing method
        logger.info("Parsing resume with Gemma model")
        result = parse_resume_with_gemma(resume_text, source_filename)

        # Check if we got a valid result
        if not result or not isinstance(result, dict):
            logger.error(f"Invalid result from parse_resume_with_gemma: {type(result)}")
            raise ValueError("Invalid result format from resume parser")

        # Check if the result contains an error field
        if "error" in result:
            logger.warning(f"Resume parsing returned an error: {result.get('error')}")
            # We'll still normalize it to ensure consistent schema

        # Normalize the result to ensure consistent schema
        normalized_result = normalize_resume_data(result, convert_skills_to_dict_format)

        # Add a message if there was an error in parsing
        if "error" in result:
            normalized_result["error"] = result["error"]
            if "details" in result:
                normalized_result["details"] = result["details"]

        return normalized_result

    except Exception as e:
        logger.error(f"Error in resume parsing: {e}")

        # Return a minimal structure if parsing fails
        result = {
            "error": "Failed to parse resume",
            "details": str(e),
            "name": "Unknown",
            "email": None,
            "phone": None,
            "skills": [] if not convert_skills_to_dict_format else {},
            "education": [],
            "highest_education": None,
            "experience": [],
        }

        # Normalize the result to ensure consistent schema
        normalized_result = normalize_resume_data(result, convert_skills_to_dict_format)

        return normalized_result


def extract_skills_from_text(text: str) -> List[str]:
    """Extract skills from text using pattern matching and common skill keywords."""
    logger.info("Attempting fallback skills extraction from text")

    # Common technical skills to look for
    common_skills = [
        "Python", "Java", "JavaScript", "C++", "C#", "Ruby", "PHP", "Swift", "Kotlin", "Go", "Rust",
        "HTML", "CSS", "SQL", "NoSQL", "MongoDB", "MySQL", "PostgreSQL", "Oracle", "Redis",
        "React", "Angular", "Vue", "Node.js", "Express", "Django", "Flask", "Spring", "ASP.NET",
        "AWS", "Azure", "GCP", "Docker", "Kubernetes", "Jenkins", "Git", "GitHub", "GitLab",
        "Machine Learning", "Deep Learning", "AI", "Data Science", "TensorFlow", "PyTorch", "Keras",
        "Big Data", "Hadoop", "Spark", "Kafka", "Airflow", "ETL", "Data Mining", "Data Analysis",
        "DevOps", "CI/CD", "Agile", "Scrum", "Kanban", "Jira", "Confluence", "REST API", "GraphQL",
        "Microservices", "Serverless", "Linux", "Unix", "Windows", "MacOS", "Android", "iOS",
        "Testing", "QA", "Selenium", "JUnit", "TestNG", "Cypress", "Jest", "Mocha", "Chai",
        "UI/UX", "Figma", "Sketch", "Adobe XD", "Photoshop", "Illustrator", "InDesign",
        "Project Management", "Product Management", "Scrum Master", "Product Owner",
        "Communication", "Leadership", "Teamwork", "Problem Solving", "Critical Thinking",
        "Time Management", "Adaptability", "Creativity", "Attention to Detail", "Analytical Skills"
    ]

    # Extract skills using pattern matching
    extracted_skills = []

    # Convert text to lowercase for case-insensitive matching
    text_lower = text.lower()

    # Look for skills in the text
    for skill in common_skills:
        if skill.lower() in text_lower:
            # Check if it's a standalone word or part of a phrase
            # This helps avoid false positives like "go" matching in "going"
            if len(skill) <= 2:  # For very short skills like "Go", "C#", etc.
                # Look for word boundaries or specific contexts
                if re.search(r'\b' + re.escape(skill.lower()) + r'\b', text_lower) or \
                   re.search(r'skills?.*' + re.escape(skill.lower()), text_lower) or \
                   re.search(r'technologies?.*' + re.escape(skill.lower()), text_lower) or \
                   re.search(r'experience.*' + re.escape(skill.lower()), text_lower):
                    extracted_skills.append(skill)
            else:
                extracted_skills.append(skill)

    # Look for skills mentioned in specific contexts
    skill_contexts = [
        r'proficient in\s+([A-Za-z0-9+#/\s]+)',
        r'experience with\s+([A-Za-z0-9+#/\s]+)',
        r'knowledge of\s+([A-Za-z0-9+#/\s]+)',
        r'familiar with\s+([A-Za-z0-9+#/\s]+)',
        r'skills:?\s*([A-Za-z0-9+#/\s,]+)',
        r'technologies:?\s*([A-Za-z0-9+#/\s,]+)',
        r'requirements:?\s*([A-Za-z0-9+#/\s,]+)'
    ]

    for pattern in skill_contexts:
        matches = re.finditer(pattern, text_lower)
        for match in matches:
            skill_text = match.group(1).strip()
            # Split by common separators
            for skill in re.split(r'[,;/|]|\sand\s', skill_text):
                skill = skill.strip()
                if skill and len(skill) > 2 and skill not in [s.lower() for s in extracted_skills]:
                    # Capitalize the first letter of each word
                    formatted_skill = ' '.join(word.capitalize() for word in skill.split())
                    extracted_skills.append(formatted_skill)

    # Remove duplicates while preserving order
    unique_skills = []
    for skill in extracted_skills:
        if skill not in unique_skills:
            unique_skills.append(skill)

    logger.info(f"Fallback extraction found {len(unique_skills)} skills")
    return unique_skills


def extract_education_from_text(text: str) -> List[str]:
    """Extract education requirements from text using pattern matching."""
    logger.info("Attempting fallback education extraction from text")

    # Common education patterns to look for
    education_patterns = [
        r"(?:bachelor|master|phd|doctorate|bs|ba|ms|ma|b\.s\.|b\.a\.|m\.s\.|m\.a\.|ph\.d\.)['']?s?\s+(?:degree|in)\s+([A-Za-z\s]+)",
        r"degree\s+(?:in|required)?\s+([A-Za-z\s]+)",
        r"(?:bachelor|master|phd|doctorate|bs|ba|ms|ma|b\.s\.|b\.a\.|m\.s\.|m\.a\.|ph\.d\.)['']?s?\s+(?:or equivalent)",
        r"(?:bachelor|master|phd|doctorate|bs|ba|ms|ma|b\.s\.|b\.a\.|m\.s\.|m\.a\.|ph\.d\.)['']?s?(?:\s+degree)?",
        r"education:?\s*([A-Za-z0-9+#/\s,]+)",
        r"qualifications:?\s*([A-Za-z0-9+#/\s,]+)"
    ]

    # Extract education requirements using pattern matching
    extracted_education = []

    # Convert text to lowercase for case-insensitive matching
    text_lower = text.lower()

    # Look for education requirements in the text
    for pattern in education_patterns:
        matches = re.finditer(pattern, text_lower)
        for match in matches:
            if len(match.groups()) > 0:
                edu_text = match.group(0).strip()
            else:
                edu_text = match.group(0).strip()

            # Clean up the text
            edu_text = re.sub(r'\s+', ' ', edu_text)

            if edu_text and edu_text not in [e.lower() for e in extracted_education]:
                # Capitalize the first letter of each word
                formatted_edu = ' '.join(word.capitalize() for word in edu_text.split())
                extracted_education.append(formatted_edu)

    # Check for specific degree mentions
    degree_types = [
        "Bachelor's degree", "Master's degree", "PhD", "Doctorate",
        "BS", "BA", "MS", "MA", "B.S.", "B.A.", "M.S.", "M.A.", "Ph.D."
    ]

    for degree in degree_types:
        if degree.lower() in text_lower and degree not in extracted_education:
            extracted_education.append(degree)

    # Check for specific field mentions
    fields = [
        "Computer Science", "Engineering", "Information Technology",
        "Business", "Mathematics", "Statistics", "Data Science"
    ]

    for field in fields:
        field_pattern = f"{field.lower()}"
        if field_pattern in text_lower:
            # Check if this field is already part of an extracted education requirement
            if not any(field.lower() in edu.lower() for edu in extracted_education):
                # Add as a standalone field
                extracted_education.append(field)

    # Remove duplicates while preserving order
    unique_education = []
    for edu in extracted_education:
        if edu not in unique_education:
            unique_education.append(edu)

    logger.info(f"Fallback extraction found {len(unique_education)} education requirements")
    return unique_education


def parse_jd(jd_text: str, source_filename: str = None) -> Dict:
    """Use Gemma model to parse job description text."""
    logger.info("Starting job description parsing")

    try:
        # Use the primary LLM-based parsing method
        logger.info("Parsing job description with Gemma model")
        result = parse_jd_with_gemma(jd_text, source_filename)

        # Check if we got a valid result
        if not result or not isinstance(result, dict):
            logger.error(f"Invalid result from parse_jd_with_gemma: {type(result)}")
            raise ValueError("Invalid result format from JD parser")

        # Check if the result contains an error field
        if "error" in result:
            logger.warning(f"JD parsing returned an error: {result.get('error')}")
            # We'll still normalize it to ensure consistent schema

        # Normalize the result to ensure consistent schema
        normalized_result = normalize_jd_data(result)

        # Fallback mechanism for skills extraction if no skills were found
        if not normalized_result["required_skills"]:
            logger.warning("No required skills found in JD, attempting fallback extraction")
            extracted_skills = extract_skills_from_text(jd_text)
            if extracted_skills:
                logger.info(f"Fallback extraction found {len(extracted_skills)} skills")
                normalized_result["required_skills"] = extracted_skills

        # Fallback mechanism for education requirements if none were found
        if not normalized_result["education_requirements"] and not normalized_result["education_details"]["degree_level"]:
            logger.warning("No education requirements found in JD, attempting fallback extraction")
            extracted_education = extract_education_from_text(jd_text)
            if extracted_education:
                logger.info(f"Fallback extraction found education requirements: {extracted_education}")
                normalized_result["education_requirements"] = extracted_education

                # Try to extract degree level and field
                for req in extracted_education:
                    if "bachelor" in req.lower() or "b." in req.lower() or "bs" in req.lower() or "ba" in req.lower():
                        normalized_result["education_details"]["degree_level"] = "Bachelor's degree"
                        # Try to extract field
                        if "comput" in req.lower() or "cs" in req.lower() or "cse" in req.lower() or "software" in req.lower():
                            normalized_result["education_details"]["field_of_study"] = "Computer Science"
                        elif "engineer" in req.lower():
                            normalized_result["education_details"]["field_of_study"] = "Engineering"
                        break
                    elif "master" in req.lower() or "m." in req.lower() or "ms" in req.lower() or "ma" in req.lower():
                        normalized_result["education_details"]["degree_level"] = "Master's degree"
                        break

        # Add a message if there was an error in parsing
        if "error" in result:
            normalized_result["error"] = result["error"]
            if "details" in result:
                normalized_result["details"] = result["details"]

        return normalized_result

    except Exception as e:
        logger.error(f"Error in job description parsing: {e}")

        # Return a minimal structure if parsing fails
        result = {
            "error": "Failed to parse job description",
            "details": str(e),
            "job_title": "Unknown",
            "required_skills": [],
            "preferred_skills": [],
            "responsibilities": [],
            "education_requirements": [],
            "education_details": {
                "degree_level": None,
                "field_of_study": None,
                "is_required": True,
                "alternatives": None
            }
        }

        # Normalize the result to ensure consistent schema
        normalized_result = normalize_jd_data(result)

        return normalized_result

@app.get("/")
async def root():
    return {
        "message": "Welcome to the Gemma3 API",
        "endpoints": [
            {
                "path": "/generate",
                "method": "POST",
                "description": "Generate a response from the Gemma model",
                "body": {"prompt": "string", "history": "string (optional)"}
            },
            {
                "path": "/generate_with_image",
                "method": "POST",
                "description": "Generate a response based on an image and text prompt",
                "body": "Upload an image file and provide a text prompt"
            },
            {
                "path": "/resume",
                "method": "POST",
                "description": "Parse a resume (PDF or DOCX) and extract ALL structured information with skills as a dictionary",
                "body": "Upload a PDF or DOCX file"
            },
            {
                "path": "/jd_parser",
                "method": "POST",
                "description": "Parse a job description (PDF or DOCX) and extract structured information in JSON format",
                "body": "Upload a PDF or DOCX file"
            },
            {
                "path": "/jd",
                "method": "POST",
                "description": "Generate interview questions based on a job description and resume data",
                "body": "Upload a JD file (PDF/DOCX) and provide question scales and resume data as JSON"
            },
            {
                "path": "/jd_only",
                "method": "POST",
                "description": "Generate interview questions based only on a job description (no resume data needed)",
                "body": "Upload a JD file (PDF/DOCX)"
            },
            {
                "path": "/intervet",
                "method": "POST",
                "description": "Evaluate how well a candidate's resume matches a job description",
                "body": {"resume_json": "Resume data from /resume endpoint", "jd_json": "JD data from /jd_parser endpoint"}
            },
            {
                "path": "/intervet2",
                "method": "POST",
                "description": "Evaluate how well a candidate's resume matches a job description using file uploads",
                "body": "Upload a resume file (PDF/DOCX) and a job description file (PDF/DOCX)"
            },
            {
                "path": "/intervet_new",
                "method": "POST",
                "description": "CGPA-style candidate evaluation with configurable weights and comprehensive logging",
                "body": {"resume_json": "Resume data from /resume endpoint", "jd_json": "JD data from /jd_parser endpoint", "weightage": "Optional weightage configuration for scoring fields"}
            },
            {
                "path": "/interfix",
                "method": "POST",
                "description": "Extract interview information from call summary or transcript (VAPI response)",
                "body": {"summary": "Call summary or transcript text"}
            },
            {
                "path": "/debug/json-files",
                "method": "GET",
                "description": "List all debug JSON files created during parsing failures"
            },
            {
                "path": "/debug/json-files/{filename}",
                "method": "GET",
                "description": "Get content and analysis of a specific debug JSON file"
            },
            {
                "path": "/debug/json-files",
                "method": "DELETE",
                "description": "Clean up all debug JSON files"
            },
            {
                "path": "/debug/repair-json",
                "method": "POST",
                "description": "Test LLM JSON repair functionality directly",
                "body": {"malformed_json": "string", "context": "resume|job_description"}
            },
            {
                "path": "/section",
                "method": "POST",
                "description": "Extract resume sections using multiple LLM calls (one call per section)",
                "body": "Upload a resume file (PDF/DOCX)"
            },
            {
                "path": "/section2",
                "method": "POST",
                "description": "Extract resume sections using a single LLM call",
                "body": "Upload a resume file (PDF/DOCX)"
            },
            {
                "path": "/section3",
                "method": "POST",
                "description": "Extract resume sections using regex pattern matching (fastest, no LLM calls)",
                "body": "Upload a resume file (PDF/DOCX)"
            },
            {
                "path": "/hybrid_resume",
                "method": "POST",
                "description": "Hybrid resume parsing: regex section extraction + LLM JSON structuring",
                "body": "Upload a resume file (PDF/DOCX)"
            }
        ]
    }

@app.post("/generate")
async def generate_endpoint(request: GenerateRequest):
    try:
        full_prompt = request.history + "\nUser: " + request.prompt + "\nAssistant: "
        response = get_response(
            full_prompt,
            endpoint="/generate",
            context="chat_conversation",
            call_type="main"
        )
        return {"response": response}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred.")

@app.post("/generate_with_image", summary="Generate a response based on text and image", description="Upload an image and provide a prompt to get a response related to the image")
async def generate_with_image_endpoint(
    file: UploadFile = File(..., description="Image file to analyze"),
    prompt: str = Form(..., description="Text prompt related to the image"),
    history: str = Form("", description="Optional conversation history")
):
    """Generate a response based on an image and text prompt.

    - **file**: Upload an image file (JPG, PNG, etc.)
    - **prompt**: Text prompt related to the image
    - **history**: Optional conversation history

    Returns a JSON object with the generated response.
    """
    try:
        # Check if the file is an image
        file_extension = os.path.splitext(file.filename.lower())[1]
        supported_extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp"]

        if file_extension not in supported_extensions:
            logger.warning(f"Unsupported file format: {file.filename}")
            raise HTTPException(status_code=400, detail=f"Only image files are supported: {', '.join(supported_extensions)}")

        logger.info(f"Processing image file: {file.filename}")

        # Create a temporary file to store the uploaded image
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            # Write the uploaded file content to the temporary file
            content = await file.read()
            if not content:
                logger.error("Uploaded file is empty")
                raise HTTPException(status_code=400, detail="The uploaded file is empty.")

            temp_file.write(content)
            temp_file_path = temp_file.name
            logger.info(f"Saved uploaded image to temporary location: {temp_file_path}")

        try:
            # Create a multimodal prompt that includes the image
            image_prompt = f"I'm looking at this image. {prompt}"

            # Combine with history if provided
            if history:
                image_prompt = f"{history}\n\n{image_prompt}"

            logger.info(f"Sending image to Gemma with prompt: {image_prompt}")

            # Get response with a longer timeout for image processing
            # Pass the image path to the model
            response = get_response(
                prompt=image_prompt,
                timeout_seconds=120,
                max_tokens=1500,
                image_path=temp_file_path,
                endpoint="/generate_image",
                context=f"uploaded_image_{file.filename}",
                call_type="image_chat"
            )

            return {"response": response}
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                logger.info(f"Removing temporary file: {temp_file_path}")
                os.remove(temp_file_path)

    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Unexpected error in image processing: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@app.post("/resume", response_model=Dict, summary="Parse a resume", description="Upload a PDF or DOCX resume and extract ALL structured information using the Gemma 3:4B model")
async def parse_resume_endpoint(
    request: Request,
    file: UploadFile = File(..., description="Resume file to parse (PDF or DOCX format)")
):
    """Parse a resume and extract ALL structured information.

    - **file**: Upload a PDF or DOCX file containing a resume

    Returns a comprehensive JSON object with ALL parsed resume information, including but not limited to:
    - Basic information (name, email, phone)
    - Education history
    - Work experience
    - Skills (as a dictionary with context about where each skill was used)
    - Projects
    - Certifications
    - Domain of interest
    - Languages
    - Achievements
    - Publications
    - And any other relevant information found in the resume

    Note: This endpoint uses a waterfall mechanism for text extraction:
    1. First attempts to extract text using standard PDF/DOCX parsing
    2. If that fails, falls back to treating the document as an image and using the LLM's vision capabilities

    The confidence score indicates how confident the model is in the correctness of the extracted information.
    A higher score means the model is more confident that the extracted data accurately represents what's in the
    resume. The score is calculated by analyzing the structure, consistency, and patterns in the
    extracted data.
    """
    # Get the metrics tracker from request state
    metrics = getattr(request.state, "metrics", None)
    if metrics:
        metrics.add_metric("endpoint", "resume")

    try:
        # Check if the file is a supported format (PDF or DOCX)
        file_extension = os.path.splitext(file.filename.lower())[1]

        if file_extension == '.pdf':
            file_type = "pdf"
            suffix = '.pdf'
        elif file_extension == '.docx':
            file_type = "docx"
            suffix = '.docx'
        else:
            logger.warning(f"Unsupported file format: {file.filename}")
            if metrics:
                metrics.add_metric("error", f"Unsupported file format: {file_extension}")
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported.")

        logger.info(f"Processing resume file: {file.filename} (type: {file_type})")
        if metrics:
            metrics.add_metric("file_name", file.filename)
            metrics.add_metric("file_type", file_type)

        # Create a temporary file to store the uploaded file
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            # Write the uploaded file content to the temporary file
            content = await file.read()
            if not content:
                logger.error("Uploaded file is empty")
                if metrics:
                    metrics.add_metric("error", "Empty file")
                raise HTTPException(status_code=400, detail="The uploaded file is empty.")

            temp_file.write(content)
            temp_file_path = temp_file.name
            logger.info(f"Saved uploaded file to temporary location: {temp_file_path}")

            if metrics:
                metrics.add_metric("file_size", len(content))

        try:
            # Extract text from the file using waterfall mechanism
            # This will first try standard extraction, then fall back to image-based extraction if needed
            start_extraction_time = time.time()
            resume_text = extract_text_from_file(
                temp_file_path,
                file_type,
                request_metrics=metrics,
                source_filename=file.filename,
                context="resume"
            )
            extraction_time = time.time() - start_extraction_time

            if metrics:
                metrics.add_metric("text_extraction_time", extraction_time)
                metrics.add_metric("extracted_text_length", len(resume_text))

            # Parse the resume text using the Gemma model
            start_parsing_time = time.time()

            # Parse the resume text using the Gemma model (always convert skills to dictionary format)
            parsed_data = parse_resume(resume_text, convert_skills_to_dict_format=True, source_filename=file.filename)

            parsing_time = time.time() - start_parsing_time
            if metrics:
                metrics.add_metric("parsing_time", parsing_time)
                metrics.add_metric("confidence_score", parsed_data.get("confidence_score", 0))

            # Remove any internal fields that might be present
            for field in list(parsed_data.keys()):
                if field.startswith("_"):
                    parsed_data.pop(field)

            # Log the number of fields extracted
            if metrics:
                metrics.add_metric("fields_extracted", len(parsed_data))
                metrics.add_metric("skills_count", len(parsed_data.get("skills", [])))
                metrics.add_metric("education_count", len(parsed_data.get("education", [])))
                metrics.add_metric("experience_count", len(parsed_data.get("experience", [])))
                metrics.add_metric("total_processing_time", extraction_time + parsing_time)

            return parsed_data
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                logger.info(f"Removing temporary file: {temp_file_path}")
                os.remove(temp_file_path)

    except HTTPException as e:
        # Log the error in metrics
        if metrics:
            metrics.add_metric("error", str(e))
            metrics.add_metric("error_status", e.status_code)
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        # Log the error in metrics
        if metrics:
            metrics.add_metric("error", str(e))
            metrics.add_metric("error_status", 500)
        logger.error(f"Unexpected error in resume parsing: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

def generate_questions_for_category(jd_text: str, resume_data: Dict, category: str, num_questions: int) -> List[str]:
    """Generate interview questions for a specific category."""
    if num_questions <= 0:
        return []

    logger.info(f"Generating {num_questions} questions for category: {category}")

    # Map category to a more readable name
    category_name = {
        "technical_questions": "Technical",
        "past_experience_questions": "Past Experience",
        "case_study_questions": "Case Study",
        "situation_handling_questions": "Situation Handling",
        "personality_test_questions": "Personality Test"
    }.get(category, category)

    # Special handling for personality test questions
    if category == "personality_test_questions":
        # For personality questions, use a more general prompt focused on personality traits
        prompt = f"""
        You are an expert interview question generator. Your task is to create {num_questions} personality assessment questions.

        Candidate Resume Data (in JSON format):
        {json.dumps(resume_data, indent=2)}

        Generate exactly {num_questions} personality assessment questions that:
        - Focus on general personality traits like teamwork, leadership, adaptability, work ethic, etc.
        - Help understand the candidate's character, values, and working style
        - Are NOT specific to the job description but rather about the person's general traits
        - Reveal how the candidate might fit into different team environments
        - Assess soft skills and interpersonal abilities

        Examples of good personality questions:
        - "How do you handle criticism or feedback from colleagues or supervisors?"
        - "Describe a situation where you had to adapt to a significant change at work. How did you handle it?"
        - "What motivates you the most in your professional life?"
        - "How do you prioritize tasks when facing multiple deadlines?"
        - "Describe your ideal work environment and management style."

        IMPORTANT: Respond ONLY with a JSON array of strings, where each string is a question.
        Example: ["Question 1?", "Question 2?", "Question 3?"]

        DO NOT include any explanations, markdown formatting, or code blocks in your response.
        Your entire response should be ONLY the JSON array, nothing else.
        """
    else:
        # For all other categories, use the original prompt
        prompt = f"""
        You are an expert interview question generator. Your task is to create {num_questions} tailored {category_name} questions based on a job description and a candidate's resume.

        Job Description:
        {jd_text}

        Candidate Resume Data (in JSON format):
        {json.dumps(resume_data, indent=2)}

        Generate exactly {num_questions} {category_name} questions that are:
        - Relevant to the job description
        - Tailored to the candidate's background and experience
        - Specific and detailed (not generic)
        - Designed to assess the candidate's fit for the role

        IMPORTANT: Respond ONLY with a JSON array of strings, where each string is a question.
        Example: ["Question 1?", "Question 2?", "Question 3?"]

        DO NOT include any explanations, markdown formatting, or code blocks in your response.
        Your entire response should be ONLY the JSON array, nothing else.
        """

    try:
        # Use a shorter timeout for individual category generation
        response = get_response(
            prompt,
            timeout_seconds=45,
            max_tokens=500,
            endpoint="/jd_questions",
            context=f"{category}_questions",
            call_type="question_generation"
        )

        # Try to extract JSON array from the response
        json_str = response.strip()

        # Remove any markdown formatting if present
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()

        # Try to find the JSON array if there's additional text
        if json_str.find('[') >= 0 and json_str.rfind(']') >= 0:
            start = json_str.find('[')
            end = json_str.rfind(']') + 1
            json_str = json_str[start:end]

        # Parse the JSON string into a Python list
        try:
            questions = json.loads(json_str)

            # Ensure we have a list of strings
            if isinstance(questions, list):
                # Convert all items to strings and filter out empty ones
                questions = [str(q) for q in questions if q]
                return questions[:num_questions]  # Limit to requested number
            else:
                logger.warning(f"Expected a list but got {type(questions)} for {category}")
                return []

        except json.JSONDecodeError:
            # If JSON parsing fails, try to extract questions using regex
            # Look for text that looks like questions (ends with ? or has question-like structure)
            question_pattern = re.compile(r'"([^"]+\?)"')
            matches = question_pattern.findall(json_str)

            if matches:
                return matches[:num_questions]  # Limit to requested number
            else:
                logger.warning(f"Could not extract questions for {category}")
                return []

    except Exception as e:
        logger.error(f"Error generating questions for {category}: {e}")
        return []

def generate_interview_questions(jd_text: str, resume_data: Dict, question_scales: Dict) -> Dict:
    """Generate interview questions based on job description and resume data."""
    logger.info("Starting interview question generation using category-by-category approach")

    # Define the categories and map scale values to actual question counts
    categories = [
        "technical_questions",
        "past_experience_questions",
        "case_study_questions",
        "situation_handling_questions",
        "personality_test_questions"
    ]

    # Initialize results dictionary
    questions_data = {}

    # Generate questions for each category in parallel
    from concurrent.futures import ThreadPoolExecutor

    def process_category(category):
        # Map scale (0-10) to number of questions (0-5)
        scale = question_scales.get(category, 0)
        num_questions = min(5, max(0, int(scale / 2)))

        if num_questions > 0:
            return category, generate_questions_for_category(jd_text, resume_data, category, num_questions)
        else:
            return category, []

    # Use ThreadPoolExecutor to process categories in parallel
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = {executor.submit(process_category, category): category for category in categories}

        for future in futures:
            try:
                category, questions = future.result()
                questions_data[category] = questions
                logger.info(f"Generated {len(questions)} questions for {category}")
            except Exception as e:
                logger.error(f"Error processing category: {e}")
                # Initialize with empty list on error
                questions_data[futures[future]] = []

    # Ensure all categories are present
    for category in categories:
        if category not in questions_data:
            questions_data[category] = []

    logger.info("Successfully generated all interview questions")
    return questions_data

@app.post("/jd_parser", response_model=Dict, summary="Parse a job description", description="Upload a PDF or DOCX job description and extract ALL structured information using the Gemma 3:4B model")
async def parse_jd_endpoint(
    file: UploadFile = File(..., description="Job description file to parse (PDF or DOCX format)")
):
    """Parse a job description and extract ALL structured information.

    - **file**: Upload a PDF or DOCX file containing a job description

    Returns a comprehensive JSON object with parsed job description information, including but not limited to:
    - Job title
    - Company name
    - Location
    - Job type (Full-time, Part-time, etc.)
    - Work mode (Remote, Hybrid, On-site)
    - Required skills
    - Preferred skills
    - Required experience
    - Education requirements
    - Responsibilities
    - Benefits
    - And any other relevant information found in the job description

    Note: This endpoint only returns the parsed JD data in JSON format. It does not generate interview questions.
    Use the /jd or /jd_only endpoints if you need interview questions based on the job description.

    This endpoint uses a waterfall mechanism for text extraction:
    1. First attempts to extract text using standard PDF/DOCX parsing
    2. If that fails, falls back to treating the document as an image and using the LLM's vision capabilities

    The confidence score indicates how confident the model is in the correctness of the extracted information.
    A higher score means the model is more confident that the extracted data accurately represents what's in the
    job description. The score is calculated by analyzing the structure, consistency, and patterns in the
    extracted data.
    """
    try:
        # Check if the file is a supported format (PDF or DOCX)
        file_extension = os.path.splitext(file.filename.lower())[1]

        if file_extension == '.pdf':
            file_type = "pdf"
            suffix = '.pdf'
        elif file_extension == '.docx':
            file_type = "docx"
            suffix = '.docx'
        else:
            logger.warning(f"Unsupported file format: {file.filename}")
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported.")

        logger.info(f"Processing job description file: {file.filename} (type: {file_type})")

        # Create a temporary file to store the uploaded file
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            # Write the uploaded file content to the temporary file
            content = await file.read()
            if not content:
                logger.error("Uploaded file is empty")
                raise HTTPException(status_code=400, detail="The uploaded file is empty.")

            temp_file.write(content)
            temp_file_path = temp_file.name
            logger.info(f"Saved uploaded file to temporary location: {temp_file_path}")

        try:
            # Extract text from the file using waterfall mechanism
            # This will first try standard extraction, then fall back to image-based extraction if needed
            jd_text = extract_text_from_file(
                temp_file_path,
                file_type,
                source_filename=file.filename,
                context="jd"
            )

            # Parse the job description text using the Gemma model
            logger.info(f"Extracted {len(jd_text)} characters of text from job description file")

            # Log a preview of the extracted text for debugging
            text_preview = jd_text[:500] + "..." if len(jd_text) > 500 else jd_text
            logger.info(f"Text preview: {text_preview}")

            # Parse the job description
            start_parsing_time = time.time()
            parsed_data = parse_jd(jd_text, source_filename=file.filename)
            parsing_time = time.time() - start_parsing_time

            logger.info(f"Job description parsing completed in {parsing_time:.2f} seconds")

            # Log parsing results
            if "job_title" in parsed_data:
                logger.info(f"Parsed job title: {parsed_data['job_title']}")

            if "required_skills" in parsed_data:
                skills_count = len(parsed_data["required_skills"])
                logger.info(f"Extracted {skills_count} required skills")
                if skills_count > 0:
                    skills_preview = ", ".join(parsed_data["required_skills"][:5])
                    if skills_count > 5:
                        skills_preview += f", ... ({skills_count-5} more)"
                    logger.info(f"Skills preview: {skills_preview}")

            if "education_requirements" in parsed_data:
                edu_count = len(parsed_data["education_requirements"])
                logger.info(f"Extracted {edu_count} education requirements")
                if edu_count > 0:
                    logger.info(f"Education requirements: {parsed_data['education_requirements']}")

            if "education_details" in parsed_data and isinstance(parsed_data["education_details"], dict):
                logger.info(f"Education details: {parsed_data['education_details']}")

            # Remove any internal fields that might be present
            for field in list(parsed_data.keys()):
                if field.startswith("_"):
                    parsed_data.pop(field)

            return parsed_data
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                logger.info(f"Removing temporary file: {temp_file_path}")
                os.remove(temp_file_path)

    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Unexpected error in job description parsing: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")


@app.post("/jd", response_model=Dict, summary="Generate interview questions", description="Upload a job description and provide resume data to generate tailored interview questions")
async def generate_interview_questions_endpoint(
    file: UploadFile = File(..., description="Job description file to parse (PDF or DOCX format)"),
    request_data_json: str = Form(None, description="JSON string containing resume data and question scales")
):
    """Generate tailored interview questions based on a job description and resume data.

    - **file**: Upload a PDF or DOCX file containing a job description
    - **request_data_json**: JSON data containing resume information and question scales

    The JSON data should have the following structure:
    ```json
    {
        "resume_json": { ... resume data ... },
        "technical_questions": 5,  // Scale 0-10
        "past_experience_questions": 3,  // Scale 0-10
        "case_study_questions": 2,  // Scale 0-10
        "situation_handling_questions": 4,  // Scale 0-10
        "personality_test_questions": 2  // Scale 0-10
    }
    ```

    Returns a JSON object with categorized interview questions:
    - Technical questions
    - Past experience questions
    - Case study questions
    - Situation handling questions
    - Personality test questions

    The number of questions in each category is proportional to the scale values provided (0-10).
    For efficiency, the scale values are converted to a maximum of 5 questions per category.

    Note: This endpoint processes each question category in parallel for faster response times.
    """
    try:
        # Check if the file is a supported format (PDF or DOCX)
        file_extension = os.path.splitext(file.filename.lower())[1]

        if file_extension == '.pdf':
            file_type = "pdf"
            suffix = '.pdf'
        elif file_extension == '.docx':
            file_type = "docx"
            suffix = '.docx'
        else:
            logger.warning(f"Unsupported file format: {file.filename}")
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported.")

        # Parse the request data JSON
        try:
            # Check if we have any data
            if request_data_json is None:
                raise HTTPException(
                    status_code=400,
                    detail="Missing request data. Please provide the 'request_data_json' field."
                )

            # Parse the JSON data
            request_data_dict = json.loads(request_data_json)
            request_data_obj = JDQuestionRequest(**request_data_dict)

            logger.info("Successfully parsed request data")
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON in request data.")
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid request data: {str(e)}")

        logger.info(f"Processing job description file: {file.filename} (type: {file_type})")

        # Create a temporary file to store the uploaded file
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            # Write the uploaded file content to the temporary file
            content = await file.read()
            if not content:
                logger.error("Uploaded file is empty")
                raise HTTPException(status_code=400, detail="The uploaded file is empty.")

            temp_file.write(content)
            temp_file_path = temp_file.name
            logger.info(f"Saved uploaded file to temporary location: {temp_file_path}")

        try:
            # Extract text from the file based on its type
            jd_text = extract_text_from_file(
                temp_file_path,
                file_type,
                source_filename=file.filename,
                context="jd"
            )

            # Prepare question scales dictionary
            question_scales = {
                "technical_questions": request_data_obj.technical_questions,
                "past_experience_questions": request_data_obj.past_experience_questions,
                "case_study_questions": request_data_obj.case_study_questions,
                "situation_handling_questions": request_data_obj.situation_handling_questions,
                "personality_test_questions": request_data_obj.personality_test_questions
            }

            # Generate interview questions
            questions_data = generate_interview_questions(jd_text, request_data_obj.resume_json, question_scales)

            return questions_data
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                logger.info(f"Removing temporary file: {temp_file_path}")
                os.remove(temp_file_path)

    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Unexpected error in interview question generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

def normalize_skill(skill: str) -> str:
    """
    Normalize a skill name by removing common variations and standardizing format.

    Args:
        skill: The skill name to normalize

    Returns:
        Normalized skill name
    """
    # Handle None or empty strings
    if not skill:
        return ""

    # Convert to lowercase
    normalized = str(skill).lower()

    # Remove common suffixes and prefixes
    normalized = re.sub(r'\b(programming|development|developer|engineer|engineering|specialist|expert|proficiency|basics of|basic|advanced|intermediate|experience with|experience in|knowledge of|skills in)\b', '', normalized)

    # Remove punctuation and extra whitespace
    normalized = re.sub(r'[^\w\s]', '', normalized)
    normalized = re.sub(r'\s+', ' ', normalized).strip()

    return normalized


def get_skill_variations(skill: str) -> List[str]:
    """
    Generate common variations of a skill name.

    Args:
        skill: The base skill name

    Returns:
        List of skill variations
    """
    # Handle None or empty strings
    if not skill:
        return []

    variations = [skill]
    normalized = normalize_skill(skill)

    # Skip empty normalized skills
    if not normalized:
        return variations

    # Add normalized version if different
    if normalized != str(skill).lower():
        variations.append(normalized)

    # Add common variations for programming languages
    if normalized in ["python", "java", "javascript", "c++", "c#", "ruby", "php", "go", "rust", "typescript"]:
        variations.extend([
            f"{normalized} programming",
            f"{normalized} development",
            f"{normalized} coding",
            f"{normalized} language"
        ])

    # Add variations for frameworks
    if normalized in ["react", "angular", "vue", "django", "flask", "spring", "laravel", "express"]:
        variations.extend([
            f"{normalized} framework",
            f"{normalized} development",
            f"{normalized}.js"
        ])

    # Add variations for databases
    if normalized in ["sql", "mysql", "postgresql", "mongodb", "oracle", "sqlite", "redis"]:
        variations.extend([
            f"{normalized} database",
            f"{normalized} db"
        ])

    # Add variations for cloud platforms
    if normalized in ["aws", "azure", "gcp", "google cloud"]:
        variations.extend([
            f"{normalized} platform",
            f"{normalized} services",
            f"{normalized} cloud"
        ])

    # Special cases
    if normalized == "ml":
        variations.extend(["machine learning"])
    elif normalized == "ai":
        variations.extend(["artificial intelligence"])
    elif normalized == "ui":
        variations.extend(["user interface", "ui design"])
    elif normalized == "ux":
        variations.extend(["user experience", "ux design"])

    # Remove duplicates while preserving order
    unique_variations = []
    for v in variations:
        if v and v not in unique_variations:
            unique_variations.append(v)

    return unique_variations


def is_skill_match(resume_skill: str, jd_skill: str) -> bool:
    """
    Check if a resume skill matches a job description skill using semantic matching.

    Args:
        resume_skill: Skill from resume
        jd_skill: Skill from job description

    Returns:
        True if skills match semantically, False otherwise
    """
    # Handle None or empty strings
    if not resume_skill or not jd_skill:
        return False

    # Normalize both skills
    resume_skill_norm = normalize_skill(resume_skill)
    jd_skill_norm = normalize_skill(jd_skill)

    # Skip if either normalized skill is empty
    if not resume_skill_norm or not jd_skill_norm:
        return False

    # Direct match after normalization
    if resume_skill_norm == jd_skill_norm:
        return True

    # Check if one is a substring of the other
    # Only consider substantial matches to avoid false positives
    # For example, "C" shouldn't match "C++" but "Python" should match "Python programming"
    if len(resume_skill_norm) > 2 and len(jd_skill_norm) > 2:
        if (resume_skill_norm in jd_skill_norm) or (jd_skill_norm in resume_skill_norm):
            return True

    # Get variations of both skills and check for overlaps
    resume_variations = get_skill_variations(resume_skill)
    jd_variations = get_skill_variations(jd_skill)

    # Check if any variation of resume skill matches any variation of jd skill
    for rv in resume_variations:
        if not rv:  # Skip empty variations
            continue
        for jv in jd_variations:
            if not jv:  # Skip empty variations
                continue
            # Direct match
            if rv.lower() == jv.lower():
                return True
            # Substring match for substantial strings
            if len(rv) > 2 and len(jv) > 2:
                if rv.lower() in jv.lower() or jv.lower() in rv.lower():
                    return True

    return False


def find_matching_skills(resume_skills: List[str], jd_skills: List[str]) -> Tuple[List[str], List[str]]:
    """
    Find matching skills between resume and job description using semantic matching.

    Args:
        resume_skills: List of skills from resume
        jd_skills: List of skills from job description

    Returns:
        Tuple of (matched_skills, unmatched_skills)
    """
    # Handle None values
    if resume_skills is None:
        resume_skills = []
    if jd_skills is None:
        jd_skills = []

    matched = []
    unmatched = []

    # Filter out empty skills
    valid_resume_skills = [s for s in resume_skills if s]
    valid_jd_skills = [s for s in jd_skills if s]

    for jd_skill in valid_jd_skills:
        found_match = False
        for resume_skill in valid_resume_skills:
            if is_skill_match(resume_skill, jd_skill):
                matched.append(jd_skill)
                found_match = True
                break

        if not found_match:
            unmatched.append(jd_skill)

    return matched, unmatched


def calculate_candidate_job_fit(resume_data: Dict, jd_data: Dict) -> Dict:
    """
    Calculate how well a candidate's resume matches a job description.

    This function evaluates the fit between a candidate and a job based on multiple criteria:
    1. Skills matching (direct and from projects/experience)
    2. Years of experience
    3. Reliability (experience to job ratio)
    4. Location match
    5. Academic qualifications
    6. Alma mater prestige
    7. Relevant certifications

    Returns a dictionary with overall score and detailed rationale.
    """
    logger.info("Calculating candidate-job fit score")

    # Initialize scores and rationale
    scores = {
        "skills_match_direct": 0,
        "skills_match_subjective": 0,
        "experience_match": 0,
        "reliability": 0,
        "location_match": 0,
        "academic_match": 0,
        "alma_mater": 0,
        "certifications": 0
    }
    rationale = {
        "skills_match_direct": "",
        "skills_match_subjective": "",
        "experience_match": "",
        "reliability": "",
        "location_match": "",
        "academic_match": "",
        "alma_mater": "",
        "certifications": ""
    }

    # 1. Direct Skills Matching (25%)
    resume_skills_list = []
    if "skills" in resume_data:
        if isinstance(resume_data["skills"], list):
            resume_skills_list = resume_data["skills"]
        elif isinstance(resume_data["skills"], dict):
            resume_skills_list = list(resume_data["skills"].keys())

    jd_required_skills_list = []
    jd_preferred_skills_list = []

    if "required_skills" in jd_data and isinstance(jd_data["required_skills"], list):
        jd_required_skills_list = jd_data["required_skills"]

    if "preferred_skills" in jd_data and isinstance(jd_data["preferred_skills"], list):
        jd_preferred_skills_list = jd_data["preferred_skills"]

    # Calculate direct skills match using semantic matching
    if jd_required_skills_list:
        # Log skills for debugging
        logger.info(f"Resume skills: {resume_skills_list}")
        logger.info(f"JD required skills: {jd_required_skills_list}")
        if jd_preferred_skills_list:
            logger.info(f"JD preferred skills: {jd_preferred_skills_list}")

        # Find matching and non-matching required skills
        matched_required, missing_required = find_matching_skills(resume_skills_list, jd_required_skills_list)
        required_match_percentage = len(matched_required) / len(jd_required_skills_list) if jd_required_skills_list else 0

        # Log matching results
        logger.info(f"Matched required skills: {matched_required}")
        logger.info(f"Missing required skills: {missing_required}")
        logger.info(f"Required skills match percentage: {required_match_percentage:.2f}")

        # Bonus for preferred skills
        preferred_bonus = 0
        matched_preferred = []
        if jd_preferred_skills_list:
            # Find matching preferred skills
            matched_preferred, _ = find_matching_skills(resume_skills_list, jd_preferred_skills_list)
            preferred_match_percentage = len(matched_preferred) / len(jd_preferred_skills_list) if jd_preferred_skills_list else 0
            preferred_bonus = preferred_match_percentage * 0.2  # 20% bonus max for preferred skills

            # Log preferred skills matching
            logger.info(f"Matched preferred skills: {matched_preferred}")
            logger.info(f"Preferred skills match percentage: {preferred_match_percentage:.2f}")
            logger.info(f"Preferred skills bonus: {preferred_bonus:.2f}")

        # Calculate final direct skills score (out of 25)
        skills_score = min(25, (required_match_percentage * 20) + (preferred_bonus * 5))
        scores["skills_match_direct"] = skills_score
        logger.info(f"Final direct skills score: {skills_score:.2f}/25")

        # Generate rationale
        rationale["skills_match_direct"] = f"Matched {len(matched_required)}/{len(jd_required_skills_list)} required skills"
        if jd_preferred_skills_list:
            rationale["skills_match_direct"] += f" and {len(matched_preferred)}/{len(jd_preferred_skills_list)} preferred skills"

        # List matched and missing skills
        if matched_required:
            rationale["skills_match_direct"] += f". Matched required skills: {', '.join(matched_required)}"

        if missing_required:
            rationale["skills_match_direct"] += f". Missing required skills: {', '.join(missing_required)}"
    else:
        scores["skills_match_direct"] = 0
        rationale["skills_match_direct"] = "No required skills specified in the job description"

    # 2. Subjective Skills Matching from Projects and Experience (15%)
    # Extract text from projects and experience
    experience_text = ""
    projects_text = ""

    if "experience" in resume_data and isinstance(resume_data["experience"], list):
        for exp in resume_data["experience"]:
            if isinstance(exp, dict):
                for _, value in exp.items():  # Use _ to indicate unused variable
                    if isinstance(value, str):
                        experience_text += value + " "

    if "projects" in resume_data and isinstance(resume_data["projects"], list):
        for project in resume_data["projects"]:
            if isinstance(project, dict):
                for _, value in project.items():  # Use _ to indicate unused variable
                    if isinstance(value, str):
                        projects_text += value + " "
            elif isinstance(project, str):
                projects_text += project + " "

    combined_text = (experience_text + " " + projects_text).lower()

    # Check for skills in the combined text using semantic matching
    subjective_matches = []

    # Combine required and preferred skills from JD
    all_jd_skills_list = jd_required_skills_list + jd_preferred_skills_list

    # Extract potential skills from experience and projects text
    # Look for skills mentioned in specific contexts
    skill_contexts = [
        r'proficient in\s+([A-Za-z0-9+#/\s]+)',
        r'experience with\s+([A-Za-z0-9+#/\s]+)',
        r'knowledge of\s+([A-Za-z0-9+#/\s]+)',
        r'familiar with\s+([A-Za-z0-9+#/\s]+)',
        r'skills:?\s*([A-Za-z0-9+#/\s,]+)',
        r'technologies:?\s*([A-Za-z0-9+#/\s,]+)',
        r'developed\s+([A-Za-z0-9+#/\s,]+)',
        r'implemented\s+([A-Za-z0-9+#/\s,]+)',
        r'using\s+([A-Za-z0-9+#/\s,]+)'
    ]

    extracted_skills = []
    for pattern in skill_contexts:
        matches = re.finditer(pattern, combined_text)
        for match in matches:
            skill_text = match.group(1).strip()
            # Split by common separators
            for skill in re.split(r'[,;/|]|\sand\s', skill_text):
                skill = skill.strip()
                if skill and len(skill) > 2:
                    extracted_skills.append(skill)

    # Check if any of the extracted skills match JD skills
    for jd_skill in all_jd_skills_list:
        # Skip empty skills
        if not jd_skill:
            continue

        # Skip if this skill is already matched in the resume skills
        if any(is_skill_match(resume_skill, jd_skill) for resume_skill in resume_skills_list if resume_skill):
            continue

        # Check if any extracted skill matches this JD skill
        for extracted_skill in extracted_skills:
            if not extracted_skill:
                continue

            if is_skill_match(extracted_skill, jd_skill):
                subjective_matches.append(jd_skill)
                break

    # Log extracted skills for debugging
    if extracted_skills:
        logger.info(f"Extracted {len(extracted_skills)} potential skills from experience/projects text")
        logger.info(f"Extracted skills sample: {', '.join(extracted_skills[:10])}" +
                   ("..." if len(extracted_skills) > 10 else ""))

    # Log subjective matches
    if subjective_matches:
        logger.info(f"Found {len(subjective_matches)} additional skills in experience/projects: {', '.join(subjective_matches)}")
    else:
        logger.info("No additional skills found in experience/projects")

    # Calculate subjective skills score
    if all_jd_skills_list:
        # Calculate percentage based on additional matches found
        subjective_match_percentage = len(subjective_matches) / len(all_jd_skills_list)
        subjective_score = min(15, subjective_match_percentage * 15)
        scores["skills_match_subjective"] = subjective_score

        logger.info(f"Subjective skills match percentage: {subjective_match_percentage:.2f}")
        logger.info(f"Subjective skills score: {subjective_score:.2f}/15")

        # Generate rationale
        if subjective_matches:
            rationale["skills_match_subjective"] = f"Found {len(subjective_matches)} additional skills in experience/projects: {', '.join(subjective_matches)}"
        else:
            rationale["skills_match_subjective"] = "No additional skills found in experience/projects"
    else:
        scores["skills_match_subjective"] = 0
        rationale["skills_match_subjective"] = "No skills specified in the job description"

    # 3. Years of Experience Match (20%)
    required_yoe = None

    # Extract required years of experience from JD
    if "required_experience" in jd_data and jd_data["required_experience"]:
        yoe_match = re.search(r'(\d+)[\+\-]?\s*(?:year|yr|yrs|years)', jd_data["required_experience"].lower())
        if yoe_match:
            required_yoe = int(yoe_match.group(1))

    # Calculate candidate's total years of experience
    candidate_yoe = 0
    if "experience" in resume_data and isinstance(resume_data["experience"], list):
        for exp in resume_data["experience"]:
            if isinstance(exp, dict) and "duration" in exp:
                duration = exp["duration"]
                if isinstance(duration, str):
                    # Try to extract years from duration strings like "2018-2022" or "3 years"
                    year_match = re.search(r'(\d{4})\s*-\s*(\d{4}|\bpresent\b)', duration.lower())
                    if year_match:
                        start_year = int(year_match.group(1))
                        end_year = 2023  # Default to current year if "present"
                        if year_match.group(2).isdigit():
                            end_year = int(year_match.group(2))
                        candidate_yoe += (end_year - start_year)
                    else:
                        # Try direct year specification
                        direct_years = re.search(r'(\d+)\s*(?:year|yr|yrs|years)', duration.lower())
                        if direct_years:
                            candidate_yoe += int(direct_years.group(1))

    # Calculate experience match score
    if required_yoe is not None and candidate_yoe > 0:
        # Check if within 20% of required experience
        if candidate_yoe >= required_yoe * 0.8:
            if candidate_yoe <= required_yoe * 1.2:
                # Perfect match - within 20% range
                scores["experience_match"] = 20
                rationale["experience_match"] = f"Candidate has {candidate_yoe} years of experience, which is within the ideal range for the required {required_yoe} years"
            else:
                # Over-experienced but still good
                over_percentage = min(100, ((candidate_yoe - required_yoe * 1.2) / required_yoe) * 100)
                scores["experience_match"] = max(10, 20 - (over_percentage / 10))
                rationale["experience_match"] = f"Candidate has {candidate_yoe} years of experience, which is {over_percentage:.0f}% more than the required {required_yoe} years"
        else:
            # Under-experienced
            under_percentage = ((required_yoe * 0.8) - candidate_yoe) / (required_yoe * 0.8) * 100
            scores["experience_match"] = max(0, 20 - (under_percentage / 5))
            rationale["experience_match"] = f"Candidate has {candidate_yoe} years of experience, which is {under_percentage:.0f}% less than the minimum recommended {required_yoe * 0.8:.1f} years"
    else:
        scores["experience_match"] = 10  # Neutral score if we can't determine
        if required_yoe is None:
            rationale["experience_match"] = "No specific years of experience requirement found in the job description"
        else:
            rationale["experience_match"] = f"Could not determine candidate's years of experience to compare with required {required_yoe} years"

    # 4. Reliability (10%) - YOE / Number of Companies
    num_companies = len(resume_data.get("experience", [])) if isinstance(resume_data.get("experience"), list) else 0

    if candidate_yoe > 0 and num_companies > 0:
        avg_tenure = candidate_yoe / num_companies

        # Score based on average tenure
        if avg_tenure >= 3:
            scores["reliability"] = 10  # Excellent tenure
            rationale["reliability"] = f"Excellent stability with average {avg_tenure:.1f} years per company"
        elif avg_tenure >= 2:
            scores["reliability"] = 8  # Good tenure
            rationale["reliability"] = f"Good stability with average {avg_tenure:.1f} years per company"
        elif avg_tenure >= 1:
            scores["reliability"] = 5  # Acceptable tenure
            rationale["reliability"] = f"Acceptable stability with average {avg_tenure:.1f} years per company"
        else:
            scores["reliability"] = 2  # Short tenure
            rationale["reliability"] = f"Frequent job changes with only {avg_tenure:.1f} years per company"
    else:
        scores["reliability"] = 5  # Neutral score
        rationale["reliability"] = "Could not calculate job stability due to missing experience data"

    # 5. Location Match (10%)
    jd_location = jd_data.get("location", "").lower() if isinstance(jd_data.get("location"), str) else ""
    resume_location = resume_data.get("location", "").lower() if isinstance(resume_data.get("location"), str) else ""

    # Also check experience locations
    experience_locations = []
    if "experience" in resume_data and isinstance(resume_data["experience"], list):
        for exp in resume_data["experience"]:
            if isinstance(exp, dict) and "company_name" in exp and isinstance(exp["company_name"], str):
                # Company names sometimes include location in format "Company Name, Location"
                parts = exp["company_name"].split(",")
                if len(parts) > 1:
                    experience_locations.append(parts[-1].strip().lower())

    if jd_location and resume_location:
        # Direct location match
        if jd_location in resume_location or resume_location in jd_location:
            scores["location_match"] = 10
            rationale["location_match"] = f"Current location ({resume_location}) matches job location ({jd_location})"
        elif any(jd_location in loc or loc in jd_location for loc in experience_locations):
            # Match with previous work locations
            scores["location_match"] = 7
            rationale["location_match"] = f"Previous work location matches job location ({jd_location})"
        else:
            # No match
            scores["location_match"] = 0
            rationale["location_match"] = f"Current location ({resume_location}) does not match job location ({jd_location})"
    elif jd_location and experience_locations:
        if any(jd_location in loc or loc in jd_location for loc in experience_locations):
            scores["location_match"] = 7
            rationale["location_match"] = f"Previous work location matches job location ({jd_location})"
        else:
            scores["location_match"] = 0
            rationale["location_match"] = f"No location match found with job location ({jd_location})"
    else:
        scores["location_match"] = 5  # Neutral score
        rationale["location_match"] = "Location information not available for comparison"

    # 6. Academic Match (10%)
    education_entries = resume_data.get("education", []) if isinstance(resume_data.get("education"), list) else []
    education_requirements = jd_data.get("education_requirements", []) if isinstance(jd_data.get("education_requirements"), list) else []

    # Check for detailed education information
    education_details = jd_data.get("education_details", {}) if isinstance(jd_data.get("education_details"), dict) else {}

    # If we have detailed education information, add it to the requirements list
    if education_details:
        degree_level = education_details.get("degree_level")
        field_of_study = education_details.get("field_of_study")
        alternatives = education_details.get("alternatives")

        if degree_level and field_of_study:
            combined_req = f"{degree_level} in {field_of_study}"
            if combined_req not in education_requirements:
                education_requirements.append(combined_req)
        elif degree_level:
            if degree_level not in education_requirements:
                education_requirements.append(degree_level)
        elif field_of_study:
            if field_of_study not in education_requirements:
                education_requirements.append(field_of_study)

        if alternatives and alternatives not in education_requirements:
            education_requirements.append(alternatives)

    # Extract GPA/CGPA if available
    max_gpa = 0
    gpa_scale = 10  # Default scale

    for edu in education_entries:
        if isinstance(edu, dict):
            # Check for GPA in various fields
            for field in ["gpa", "cgpa", "score", "percentage"]:
                if field in edu and isinstance(edu[field], (str, int, float)):
                    gpa_str = str(edu[field])
                    # Try to extract numeric value
                    gpa_match = re.search(r'(\d+(?:\.\d+)?)', gpa_str)
                    if gpa_match:
                        gpa_value = float(gpa_match.group(1))

                        # Determine scale
                        if "out of" in gpa_str.lower() or "/" in gpa_str:
                            scale_match = re.search(r'(?:out of|\/)\s*(\d+(?:\.\d+)?)', gpa_str.lower())
                            if scale_match:
                                gpa_scale = float(scale_match.group(1))
                        elif gpa_value > 10:
                            gpa_scale = 100  # Assume percentage
                        elif gpa_value <= 4:
                            gpa_scale = 4  # Assume 4.0 scale

                        # Normalize to percentage
                        normalized_gpa = (gpa_value / gpa_scale) * 100
                        max_gpa = max(max_gpa, normalized_gpa)

    # Check if education requirements are met using semantic matching
    education_match = False
    matched_degree = ""
    matched_requirement = ""

    # Define common degree types and their variations for semantic matching
    degree_mappings = {
        "bachelor": ["bachelor", "bachelors", "b.tech", "b.e.", "b.e", "b.s.", "b.s", "b.a.", "b.a", "undergraduate", "btech", "be", "bs", "ba", "bsc", "bca"],
        "master": ["master", "masters", "m.tech", "m.e.", "m.e", "m.s.", "m.s", "m.a.", "m.a", "graduate", "mtech", "me", "ms", "ma", "msc", "mca"],
        "phd": ["phd", "ph.d", "ph.d.", "doctorate", "doctoral"],
        "associate": ["associate", "diploma", "a.s.", "a.s", "a.a.", "a.a"]
    }

    # Define common fields of study and their variations
    field_mappings = {
        "computer science": ["computer science", "cs", "cse", "computer engineering", "computing", "software engineering", "information technology", "it", "information systems", "aida", "ai", "artificial intelligence", "data analytics", "data science"],
        "engineering": ["engineering", "engineer", "technology"],
        "business": ["business", "management", "mba", "administration", "finance", "marketing", "economics"],
        "science": ["science", "physics", "chemistry", "biology", "mathematics", "math", "statistics"],
        "arts": ["arts", "humanities", "liberal arts", "social sciences", "psychology", "sociology", "philosophy"]
    }

    if education_requirements:
        for req in education_requirements:
            req_lower = req.lower()

            # Extract degree type and field from requirement
            req_degree_type = None
            for degree_type, variations in degree_mappings.items():
                if any(variation in req_lower for variation in variations):
                    req_degree_type = degree_type
                    break

            req_field = None
            for field, variations in field_mappings.items():
                if any(variation in req_lower for variation in variations):
                    req_field = field
                    break

            # Check each education entry against the requirement
            for edu in education_entries:
                if isinstance(edu, dict) and "degree" in edu and isinstance(edu["degree"], str):
                    edu_degree = edu["degree"].lower()

                    # Extract degree type and field from education
                    edu_degree_type = None
                    for degree_type, variations in degree_mappings.items():
                        if any(variation in edu_degree for variation in variations):
                            edu_degree_type = degree_type
                            break

                    edu_field = None
                    for field, variations in field_mappings.items():
                        if any(variation in edu_degree for variation in variations):
                            edu_field = field
                            break

                    # Check for graduation date in the education entry
                    graduation_date = None
                    for field in ["year", "graduation_date", "completion_date"]:
                        if field in edu and isinstance(edu[field], str):
                            date_str = edu[field].lower()
                            if "graduat" in date_str or "complet" in date_str:
                                # Extract year from strings like "Graduation Date: Aug 2026"
                                year_match = re.search(r'20\d\d', date_str)
                                if year_match:
                                    graduation_date = year_match.group(0)
                                    break

                    # Check for match based on degree type and field
                    if req_degree_type and edu_degree_type:
                        # If requirement specifies a degree type, it must match
                        if req_degree_type == edu_degree_type:
                            # If requirement specifies a field, it must match or be related
                            if not req_field or not edu_field or req_field == edu_field or req_field == "engineering" and edu_field == "computer science":
                                education_match = True
                                matched_degree = edu["degree"]
                                if graduation_date:
                                    matched_degree += f" (Graduation: {graduation_date})"
                                matched_requirement = req
                                break
                    else:
                        # Skip requirements that don't actually specify education credentials
                        # Only process requirements that contain actual degree-related terms
                        degree_related_terms = ["degree", "bachelor", "master", "phd", "diploma", "certificate", "graduation", "education", "qualification", "university", "college", "school"]

                        # Check if this requirement actually mentions education credentials
                        has_degree_terms = any(term in req_lower for term in degree_related_terms)

                        if has_degree_terms:
                            # Check for meaningful matches between degree and requirement
                            edu_terms = set(edu_degree.split())
                            req_terms = set(req_lower.split())
                            # Only match if there are significant overlapping terms (not just common words)
                            meaningful_overlap = edu_terms.intersection(req_terms) - {
                                "in", "of", "the", "and", "or", "a", "an", "with", "for", "to", "from",
                                "understanding", "complete", "application", "development", "life", "cycle",
                                "knowledge", "experience", "skills", "ability", "proficiency"
                            }
                            if meaningful_overlap and len(meaningful_overlap) >= 2:
                                education_match = True
                                matched_degree = edu["degree"]
                                if graduation_date:
                                    matched_degree += f" (Graduation: {graduation_date})"
                                matched_requirement = req
                                break

                        # Special case: Check if the degree contains "CSE" or "AIDA" which are common abbreviations
                        # for Computer Science and Engineering or AI and Data Analytics
                        if ("cse" in edu_degree or "aida" in edu_degree) and any(term.lower() in ["computer science", "engineering", "it", "technology", "artificial intelligence", "data analytics"] for term in req_lower.split()):
                            education_match = True
                            matched_degree = edu["degree"]
                            if graduation_date:
                                matched_degree += f" (Graduation: {graduation_date})"
                            matched_requirement = req
                            break
    else:
        # If no specific requirements, assume bachelor's degree is minimum
        for edu in education_entries:
            if isinstance(edu, dict) and "degree" in edu and isinstance(edu["degree"], str):
                edu_degree = edu["degree"].lower()

                # Check for graduation date in the education entry
                graduation_date = None
                for field in ["year", "graduation_date", "completion_date"]:
                    if field in edu and isinstance(edu[field], str):
                        date_str = edu[field].lower()
                        if "graduat" in date_str or "complet" in date_str:
                            # Extract year from strings like "Graduation Date: Aug 2026"
                            year_match = re.search(r'20\d\d', date_str)
                            if year_match:
                                graduation_date = year_match.group(0)
                                break

                # Check if it's a bachelor's degree or higher
                if any(variation in edu_degree for degree_type, variations in degree_mappings.items()
                       for variation in variations if degree_type in ["bachelor", "master", "phd"]):
                    education_match = True
                    matched_degree = edu["degree"]
                    if graduation_date:
                        matched_degree += f" (Graduation: {graduation_date})"
                    break

                # Special case for common degree abbreviations
                if "cse" in edu_degree or "aida" in edu_degree or "b.tech" in edu_degree or "btech" in edu_degree:
                    education_match = True
                    matched_degree = edu["degree"]
                    if graduation_date:
                        matched_degree += f" (Graduation: {graduation_date})"
                    break

    # Calculate academic score
    if education_match:
        # Base score for meeting education requirements
        base_score = 5

        # Additional score based on GPA
        gpa_score = 0
        if max_gpa >= 90:
            gpa_score = 5  # Excellent
        elif max_gpa >= 80:
            gpa_score = 4  # Very good
        elif max_gpa >= 70:
            gpa_score = 3  # Good
        elif max_gpa >= 60:
            gpa_score = 2  # Average
        elif max_gpa > 0:
            gpa_score = 1  # Below average but still has a score

        scores["academic_match"] = base_score + gpa_score

        if matched_degree and matched_requirement:
            rationale["academic_match"] = f"Education requirements met: '{matched_degree}' matches requirement '{matched_requirement}'"
        else:
            rationale["academic_match"] = "Education requirements met"

        if max_gpa > 0:
            rationale["academic_match"] += f" with academic performance of {max_gpa:.1f}%"
    else:
        scores["academic_match"] = 0

        # Provide more detailed rationale about why the match failed
        if not education_entries:
            rationale["academic_match"] = "Education requirements not met: No education information found in resume"
        elif not education_requirements:
            rationale["academic_match"] = "Education requirements not met: No specific education requirements found in job description"
        else:
            # List the candidate's education and the job requirements for clarity
            candidate_degrees = [edu.get("degree", "Unknown degree") for edu in education_entries if isinstance(edu, dict) and "degree" in edu]
            if candidate_degrees:
                degrees_str = ", ".join(f"'{degree}'" for degree in candidate_degrees)
                reqs_str = ", ".join(f"'{req}'" for req in education_requirements)
                rationale["academic_match"] = f"Education requirements not met: Candidate's education ({degrees_str}) does not match job requirements ({reqs_str})"
            else:
                rationale["academic_match"] = "Education requirements not met: No valid degree information found in resume"

    # 7. Alma Mater (5%)
    top_universities = [
        "mit", "stanford", "harvard", "cambridge", "oxford", "caltech", "eth zurich",
        "imperial college", "ucl", "chicago", "national university of singapore", "princeton",
        "cornell", "yale", "columbia", "tsinghua", "peking", "tokyo", "melbourne", "sydney",
        "toronto", "mcgill", "iit", "indian institute of technology", "bits", "nit", "iisc"
    ]

    alma_mater_score = 0
    top_universities_found = []

    for edu in education_entries:
        if isinstance(edu, dict) and "institution" in edu and isinstance(edu["institution"], str):
            institution = edu["institution"].lower()
            for uni in top_universities:
                if uni in institution:
                    alma_mater_score = 5
                    top_universities_found.append(edu["institution"])
                    break

    scores["alma_mater"] = alma_mater_score
    if top_universities_found:
        rationale["alma_mater"] = f"Graduated from prestigious institution(s): {', '.join(top_universities_found)}"
    else:
        rationale["alma_mater"] = "No top-ranked universities found in education history"

    # 8. Certifications (5%)
    certifications = resume_data.get("certifications", []) if isinstance(resume_data.get("certifications"), list) else []
    certification_score = 0
    relevant_certifications = []

    # Check if certifications are relevant to required skills using semantic matching
    all_jd_skills_list = jd_required_skills_list + jd_preferred_skills_list

    # Filter out empty skills
    valid_jd_skills = [s for s in all_jd_skills_list if s]

    for cert in certifications:
        cert_name = ""
        if isinstance(cert, dict) and "name" in cert:
            cert_name = cert["name"]
        elif isinstance(cert, str):
            cert_name = cert

        if cert_name:
            # Check if any JD skill matches this certification using semantic matching
            for skill in valid_jd_skills:
                if is_skill_match(cert_name, skill):
                    certification_score = min(5, certification_score + 2)  # Max 5 points
                    if cert_name not in relevant_certifications:  # Avoid duplicates
                        relevant_certifications.append(cert_name)
                    break

    scores["certifications"] = certification_score
    if relevant_certifications:
        rationale["certifications"] = f"Relevant certifications: {', '.join(relevant_certifications)}"
    else:
        rationale["certifications"] = "No relevant certifications found"

    # Calculate total score (out of 100)
    total_score = sum(scores.values())

    # Determine fit category
    fit_category = ""
    if total_score >= 85:
        fit_category = "Excellent Match"
    elif total_score >= 70:
        fit_category = "Strong Match"
    elif total_score >= 55:
        fit_category = "Good Match"
    elif total_score >= 40:
        fit_category = "Moderate Match"
    else:
        fit_category = "Weak Match"

    # Create summary
    summary = f"The candidate is a {fit_category.lower()} for this position with a score of {total_score}/100. "

    # Add key strengths and weaknesses
    strengths = []
    weaknesses = []

    for category, score in scores.items():
        max_score = 25 if category == "skills_match_direct" else 20 if category == "experience_match" else 15 if category == "skills_match_subjective" else 10
        percentage = (score / max_score) * 100

        if percentage >= 80:
            strengths.append(category.replace("_", " ").title())
        elif percentage <= 30:
            weaknesses.append(category.replace("_", " ").title())

    if strengths:
        summary += f"Key strengths: {', '.join(strengths)}. "

    if weaknesses:
        summary += f"Areas for improvement: {', '.join(weaknesses)}."

    # Return comprehensive result
    return {
        "total_score": total_score,
        "fit_category": fit_category,
        "summary": summary,
        "detailed_scores": scores,
        "detailed_rationale": rationale
    }

# New CGPA-style scoring functions for /intervet_new endpoint

def calculate_skills_score_new(resume_data: Dict, jd_data: Dict) -> Tuple[float, str, Dict]:
    """
    Calculate skills matching score for CGPA-style system (0-10 scale).
    Enhanced with detailed step-by-step calculation logging and better matching logic.

    Returns:
        Tuple of (raw_score, rationale, details)
    """
    logger.info("Calculating skills score for CGPA system")

    # Step 1: Extract skills from resume
    resume_skills_list = []
    if "skills" in resume_data:
        if isinstance(resume_data["skills"], list):
            resume_skills_list = resume_data["skills"]
        elif isinstance(resume_data["skills"], dict):
            resume_skills_list = list(resume_data["skills"].keys())

    # Step 2: Extract skills from JD
    jd_required_skills_list = []
    jd_preferred_skills_list = []

    if "required_skills" in jd_data and isinstance(jd_data["required_skills"], list):
        jd_required_skills_list = jd_data["required_skills"]

    if "preferred_skills" in jd_data and isinstance(jd_data["preferred_skills"], list):
        jd_preferred_skills_list = jd_data["preferred_skills"]

    # Step 3: Calculate direct skills match using semantic matching
    matched_required = []
    missing_required = []
    matched_preferred = []

    if jd_required_skills_list:
        matched_required, missing_required = find_matching_skills(resume_skills_list, jd_required_skills_list)

    if jd_preferred_skills_list:
        matched_preferred, _ = find_matching_skills(resume_skills_list, jd_preferred_skills_list)

    # Step 4: Calculate score with detailed breakdown (0-10 scale)
    calculation_steps = []

    if jd_required_skills_list:
        required_match_ratio = len(matched_required) / len(jd_required_skills_list)
        base_score = required_match_ratio * 8  # Max 8 points for required skills
        calculation_steps.append(f"Step 1: Required skills score = {len(matched_required)}/{len(jd_required_skills_list)} × 8 = {base_score:.2f}")

        # Bonus for preferred skills (max 2 points)
        preferred_bonus = 0
        if jd_preferred_skills_list:
            preferred_match_ratio = len(matched_preferred) / len(jd_preferred_skills_list)
            preferred_bonus = preferred_match_ratio * 2
            calculation_steps.append(f"Step 2: Preferred skills bonus = {len(matched_preferred)}/{len(jd_preferred_skills_list)} × 2 = {preferred_bonus:.2f}")
        else:
            calculation_steps.append("Step 2: No preferred skills specified, bonus = 0.00")

        raw_score = min(10, base_score + preferred_bonus)
        calculation_steps.append(f"Step 3: Total score = min(10, {base_score:.2f} + {preferred_bonus:.2f}) = {raw_score:.2f}")
    else:
        raw_score = 5.0  # Neutral score if no skills specified
        calculation_steps.append("No required skills specified in job description, using neutral score = 5.00")

    # Step 5: Generate comprehensive rationale
    if jd_required_skills_list:
        rationale = f"Matched {len(matched_required)}/{len(jd_required_skills_list)} required skills"
        if jd_preferred_skills_list:
            rationale += f" and {len(matched_preferred)}/{len(jd_preferred_skills_list)} preferred skills"

        if matched_required:
            rationale += f". Matched: {', '.join(matched_required)}"
        if missing_required:
            rationale += f". Missing: {', '.join(missing_required)}"
    else:
        rationale = "No required skills specified in job description"

    # Step 6: Create detailed breakdown (calculation steps kept for backend logging only)
    details = {
        "resume_skills_count": len(resume_skills_list),
        "required_skills_count": len(jd_required_skills_list),
        "preferred_skills_count": len(jd_preferred_skills_list),
        "matched_required": matched_required,
        "missing_required": missing_required,
        "matched_preferred": matched_preferred,
        "required_match_ratio": len(matched_required) / len(jd_required_skills_list) if jd_required_skills_list else 0,
        "preferred_match_ratio": len(matched_preferred) / len(jd_preferred_skills_list) if jd_preferred_skills_list else 0,
        # Backend logging details (not shown in API response)
        "_calculation_steps": calculation_steps,
        "_scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))",
        "_explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)"
    }

    logger.info(f"Skills score: {raw_score:.2f}/10 - {rationale}")
    return raw_score, rationale, details

def calculate_experience_score_new(resume_data: Dict, jd_data: Dict) -> Tuple[float, str, Dict]:
    """
    Calculate experience matching score for CGPA-style system (0-10 scale).
    Enhanced with detailed step-by-step calculation logging and better experience parsing.

    Returns:
        Tuple of (raw_score, rationale, details)
    """
    logger.info("Calculating experience score for CGPA system")

    # Step 1: Extract required years of experience from JD
    required_yoe = None
    calculation_steps = []

    if "required_experience" in jd_data and jd_data["required_experience"]:
        yoe_match = re.search(r'(\d+)[\+\-]?\s*(?:year|yr|yrs|years)', jd_data["required_experience"].lower())
        if yoe_match:
            required_yoe = int(yoe_match.group(1))
            calculation_steps.append(f"Step 1: Required experience extracted: {required_yoe} years from '{jd_data['required_experience']}'")
        else:
            calculation_steps.append(f"Step 1: Could not extract numeric experience from '{jd_data['required_experience']}'")
    else:
        calculation_steps.append("Step 1: No required experience specified in job description")

    # Step 2: Calculate candidate's total years of experience with detailed breakdown
    candidate_yoe = 0
    experience_breakdown = []

    if "experience" in resume_data and isinstance(resume_data["experience"], list):
        calculation_steps.append(f"Step 2: Analyzing {len(resume_data['experience'])} experience entries")

        for i, exp in enumerate(resume_data["experience"]):
            if isinstance(exp, dict) and "duration" in exp:
                duration = exp["duration"]
                company = exp.get("company", "Unknown Company")
                position = exp.get("position", "Unknown Position")

                if isinstance(duration, str):
                    years_for_this_job = 0

                    # Try to extract years from duration strings
                    year_match = re.search(r'(\d{4})\s*-\s*(\d{4}|\bpresent\b)', duration.lower())
                    if year_match:
                        start_year = int(year_match.group(1))
                        end_year = 2024  # Current year
                        if year_match.group(2).isdigit():
                            end_year = int(year_match.group(2))
                        years_for_this_job = max(0, end_year - start_year)
                        experience_breakdown.append({
                            "company": company,
                            "position": position,
                            "duration": duration,
                            "years_calculated": years_for_this_job,
                            "calculation_method": f"Year range: {start_year} to {end_year}"
                        })
                        calculation_steps.append(f"  Entry {i+1}: {company} - {position} ({duration}) = {years_for_this_job} years")
                    else:
                        # Try direct year specification
                        direct_years = re.search(r'(\d+)\s*(?:year|yr|yrs|years)', duration.lower())
                        if direct_years:
                            years_for_this_job = int(direct_years.group(1))
                            experience_breakdown.append({
                                "company": company,
                                "position": position,
                                "duration": duration,
                                "years_calculated": years_for_this_job,
                                "calculation_method": "Direct year specification"
                            })
                            calculation_steps.append(f"  Entry {i+1}: {company} - {position} ({duration}) = {years_for_this_job} years")
                        else:
                            experience_breakdown.append({
                                "company": company,
                                "position": position,
                                "duration": duration,
                                "years_calculated": 0,
                                "calculation_method": "Could not parse duration"
                            })
                            calculation_steps.append(f"  Entry {i+1}: {company} - {position} ({duration}) = 0 years (could not parse)")

                    candidate_yoe += years_for_this_job

        calculation_steps.append(f"Step 2 Result: Total candidate experience = {candidate_yoe} years")
    else:
        calculation_steps.append("Step 2: No experience entries found in resume")

    # Step 3: Calculate score with detailed breakdown (0-10 scale)
    calculation_steps.append("Step 3: Calculating experience score")

    if required_yoe is not None and candidate_yoe > 0:
        experience_ratio = candidate_yoe / required_yoe
        calculation_steps.append(f"  Experience ratio: {candidate_yoe}/{required_yoe} = {experience_ratio:.2f}")

        if 0.8 <= experience_ratio <= 1.5:
            # Ideal range: 80% to 150% of required experience
            raw_score = 10.0
            calculation_steps.append(f"  ✓ Ideal range (0.8-1.5): Score = 10.0")
            rationale = f"Excellent match: {candidate_yoe} years vs required {required_yoe} years (ratio: {experience_ratio:.2f})"
        elif 0.6 <= experience_ratio < 0.8:
            # Slightly under-experienced
            raw_score = 7.0 + (experience_ratio - 0.6) * 15  # 7-10 range
            calculation_steps.append(f"  ~ Slightly under-experienced (0.6-0.8): Score = 7.0 + ({experience_ratio:.2f} - 0.6) × 15 = {raw_score:.2f}")
            rationale = f"Good match: {candidate_yoe} years vs required {required_yoe} years (slightly under, ratio: {experience_ratio:.2f})"
        elif 1.5 < experience_ratio <= 2.5:
            # Over-experienced but acceptable
            raw_score = 8.0
            calculation_steps.append(f"  ~ Over-experienced but acceptable (1.5-2.5): Score = 8.0")
            rationale = f"Good match: {candidate_yoe} years vs required {required_yoe} years (over-experienced, ratio: {experience_ratio:.2f})"
        elif experience_ratio > 2.5:
            # Significantly over-experienced
            raw_score = 6.0
            calculation_steps.append(f"  ~ Significantly over-experienced (>2.5): Score = 6.0")
            rationale = f"Moderate match: {candidate_yoe} years vs required {required_yoe} years (significantly over-experienced, ratio: {experience_ratio:.2f})"
        else:
            # Under-experienced
            raw_score = max(2.0, experience_ratio * 10)
            calculation_steps.append(f"  ✗ Under-experienced (<0.6): Score = max(2.0, {experience_ratio:.2f} × 10) = {raw_score:.2f}")
            rationale = f"Below requirements: {candidate_yoe} years vs required {required_yoe} years (ratio: {experience_ratio:.2f})"
    else:
        raw_score = 5.0  # Neutral score
        if required_yoe is None:
            calculation_steps.append(f"  ~ No experience requirement specified: Score = 5.0 (neutral)")
            rationale = "No specific experience requirement found in job description"
        else:
            calculation_steps.append(f"  ✗ Could not determine candidate experience: Score = 5.0 (neutral)")
            rationale = f"Could not determine candidate's experience to compare with required {required_yoe} years"

    # Step 4: Create detailed breakdown (calculation steps kept for backend logging only)
    details = {
        "candidate_yoe": candidate_yoe,
        "required_yoe": required_yoe,
        "experience_ratio": candidate_yoe / required_yoe if required_yoe and required_yoe > 0 else None,
        "experience_entries_count": len(resume_data.get("experience", [])),
        # Backend logging details (not shown in API response)
        "_experience_breakdown": experience_breakdown,
        "_calculation_steps": calculation_steps,
        "_scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x",
        "_explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification"
    }

    logger.info(f"Experience score: {raw_score:.2f}/10 - {rationale}")
    return raw_score, rationale, details

def calculate_education_score_new(resume_data: Dict, jd_data: Dict) -> Tuple[float, str, Dict]:
    """
    Calculate education matching score for CGPA-style system (0-10 scale).
    Uses binary scoring: 10 for having required education, 0 for not having it.
    Provides benefit of doubt for related degrees with partial scoring.

    Returns:
        Tuple of (raw_score, rationale, details)
    """
    logger.info("Calculating education score for CGPA system")

    education_entries = resume_data.get("education", []) if isinstance(resume_data.get("education"), list) else []
    education_requirements = jd_data.get("education_requirements", []) if isinstance(jd_data.get("education_requirements"), list) else []

    # Define degree mappings for semantic matching (using word boundaries to avoid false matches)
    degree_mappings = {
        "bachelor": ["bachelor", "bachelors", "b.tech", "b.e.", "b.s.", "b.a.", "undergraduate", "btech", "be", "bs", "ba", "bsc", "bca"],
        "master": ["master", "masters", "m.tech", "m.e.", "m.s.", "m.a.", "graduate", "mtech", "me", "ms", "ma", "msc", "mca"],
        "phd": ["phd", "ph.d", "ph.d.", "doctorate", "doctoral"],
        "associate": ["associate", "diploma", "a.s.", "a.a."],
        "intermediate": ["intermediate", "12th", "higher secondary", "senior secondary"],
        "matriculation": ["matriculation", "10th", "secondary", "high school"]
    }

    field_mappings = {
        "computer science": ["computer science", "cs", "cse", "computer engineering", "computing", "software engineering", "information technology", "it", "information systems", "aida", "ai", "artificial intelligence", "data analytics", "data science"],
        "engineering": ["engineering", "engineer", "technology"],
        "business": ["business", "management", "mba", "administration", "finance", "marketing", "economics"],
        "science": ["science", "physics", "chemistry", "biology", "mathematics", "math", "statistics"],
        "arts": ["arts", "humanities", "liberal arts", "social sciences", "psychology", "sociology", "philosophy"]
    }

    # Step 1: Initialize tracking variables
    education_match = False
    partial_match = False
    matched_degree = ""
    matched_requirement = ""
    match_type = ""
    calculation_steps = []

    # Step 2: Check for education match
    if education_requirements:
        calculation_steps.append(f"Step 1: Checking {len(education_requirements)} education requirement(s)")

        for req in education_requirements:
            req_lower = req.lower()
            calculation_steps.append(f"  - Analyzing requirement: '{req}'")

            # Extract degree type and field from requirement using word boundaries
            import re
            req_degree_type = None
            for degree_type, variations in degree_mappings.items():
                for variation in variations:
                    # Use word boundaries to avoid false matches like "me" in "intermediate"
                    pattern = r'\b' + re.escape(variation) + r'\b'
                    if re.search(pattern, req_lower):
                        req_degree_type = degree_type
                        break
                if req_degree_type:
                    break

            req_field = None
            for field, variations in field_mappings.items():
                for variation in variations:
                    pattern = r'\b' + re.escape(variation) + r'\b'
                    if re.search(pattern, req_lower):
                        req_field = field
                        break
                if req_field:
                    break

            calculation_steps.append(f"    Required degree type: {req_degree_type or 'Not specified'}")
            calculation_steps.append(f"    Required field: {req_field or 'Not specified'}")

            # Check each education entry
            for edu in education_entries:
                if isinstance(edu, dict) and "degree" in edu and isinstance(edu["degree"], str):
                    edu_degree = edu["degree"].lower()

                    # Extract degree type and field from education using word boundaries
                    edu_degree_type = None
                    for degree_type, variations in degree_mappings.items():
                        for variation in variations:
                            # Use word boundaries to avoid false matches like "me" in "intermediate"
                            pattern = r'\b' + re.escape(variation) + r'\b'
                            if re.search(pattern, edu_degree):
                                edu_degree_type = degree_type
                                break
                        if edu_degree_type:
                            break

                    edu_field = None
                    for field, variations in field_mappings.items():
                        for variation in variations:
                            pattern = r'\b' + re.escape(variation) + r'\b'
                            if re.search(pattern, edu_degree):
                                edu_field = field
                                break
                        if edu_field:
                            break

                    calculation_steps.append(f"    Candidate degree: '{edu['degree']}' (Type: {edu_degree_type or 'Unknown'}, Field: {edu_field or 'Unknown'})")

                    # Check for exact match
                    if req_degree_type and edu_degree_type:
                        if req_degree_type == edu_degree_type:
                            if not req_field or not edu_field or req_field == edu_field or (req_field == "engineering" and edu_field == "computer science"):
                                education_match = True
                                matched_degree = edu["degree"]
                                matched_requirement = req
                                match_type = "exact_match"
                                calculation_steps.append(f"    ✓ EXACT MATCH FOUND: Degree type and field match")
                                break
                        elif edu_degree_type in ["master", "phd"] and req_degree_type == "bachelor":
                            # Higher degree than required
                            education_match = True
                            matched_degree = edu["degree"]
                            matched_requirement = req
                            match_type = "higher_degree"
                            calculation_steps.append(f"    ✓ HIGHER DEGREE MATCH: {edu_degree_type} exceeds {req_degree_type} requirement")
                            break
                        elif req_field and edu_field and req_field == edu_field:
                            # Same field, different degree level - partial match
                            partial_match = True
                            matched_degree = edu["degree"]
                            matched_requirement = req
                            match_type = "field_match"
                            calculation_steps.append(f"    ~ PARTIAL MATCH: Same field ({req_field}) but different degree level")
                    else:
                        # Skip requirements that don't actually specify education credentials
                        # Only process requirements that contain actual degree-related terms
                        degree_related_terms = ["degree", "bachelor", "master", "phd", "diploma", "certificate", "graduation", "education", "qualification", "university", "college", "school"]

                        # Check if this requirement actually mentions education credentials
                        has_degree_terms = any(term in req_lower for term in degree_related_terms)

                        if has_degree_terms:
                            # Check for meaningful matches between degree and requirement
                            edu_terms = set(edu_degree.split())
                            req_terms = set(req_lower.split())
                            # Only match if there are significant overlapping terms (not just common words)
                            meaningful_overlap = edu_terms.intersection(req_terms) - {
                                "in", "of", "the", "and", "or", "a", "an", "with", "for", "to", "from",
                                "understanding", "complete", "application", "development", "life", "cycle",
                                "knowledge", "experience", "skills", "ability", "proficiency"
                            }
                            if meaningful_overlap and len(meaningful_overlap) >= 2:
                                partial_match = True
                                matched_degree = edu["degree"]
                                matched_requirement = req
                                match_type = "meaningful_keyword_match"
                                calculation_steps.append(f"    ~ MEANINGFUL KEYWORD MATCH: Overlapping terms: {meaningful_overlap}")
                        else:
                            calculation_steps.append(f"    ✗ SKIPPED: '{req}' is not an education requirement (no degree-related terms found)")
                            calculation_steps.append(f"    ~ PARTIAL MATCH: Keyword similarity found")

            if education_match:
                break
    else:
        # If no specific requirements, check for any bachelor's or higher
        calculation_steps.append("Step 1: No specific education requirements, checking for any degree")
        for edu in education_entries:
            if isinstance(edu, dict) and "degree" in edu and isinstance(edu["degree"], str):
                edu_degree = edu["degree"].lower()
                if any(variation in edu_degree for degree_type, variations in degree_mappings.items()
                       for variation in variations if degree_type in ["bachelor", "master", "phd"]):
                    education_match = True
                    matched_degree = edu["degree"]
                    match_type = "general_degree"
                    calculation_steps.append(f"    ✓ DEGREE FOUND: '{edu['degree']}'")
                    break

    # Step 3: Binary scoring with benefit of doubt
    calculation_steps.append("Step 2: Applying binary scoring system")

    if education_match:
        raw_score = 10.0  # Full score for meeting requirements
        calculation_steps.append(f"  ✓ Education requirement met: Score = 10.0")

        rationale = f"Education requirements fully met: '{matched_degree}'"
        if matched_requirement:
            rationale += f" matches requirement '{matched_requirement}'"
        rationale += f" (Match type: {match_type})"

    elif partial_match:
        # Benefit of doubt for related degrees
        raw_score = 6.0  # Partial score for related education
        calculation_steps.append(f"  ~ Partial education match (benefit of doubt): Score = 6.0")

        rationale = f"Partial education match (benefit of doubt): '{matched_degree}'"
        if matched_requirement:
            rationale += f" partially matches requirement '{matched_requirement}'"
        rationale += f" (Match type: {match_type})"

    else:
        raw_score = 0.0  # No score for not meeting requirements
        calculation_steps.append(f"  ✗ Education requirements not met: Score = 0.0")

        if not education_entries:
            rationale = "No education information found in resume"
        elif not education_requirements:
            rationale = "No specific education requirements in job description, neutral scoring applied"
            raw_score = 5.0  # Neutral score when no requirements specified
            calculation_steps[-1] = f"  ~ No education requirements specified: Score = 5.0"
        else:
            candidate_degrees = [edu.get("degree", "Unknown") for edu in education_entries if isinstance(edu, dict)]
            rationale = f"Education requirements not met. Candidate: {', '.join(candidate_degrees)}, Required: {', '.join(education_requirements)}"

    # Step 4: Check for top universities (for alma mater assessment)
    top_universities = [
        "mit", "stanford", "harvard", "cambridge", "oxford", "caltech", "eth zurich",
        "imperial college", "ucl", "chicago", "national university of singapore", "princeton",
        "cornell", "yale", "columbia", "tsinghua", "peking", "tokyo", "melbourne", "sydney",
        "toronto", "mcgill", "iit", "indian institute of technology", "bits", "nit", "iisc"
    ]

    top_universities_found = []
    for edu in education_entries:
        if isinstance(edu, dict) and "institution" in edu and isinstance(edu["institution"], str):
            institution = edu["institution"].lower()
            for uni in top_universities:
                if uni in institution:
                    top_universities_found.append(edu["institution"])
                    break

    # Step 5: Create detailed breakdown (calculation steps kept for backend logging only)
    details = {
        "education_entries_count": len(education_entries),
        "education_requirements_count": len(education_requirements),
        "education_match": education_match,
        "partial_match": partial_match,
        "matched_degree": matched_degree,
        "matched_requirement": matched_requirement,
        "match_type": match_type,
        "candidate_degrees": [edu.get("degree", "Unknown") for edu in education_entries if isinstance(edu, dict)],
        "required_degrees": education_requirements,
        "education_requirements": education_requirements,  # For detailed rationale
        "top_universities_found": top_universities_found,
        # Backend logging details (not shown in API response)
        "_calculation_steps": calculation_steps,
        "_scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements",
        "_explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)"
    }

    logger.info(f"Education score: {raw_score:.2f}/10 - {rationale}")
    return raw_score, rationale, details

def calculate_certifications_score_new(resume_data: Dict, jd_data: Dict) -> Tuple[float, str, Dict]:
    """
    Calculate certifications score for CGPA-style system (0-10 scale).
    Enhanced with detailed step-by-step calculation logging and better relevance matching.

    Returns:
        Tuple of (raw_score, rationale, details)
    """
    logger.info("Calculating certifications score for CGPA system")

    # Step 1: Extract certifications from resume
    certifications = resume_data.get("certifications", []) if isinstance(resume_data.get("certifications"), list) else []
    calculation_steps = []
    calculation_steps.append(f"Step 1: Found {len(certifications)} certifications in resume")

    # Step 2: Get all JD skills for relevance checking
    jd_required_skills_list = jd_data.get("required_skills", []) if isinstance(jd_data.get("required_skills"), list) else []
    jd_preferred_skills_list = jd_data.get("preferred_skills", []) if isinstance(jd_data.get("preferred_skills"), list) else []
    all_jd_skills = jd_required_skills_list + jd_preferred_skills_list
    calculation_steps.append(f"Step 2: Checking relevance against {len(all_jd_skills)} job skills ({len(jd_required_skills_list)} required + {len(jd_preferred_skills_list)} preferred)")

    # Step 3: Analyze each certification for relevance
    relevant_certifications = []
    irrelevant_certifications = []
    certification_analysis = []

    for i, cert in enumerate(certifications):
        cert_name = ""
        if isinstance(cert, dict) and "name" in cert:
            cert_name = cert["name"]
        elif isinstance(cert, str):
            cert_name = cert

        if cert_name:
            is_relevant = False
            matched_skills = []

            # Check if certification is relevant to any JD skill
            for skill in all_jd_skills:
                if skill and is_skill_match(cert_name, skill):
                    is_relevant = True
                    matched_skills.append(skill)

            if is_relevant:
                relevant_certifications.append(cert_name)
                certification_analysis.append({
                    "certification": cert_name,
                    "relevant": True,
                    "matched_skills": matched_skills,
                    "points_awarded": 2
                })
                calculation_steps.append(f"  Cert {i+1}: '{cert_name}' - RELEVANT (matches: {', '.join(matched_skills)}) = +2 points")
            else:
                irrelevant_certifications.append(cert_name)
                certification_analysis.append({
                    "certification": cert_name,
                    "relevant": False,
                    "matched_skills": [],
                    "points_awarded": 0
                })
                calculation_steps.append(f"  Cert {i+1}: '{cert_name}' - NOT RELEVANT = +0 points")

    # Step 4: Calculate final score (0-10 scale)
    base_score = len(relevant_certifications) * 2  # 2 points per relevant certification
    raw_score = min(10.0, base_score)

    calculation_steps.append(f"Step 3: Score calculation")
    calculation_steps.append(f"  Base score: {len(relevant_certifications)} relevant certs × 2 points = {base_score}")
    calculation_steps.append(f"  Final score: min(10, {base_score}) = {raw_score}")

    # Step 5: Generate comprehensive rationale
    if relevant_certifications:
        rationale = f"Found {len(relevant_certifications)} relevant certifications: {', '.join(relevant_certifications)}"
        if irrelevant_certifications:
            rationale += f". Also has {len(irrelevant_certifications)} other certifications not directly relevant to this role"
    else:
        if certifications:
            rationale = f"Has {len(certifications)} certifications but none are directly relevant to job requirements: {', '.join([cert.get('name', cert) if isinstance(cert, dict) else cert for cert in certifications])}"
        else:
            rationale = "No certifications found in resume"

    # Step 6: Create detailed breakdown (calculation steps kept for backend logging only)
    details = {
        "total_certifications": len(certifications),
        "relevant_certifications": relevant_certifications,
        "relevant_count": len(relevant_certifications),
        "all_certifications": [cert.get("name", cert) if isinstance(cert, dict) else cert for cert in certifications],
        # Backend logging details (not shown in API response)
        "_irrelevant_certifications": irrelevant_certifications,
        "_certification_analysis": certification_analysis,
        "_calculation_steps": calculation_steps,
        "_scoring_formula": "Score = min(10, relevant_certifications_count × 2)",
        "_explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10"
    }

    logger.info(f"Certifications score: {raw_score:.2f}/10 - {rationale}")
    return raw_score, rationale, details

def calculate_location_score_new(resume_data: Dict, jd_data: Dict) -> Tuple[float, str, Dict]:
    """
    Calculate location matching score for CGPA-style system (0-10 scale).
    Enhanced with detailed step-by-step calculation logging and better location matching.

    Returns:
        Tuple of (raw_score, rationale, details)
    """
    logger.info("Calculating location score for CGPA system")

    # Step 1: Extract location information
    jd_location = jd_data.get("location", "").lower() if isinstance(jd_data.get("location"), str) else ""
    resume_location = resume_data.get("location", "").lower() if isinstance(resume_data.get("location"), str) else ""

    calculation_steps = []
    calculation_steps.append(f"Step 1: Location extraction")
    calculation_steps.append(f"  Job location: '{jd_location}' (from JD)")
    calculation_steps.append(f"  Resume location: '{resume_location}' (from resume)")

    # Step 2: Extract experience locations
    experience_locations = []
    if "experience" in resume_data and isinstance(resume_data["experience"], list):
        for exp in resume_data["experience"]:
            if isinstance(exp, dict) and "location" in exp and isinstance(exp["location"], str):
                experience_locations.append(exp["location"].lower())

    calculation_steps.append(f"  Experience locations: {experience_locations} (from work history)")

    # Step 3: Calculate score with detailed logic
    calculation_steps.append("Step 2: Location matching analysis")
    location_match_found = False

    if jd_location and resume_location:
        if jd_location in resume_location or resume_location in jd_location:
            raw_score = 10.0
            location_match_found = True
            calculation_steps.append(f"  ✓ Current location match: Score = 10.0")
            rationale = f"Current location ({resume_location}) matches job location ({jd_location})"
        elif any(jd_location in loc or loc in jd_location for loc in experience_locations):
            raw_score = 7.0
            location_match_found = True
            matched_exp_locations = [loc for loc in experience_locations if jd_location in loc or loc in jd_location]
            calculation_steps.append(f"  ~ Previous work location match: Score = 7.0 (matched: {matched_exp_locations})")
            rationale = f"Previous work location matches job location ({jd_location})"
        else:
            raw_score = 3.0
            location_match_found = False
            calculation_steps.append(f"  ✗ No location match: Score = 3.0")
            rationale = f"Location mismatch: candidate in {resume_location}, job in {jd_location}"
    elif jd_location and experience_locations:
        if any(jd_location in loc or loc in jd_location for loc in experience_locations):
            raw_score = 7.0
            location_match_found = True
            matched_exp_locations = [loc for loc in experience_locations if jd_location in loc or loc in jd_location]
            calculation_steps.append(f"  ~ Experience location match (no current location): Score = 7.0 (matched: {matched_exp_locations})")
            rationale = f"Previous work location matches job location ({jd_location})"
        else:
            raw_score = 3.0
            location_match_found = False
            calculation_steps.append(f"  ✗ No location match found: Score = 3.0")
            rationale = f"No location match found with job location ({jd_location})"
    else:
        raw_score = 5.0  # Neutral score
        location_match_found = False
        calculation_steps.append(f"  ~ Insufficient location data: Score = 5.0 (neutral)")
        rationale = "Location information not available for comparison"

    # Step 4: Create detailed breakdown (calculation steps kept for backend logging only)
    details = {
        "jd_location": jd_location,
        "resume_location": resume_location,
        "experience_locations": experience_locations,
        "location_match_found": location_match_found,
        "has_location_info": bool(jd_location and (resume_location or experience_locations)),
        # Backend logging details (not shown in API response)
        "_calculation_steps": calculation_steps,
        "_scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data",
        "_explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"
    }

    logger.info(f"Location score: {raw_score:.2f}/10 - {rationale}")
    return raw_score, rationale, details

def calculate_reliability_score_new(resume_data: Dict, jd_data: Dict) -> Tuple[float, str, Dict]:
    """
    Calculate job stability/reliability score for CGPA-style system (0-10 scale).
    Enhanced with detailed step-by-step calculation logging and better tenure analysis.
    Note: jd_data parameter is kept for consistency but not used in reliability calculation.

    Returns:
        Tuple of (raw_score, rationale, details)
    """
    logger.info("Calculating reliability score for CGPA system")

    # Step 1: Calculate total years of experience with detailed breakdown
    candidate_yoe = 0
    tenure_breakdown = []
    calculation_steps = []

    if "experience" in resume_data and isinstance(resume_data["experience"], list):
        calculation_steps.append(f"Step 1: Analyzing {len(resume_data['experience'])} experience entries for tenure calculation")

        for i, exp in enumerate(resume_data["experience"]):
            if isinstance(exp, dict) and "duration" in exp:
                duration = exp["duration"]
                company = exp.get("company", "Unknown Company")

                if isinstance(duration, str):
                    years_for_this_job = 0

                    # Extract years from duration
                    year_match = re.search(r'(\d{4})\s*-\s*(\d{4}|\bpresent\b)', duration.lower())
                    if year_match:
                        start_year = int(year_match.group(1))
                        end_year = 2024  # Current year
                        if year_match.group(2).isdigit():
                            end_year = int(year_match.group(2))
                        years_for_this_job = max(0, end_year - start_year)
                        tenure_breakdown.append({
                            "company": company,
                            "duration": duration,
                            "years_calculated": years_for_this_job,
                            "calculation_method": f"Year range: {start_year} to {end_year}"
                        })
                        calculation_steps.append(f"  Entry {i+1}: {company} ({duration}) = {years_for_this_job} years")
                    else:
                        direct_years = re.search(r'(\d+)\s*(?:year|yr|yrs|years)', duration.lower())
                        if direct_years:
                            years_for_this_job = int(direct_years.group(1))
                            tenure_breakdown.append({
                                "company": company,
                                "duration": duration,
                                "years_calculated": years_for_this_job,
                                "calculation_method": "Direct year specification"
                            })
                            calculation_steps.append(f"  Entry {i+1}: {company} ({duration}) = {years_for_this_job} years")
                        else:
                            tenure_breakdown.append({
                                "company": company,
                                "duration": duration,
                                "years_calculated": 0,
                                "calculation_method": "Could not parse duration"
                            })
                            calculation_steps.append(f"  Entry {i+1}: {company} ({duration}) = 0 years (could not parse)")

                    candidate_yoe += years_for_this_job

        calculation_steps.append(f"Step 1 Result: Total experience = {candidate_yoe} years")
    else:
        calculation_steps.append("Step 1: No experience entries found in resume")

    num_companies = len(resume_data.get("experience", [])) if isinstance(resume_data.get("experience"), list) else 0

    # Step 2: Calculate average tenure and reliability score
    calculation_steps.append("Step 2: Calculating job stability/reliability")
    calculation_steps.append(f"  Total companies: {num_companies}")
    calculation_steps.append(f"  Total years: {candidate_yoe}")

    if candidate_yoe > 0 and num_companies > 0:
        avg_tenure = candidate_yoe / num_companies
        calculation_steps.append(f"  Average tenure: {candidate_yoe}/{num_companies} = {avg_tenure:.2f} years per company")

        # Score based on average tenure (0-10 scale)
        if avg_tenure >= 4:
            raw_score = 10.0
            calculation_steps.append(f"  ✓ Excellent stability (≥4 years): Score = 10.0")
            rationale = f"Excellent stability: average {avg_tenure:.1f} years per company"
        elif avg_tenure >= 3:
            raw_score = 8.5
            calculation_steps.append(f"  ✓ Very good stability (3-4 years): Score = 8.5")
            rationale = f"Very good stability: average {avg_tenure:.1f} years per company"
        elif avg_tenure >= 2:
            raw_score = 7.0
            calculation_steps.append(f"  ~ Good stability (2-3 years): Score = 7.0")
            rationale = f"Good stability: average {avg_tenure:.1f} years per company"
        elif avg_tenure >= 1.5:
            raw_score = 5.0
            calculation_steps.append(f"  ~ Moderate stability (1.5-2 years): Score = 5.0")
            rationale = f"Moderate stability: average {avg_tenure:.1f} years per company"
        elif avg_tenure >= 1:
            raw_score = 3.0
            calculation_steps.append(f"  ✗ Below average stability (1-1.5 years): Score = 3.0")
            rationale = f"Below average stability: average {avg_tenure:.1f} years per company"
        else:
            raw_score = 1.0
            calculation_steps.append(f"  ✗ Poor stability (<1 year): Score = 1.0")
            rationale = f"Poor stability: frequent job changes with {avg_tenure:.1f} years per company"
    else:
        raw_score = 5.0  # Neutral score
        calculation_steps.append(f"  ~ Insufficient data for calculation: Score = 5.0 (neutral)")
        rationale = "Could not calculate job stability due to missing experience data"

    # Step 3: Create detailed breakdown (calculation steps kept for backend logging only)
    details = {
        "candidate_yoe": candidate_yoe,
        "num_companies": num_companies,
        "avg_tenure": candidate_yoe / num_companies if num_companies > 0 else 0,
        "has_experience_data": bool(candidate_yoe > 0 and num_companies > 0),
        # Backend logging details (not shown in API response)
        "_tenure_breakdown": tenure_breakdown,
        "_calculation_steps": calculation_steps,
        "_scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year",
        "_explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history"
    }

    logger.info(f"Reliability score: {raw_score:.2f}/10 - {rationale}")
    return raw_score, rationale, details

def calculate_candidate_job_fit_new(resume_data: Dict, jd_data: Dict, weightage: Optional[WeightageConfig] = None) -> Dict:
    """
    Calculate candidate-job fit using CGPA-style scoring system.

    This function implements a credit-based scoring system where:
    1. Each field gets a raw score (0-10)
    2. Final score = sum(weight * raw_score) / sum(weights)
    3. Result is scaled to 0-10

    Args:
        resume_data: Parsed resume data
        jd_data: Parsed job description data
        weightage: Optional weightage configuration

    Returns:
        Dictionary with comprehensive scoring results
    """
    start_time = time.time()
    logger.info("Starting CGPA-style candidate-job fit calculation")

    # Use default weights if not provided
    if weightage is None:
        weightage = WeightageConfig()

    # Calculate individual field scores
    skills_raw, skills_rationale, skills_details = calculate_skills_score_new(resume_data, jd_data)
    experience_raw, experience_rationale, experience_details = calculate_experience_score_new(resume_data, jd_data)
    education_raw, education_rationale, education_details = calculate_education_score_new(resume_data, jd_data)
    certifications_raw, certifications_rationale, certifications_details = calculate_certifications_score_new(resume_data, jd_data)
    location_raw, location_rationale, location_details = calculate_location_score_new(resume_data, jd_data)
    reliability_raw, reliability_rationale, reliability_details = calculate_reliability_score_new(resume_data, jd_data)

    # Calculate weighted scores directly from raw scores
    skills_weighted = weightage.skills * skills_raw
    experience_weighted = weightage.experience * experience_raw
    education_weighted = weightage.education * education_raw
    certifications_weighted = weightage.certifications * certifications_raw
    location_weighted = weightage.location * location_raw
    reliability_weighted = weightage.reliability * reliability_raw

    # Calculate total credits and final score
    total_credits = weightage.skills + weightage.experience + weightage.education + weightage.certifications + weightage.location + weightage.reliability

    if total_credits > 0:
        total_weighted_score = skills_weighted + experience_weighted + education_weighted + certifications_weighted + location_weighted + reliability_weighted
        final_score = total_weighted_score / total_credits
    else:
        final_score = 0.0

    # Ensure score is within 0-10 range
    final_score = max(0.0, min(10.0, final_score))

    # Determine fit category
    if final_score >= 8.5:
        fit_category = "Excellent Match"
    elif final_score >= 7.0:
        fit_category = "Strong Match"
    elif final_score >= 5.5:
        fit_category = "Good Match"
    elif final_score >= 4.0:
        fit_category = "Moderate Match"
    else:
        fit_category = "Weak Match"

    # Create summary
    summary = f"The candidate is a {fit_category.lower()} for this position with a CGPA-style score of {final_score:.1f}/10. "

    # Identify strengths and weaknesses
    field_scores = {
        "Skills": skills_raw,
        "Experience": experience_raw,
        "Education": education_raw,
        "Certifications": certifications_raw,
        "Location": location_raw,
        "Reliability": reliability_raw
    }

    strengths = [field for field, score in field_scores.items() if score >= 7.5]
    weaknesses = [field for field, score in field_scores.items() if score <= 4.0]

    if strengths:
        summary += f"Key strengths: {', '.join(strengths)}. "
    if weaknesses:
        summary += f"Areas for improvement: {', '.join(weaknesses)}."

    processing_time = time.time() - start_time

    # Create user-friendly rationales for HR/hiring managers
    def create_user_friendly_rationale(field_name: str, score: float, technical_rationale: str, details: Dict) -> str:
        """Create user-friendly rationale for HR/hiring managers"""
        if field_name == "Skills":
            matched_req = len(details.get('matched_required', []))
            total_req = details.get('required_skills_count', 0)
            matched_pref = len(details.get('matched_preferred', []))
            total_pref = details.get('preferred_skills_count', 0)

            if score >= 8:
                return f"Excellent skills match - has {matched_req}/{total_req} required skills and {matched_pref}/{total_pref} preferred skills"
            elif score >= 6:
                return f"Good skills match - has {matched_req}/{total_req} required skills, some gaps in preferred skills"
            elif score >= 4:
                return f"Moderate skills match - has {matched_req}/{total_req} required skills, significant skill gaps"
            else:
                return f"Limited skills match - has {matched_req}/{total_req} required skills, major skill development needed"

        elif field_name == "Experience":
            candidate_yoe = details.get('candidate_yoe', 0)
            required_yoe = details.get('required_yoe', 0)

            if score >= 8:
                return f"Excellent experience level - {candidate_yoe} years matches job requirements perfectly"
            elif score >= 6:
                return f"Good experience level - {candidate_yoe} years is appropriate for this role"
            elif score >= 4:
                return f"Moderate experience level - {candidate_yoe} years, some experience gaps"
            else:
                return f"Limited experience - {candidate_yoe} years, below requirements for this role"

        elif field_name == "Education":
            if score == 10:
                return "Perfect education match - meets all degree requirements"
            elif score >= 6:
                return "Good education match - has related degree with some relevance"
            elif score > 0:
                return "Partial education match - has degree but not in required field"
            else:
                return "Education requirements not met - lacks required degree"

        elif field_name == "Certifications":
            relevant_count = details.get('relevant_count', 0)
            total_count = details.get('total_certifications', 0)

            if score >= 8:
                return f"Excellent certifications - {relevant_count} relevant certifications demonstrate expertise"
            elif score >= 4:
                return f"Good certifications - {relevant_count} relevant certifications add value"
            elif score > 0:
                return f"Some relevant certifications - {relevant_count} certifications match job requirements"
            else:
                return f"No relevant certifications - {total_count} certifications but none match job requirements" if total_count > 0 else "No certifications listed"

        elif field_name == "Location":
            if score >= 8:
                return "Excellent location match - candidate is in the job location"
            elif score >= 6:
                return "Good location match - candidate has worked in the job location before"
            elif score >= 4:
                return "Moderate location match - some geographic alignment"
            else:
                return "Location mismatch - candidate may need to relocate"

        elif field_name == "Reliability":
            avg_tenure = details.get('avg_tenure', 0)

            if score >= 8:
                return f"Excellent job stability - average {avg_tenure:.1f} years per company shows commitment"
            elif score >= 6:
                return f"Good job stability - average {avg_tenure:.1f} years per company is reasonable"
            elif score >= 4:
                return f"Moderate job stability - average {avg_tenure:.1f} years per company"
            else:
                return f"Concerning job stability - frequent job changes with {avg_tenure:.1f} years average tenure"

        return technical_rationale  # Fallback

    # Create field score objects with user-friendly rationales
    skills_score = FieldScore(
        raw_score=skills_raw,
        weight=weightage.skills,
        weighted_score=skills_weighted,
        rationale=create_user_friendly_rationale("Skills", skills_raw, skills_rationale, skills_details),
        details=skills_details
    )

    experience_score = FieldScore(
        raw_score=experience_raw,
        weight=weightage.experience,
        weighted_score=experience_weighted,
        rationale=create_user_friendly_rationale("Experience", experience_raw, experience_rationale, experience_details),
        details=experience_details
    )

    education_score = FieldScore(
        raw_score=education_raw,
        weight=weightage.education,
        weighted_score=education_weighted,
        rationale=create_user_friendly_rationale("Education", education_raw, education_rationale, education_details),
        details=education_details
    )

    certifications_score = FieldScore(
        raw_score=certifications_raw,
        weight=weightage.certifications,
        weighted_score=certifications_weighted,
        rationale=create_user_friendly_rationale("Certifications", certifications_raw, certifications_rationale, certifications_details),
        details=certifications_details
    )

    location_score = FieldScore(
        raw_score=location_raw,
        weight=weightage.location,
        weighted_score=location_weighted,
        rationale=create_user_friendly_rationale("Location", location_raw, location_rationale, location_details),
        details=location_details
    )

    reliability_score = FieldScore(
        raw_score=reliability_raw,
        weight=weightage.reliability,
        weighted_score=reliability_weighted,
        rationale=create_user_friendly_rationale("Reliability", reliability_raw, reliability_rationale, reliability_details),
        details=reliability_details
    )

    logger.info(f"CGPA calculation completed: {final_score:.2f}/10 ({fit_category}) in {processing_time:.2f}s")

    # Create clean API versions of field scores (without calculation details)
    def create_clean_field_score(field_score: FieldScore) -> FieldScoreAPI:
        """Convert FieldScore to clean API version without calculation details"""
        return FieldScoreAPI(
            raw_score=field_score.raw_score,
            weight=field_score.weight,
            weighted_score=field_score.weighted_score,
            rationale=field_score.rationale
        )

    # Create detailed rationale
    detailed_rationale = create_detailed_rationale(
        skills_details=skills_details,
        experience_details=experience_details,
        education_details=education_details,
        certifications_details=certifications_details,
        location_details=location_details,
        reliability_details=reliability_details
    )

    # Create the clean API response
    api_response = IntervetNewResponse(
        total_score=final_score,
        fit_category=fit_category,
        summary=summary,
        skills_score=create_clean_field_score(skills_score),
        experience_score=create_clean_field_score(experience_score),
        education_score=create_clean_field_score(education_score),
        certifications_score=create_clean_field_score(certifications_score),
        location_score=create_clean_field_score(location_score),
        reliability_score=create_clean_field_score(reliability_score),
        detailed_rationale=detailed_rationale,
        total_credits_used=total_credits,
        calculation_method="CGPA",
        processing_time=processing_time
    )

    # Store the full field scores with details for logging (attach as private attribute)
    api_response._full_field_scores = {
        'skills_score': skills_score,
        'experience_score': experience_score,
        'education_score': education_score,
        'certifications_score': certifications_score,
        'location_score': location_score,
        'reliability_score': reliability_score
    }

    return api_response

def create_detailed_rationale(
    skills_details: Dict,
    experience_details: Dict,
    education_details: Dict,
    certifications_details: Dict,
    location_details: Dict,
    reliability_details: Dict
) -> DetailedRationale:
    """
    Create detailed rationale structure for comprehensive candidate evaluation.

    Args:
        skills_details: Details from skills scoring
        experience_details: Details from experience scoring
        education_details: Details from education scoring
        certifications_details: Details from certifications scoring
        location_details: Details from location scoring
        reliability_details: Details from reliability scoring

    Returns:
        DetailedRationale object with comprehensive explanations
    """

    # Skills match (combines both required and preferred skills)
    matched_required = skills_details.get('matched_required', [])
    missing_required = skills_details.get('missing_required', [])
    matched_preferred = skills_details.get('matched_preferred', [])
    required_skills_count = skills_details.get('required_skills_count', 0)
    preferred_skills_count = skills_details.get('preferred_skills_count', 0)

    if required_skills_count > 0:
        skills_match_direct = f"Matched {len(matched_required)}/{required_skills_count} required skills"
        if preferred_skills_count > 0:
            skills_match_direct += f" and {len(matched_preferred)}/{preferred_skills_count} preferred skills"

        if matched_required:
            skills_match_direct += f". Matched required skills: {', '.join(matched_required)}"
        if missing_required:
            skills_match_direct += f". Missing required skills: {', '.join(missing_required)}"
    else:
        skills_match_direct = "No required skills specified in job description"

    # Experience match
    candidate_yoe = experience_details.get('candidate_yoe')
    required_yoe = experience_details.get('required_yoe')

    if candidate_yoe is not None and required_yoe is not None:
        experience_ratio = candidate_yoe / required_yoe if required_yoe > 0 else 0
        if 0.8 <= experience_ratio <= 1.5:
            experience_match = f"Excellent match: {candidate_yoe} years vs required {required_yoe} years (ratio: {experience_ratio:.2f})"
        elif experience_ratio < 0.8:
            experience_match = f"Below requirements: {candidate_yoe} years vs required {required_yoe} years (ratio: {experience_ratio:.2f})"
        else:
            experience_match = f"Over-experienced: {candidate_yoe} years vs required {required_yoe} years (ratio: {experience_ratio:.2f})"
    elif required_yoe is not None:
        experience_match = f"Could not determine candidate's years of experience to compare with required {required_yoe} years"
    else:
        experience_match = "No specific experience requirement found in job description"

    # Reliability
    avg_tenure = reliability_details.get('avg_tenure')
    num_companies = reliability_details.get('num_companies', 0)

    if avg_tenure is not None and num_companies > 0:
        if avg_tenure >= 3:
            reliability = f"Excellent stability: average {avg_tenure:.1f} years per company"
        elif avg_tenure >= 2:
            reliability = f"Good stability: average {avg_tenure:.1f} years per company"
        elif avg_tenure >= 1:
            reliability = f"Moderate stability: average {avg_tenure:.1f} years per company"
        else:
            reliability = f"Poor stability: frequent job changes with {avg_tenure:.1f} years per company"
    else:
        reliability = "Could not calculate job stability due to missing experience data"

    # Location match
    jd_location = location_details.get('jd_location')
    resume_location = location_details.get('resume_location')
    location_match_found = location_details.get('location_match_found', False)

    if jd_location and resume_location:
        if location_match_found:
            location_match = f"Current location ({resume_location}) matches job location ({jd_location})"
        else:
            location_match = f"Location mismatch: candidate in {resume_location}, job in {jd_location}"
    elif jd_location:
        location_match = f"No location match found with job location ({jd_location})"
    else:
        location_match = "Location information not available for comparison"

    # Academic match
    education_match = education_details.get('education_match', False)
    matched_degree = education_details.get('matched_degree')
    matched_requirement = education_details.get('matched_requirement')
    education_requirements = education_details.get('education_requirements', [])
    candidate_degrees = education_details.get('candidate_degrees', [])

    if education_match and matched_degree:
        academic_match = f"Education requirements met: '{matched_degree}'"
        if matched_requirement:
            academic_match += f" matches requirement '{matched_requirement}'"
    elif education_requirements and candidate_degrees:
        academic_match = f"Education requirements not met. Candidate: {', '.join(candidate_degrees)}, Required: {', '.join(education_requirements)}"
    else:
        academic_match = "No specific education requirements in job description"

    # Alma mater
    top_universities_found = education_details.get('top_universities_found', [])
    if top_universities_found:
        alma_mater = f"Graduated from prestigious institution(s): {', '.join(top_universities_found)}"
    else:
        alma_mater = "No top-ranked universities found in education history"

    # Certifications
    relevant_certifications = certifications_details.get('relevant_certifications', [])
    irrelevant_certifications = certifications_details.get('irrelevant_certifications', [])

    if relevant_certifications:
        certifications = f"Found {len(relevant_certifications)} relevant certifications: {', '.join(relevant_certifications)}"
        if irrelevant_certifications:
            certifications += f". Also has {len(irrelevant_certifications)} other certifications not directly relevant to this role"
    else:
        if irrelevant_certifications:
            certifications = f"Has {len(irrelevant_certifications)} certifications but none are directly relevant to job requirements"
        else:
            certifications = "No relevant certifications found"

    return DetailedRationale(
        skills_match_direct=skills_match_direct,
        experience_match=experience_match,
        reliability=reliability,
        location_match=location_match,
        academic_match=academic_match,
        alma_mater=alma_mater,
        certifications=certifications
    )

def log_intervet_new_calculation(
    resume_data: Dict,
    jd_data: Dict,
    weightage: WeightageConfig,
    result: IntervetNewResponse,
    request_id: str = None
) -> str:
    """
    Log comprehensive details of intervet_new calculation to a structured folder.

    Args:
        resume_data: Input resume data
        jd_data: Input job description data
        weightage: Weightage configuration used
        result: Calculation result
        request_id: Optional request ID for tracking

    Returns:
        Path to the created log folder
    """
    try:
        # Create timestamp-based folder name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        if request_id:
            folder_name = f"intervet_new_{timestamp}_{request_id}"
        else:
            folder_name = f"intervet_new_{timestamp}"

        # Create main logging directory if it doesn't exist
        main_log_dir = "intervet_new_logs"
        os.makedirs(main_log_dir, exist_ok=True)

        # Create specific log folder for this calculation
        log_folder = os.path.join(main_log_dir, folder_name)
        os.makedirs(log_folder, exist_ok=True)

        # 1. Log input data
        with open(os.path.join(log_folder, "01_input_resume.json"), "w", encoding="utf-8") as f:
            json.dump(resume_data, f, indent=2, ensure_ascii=False)

        with open(os.path.join(log_folder, "02_input_jd.json"), "w", encoding="utf-8") as f:
            json.dump(jd_data, f, indent=2, ensure_ascii=False)

        with open(os.path.join(log_folder, "03_input_weightage.json"), "w", encoding="utf-8") as f:
            json.dump(weightage.model_dump(), f, indent=2, ensure_ascii=False)

        # 2. Log detailed calculation breakdown
        calculation_breakdown = {
            "timestamp": timestamp,
            "calculation_method": result.calculation_method,
            "total_credits_used": result.total_credits_used,
            "processing_time_seconds": result.processing_time,
            "final_score": result.total_score,
            "fit_category": result.fit_category,
            "summary": result.summary,
            "field_scores": {
                "skills": {
                    "raw_score": result.skills_score.raw_score,
                    "weight": result.skills_score.weight,
                    "weighted_score": result.skills_score.weighted_score,
                    "rationale": result.skills_score.rationale,
                    "details": getattr(result, '_full_field_scores', {}).get('skills_score', type('obj', (object,), {'details': {}})).details
                },
                "experience": {
                    "raw_score": result.experience_score.raw_score,
                    "weight": result.experience_score.weight,
                    "weighted_score": result.experience_score.weighted_score,
                    "rationale": result.experience_score.rationale,
                    "details": getattr(result, '_full_field_scores', {}).get('experience_score', type('obj', (object,), {'details': {}})).details
                },
                "education": {
                    "raw_score": result.education_score.raw_score,
                    "weight": result.education_score.weight,
                    "weighted_score": result.education_score.weighted_score,
                    "rationale": result.education_score.rationale,
                    "details": getattr(result, '_full_field_scores', {}).get('education_score', type('obj', (object,), {'details': {}})).details
                },
                "certifications": {
                    "raw_score": result.certifications_score.raw_score,
                    "weight": result.certifications_score.weight,
                    "weighted_score": result.certifications_score.weighted_score,
                    "rationale": result.certifications_score.rationale,
                    "details": getattr(result, '_full_field_scores', {}).get('certifications_score', type('obj', (object,), {'details': {}})).details
                },
                "location": {
                    "raw_score": result.location_score.raw_score,
                    "weight": result.location_score.weight,
                    "weighted_score": result.location_score.weighted_score,
                    "rationale": result.location_score.rationale,
                    "details": getattr(result, '_full_field_scores', {}).get('location_score', type('obj', (object,), {'details': {}})).details
                },
                "reliability": {
                    "raw_score": result.reliability_score.raw_score,
                    "weight": result.reliability_score.weight,
                    "weighted_score": result.reliability_score.weighted_score,
                    "rationale": result.reliability_score.rationale,
                    "details": getattr(result, '_full_field_scores', {}).get('reliability_score', type('obj', (object,), {'details': {}})).details
                }
            }
        }

        with open(os.path.join(log_folder, "04_calculation_breakdown.json"), "w", encoding="utf-8") as f:
            json.dump(calculation_breakdown, f, indent=2, ensure_ascii=False)

        # 3. Log detailed step-by-step calculations for transparency
        step_by_step_calculations = {
            "timestamp": timestamp,
            "calculation_transparency": "This file contains detailed step-by-step calculations for each scoring field",
            "field_calculations": {
                "skills": {
                    "calculation_steps": getattr(result, '_full_field_scores', {}).get('skills_score', type('obj', (object,), {'details': {}})).details.get("_calculation_steps", []),
                    "scoring_formula": getattr(result, '_full_field_scores', {}).get('skills_score', type('obj', (object,), {'details': {}})).details.get("_scoring_formula", ""),
                    "explanation": getattr(result, '_full_field_scores', {}).get('skills_score', type('obj', (object,), {'details': {}})).details.get("_explanation", ""),
                    "matching_details": getattr(result, '_full_field_scores', {}).get('skills_score', type('obj', (object,), {'details': {}})).details.get("_matching_details", [])
                },
                "experience": {
                    "calculation_steps": getattr(result, '_full_field_scores', {}).get('experience_score', type('obj', (object,), {'details': {}})).details.get("_calculation_steps", []),
                    "scoring_formula": getattr(result, '_full_field_scores', {}).get('experience_score', type('obj', (object,), {'details': {}})).details.get("_scoring_formula", ""),
                    "explanation": getattr(result, '_full_field_scores', {}).get('experience_score', type('obj', (object,), {'details': {}})).details.get("_explanation", ""),
                    "experience_breakdown": getattr(result, '_full_field_scores', {}).get('experience_score', type('obj', (object,), {'details': {}})).details.get("_experience_breakdown", [])
                },
                "education": {
                    "calculation_steps": getattr(result, '_full_field_scores', {}).get('education_score', type('obj', (object,), {'details': {}})).details.get("_calculation_steps", []),
                    "scoring_formula": getattr(result, '_full_field_scores', {}).get('education_score', type('obj', (object,), {'details': {}})).details.get("_scoring_formula", ""),
                    "explanation": getattr(result, '_full_field_scores', {}).get('education_score', type('obj', (object,), {'details': {}})).details.get("_explanation", ""),
                    "match_type": getattr(result, '_full_field_scores', {}).get('education_score', type('obj', (object,), {'details': {}})).details.get("match_type", "")
                },
                "certifications": {
                    "calculation_steps": getattr(result, '_full_field_scores', {}).get('certifications_score', type('obj', (object,), {'details': {}})).details.get("_calculation_steps", []),
                    "scoring_formula": getattr(result, '_full_field_scores', {}).get('certifications_score', type('obj', (object,), {'details': {}})).details.get("_scoring_formula", ""),
                    "explanation": getattr(result, '_full_field_scores', {}).get('certifications_score', type('obj', (object,), {'details': {}})).details.get("_explanation", ""),
                    "certification_analysis": getattr(result, '_full_field_scores', {}).get('certifications_score', type('obj', (object,), {'details': {}})).details.get("_certification_analysis", [])
                },
                "location": {
                    "calculation_steps": getattr(result, '_full_field_scores', {}).get('location_score', type('obj', (object,), {'details': {}})).details.get("_calculation_steps", []),
                    "scoring_formula": getattr(result, '_full_field_scores', {}).get('location_score', type('obj', (object,), {'details': {}})).details.get("_scoring_formula", ""),
                    "explanation": getattr(result, '_full_field_scores', {}).get('location_score', type('obj', (object,), {'details': {}})).details.get("_explanation", "")
                },
                "reliability": {
                    "calculation_steps": getattr(result, '_full_field_scores', {}).get('reliability_score', type('obj', (object,), {'details': {}})).details.get("_calculation_steps", []),
                    "scoring_formula": getattr(result, '_full_field_scores', {}).get('reliability_score', type('obj', (object,), {'details': {}})).details.get("_scoring_formula", ""),
                    "explanation": getattr(result, '_full_field_scores', {}).get('reliability_score', type('obj', (object,), {'details': {}})).details.get("_explanation", ""),
                    "tenure_breakdown": getattr(result, '_full_field_scores', {}).get('reliability_score', type('obj', (object,), {'details': {}})).details.get("_tenure_breakdown", [])
                }
            },
            "final_calculation": {
                "formula": f"Final Score = (Sum of Weighted Scores) / (Sum of Weights)",
                "calculation": f"({result.skills_score.weighted_score:.2f} + {result.experience_score.weighted_score:.2f} + {result.education_score.weighted_score:.2f} + {result.certifications_score.weighted_score:.2f} + {result.location_score.weighted_score:.2f} + {result.reliability_score.weighted_score:.2f}) / {result.total_credits_used}",
                "result": f"{result.total_score:.2f}/10"
            }
        }

        with open(os.path.join(log_folder, "04_step_by_step_calculations.json"), "w", encoding="utf-8") as f:
            json.dump(step_by_step_calculations, f, indent=2, ensure_ascii=False)

        # 4. Log final result
        with open(os.path.join(log_folder, "05_final_result.json"), "w", encoding="utf-8") as f:
            json.dump(result.model_dump(), f, indent=2, ensure_ascii=False)

        # 4. Create human-readable summary
        summary_text = f"""
INTERVET_NEW CALCULATION SUMMARY
================================
Timestamp: {timestamp}
Processing Time: {result.processing_time:.3f} seconds

FINAL RESULT
============
Total Score: {result.total_score:.2f}/10
Fit Category: {result.fit_category}
Summary: {result.summary}

WEIGHTAGE CONFIGURATION
=======================
Skills: {weightage.skills}
Experience: {weightage.experience}
Education: {weightage.education}
Certifications: {weightage.certifications}
Location: {weightage.location}
Reliability: {weightage.reliability}
Total Credits: {result.total_credits_used}

DETAILED FIELD SCORES
=====================
Skills:
  Raw Score: {result.skills_score.raw_score:.2f}/10
  Weight: {result.skills_score.weight}
  Weighted Score: {result.skills_score.weighted_score:.2f}
  Rationale: {result.skills_score.rationale}

Experience:
  Raw Score: {result.experience_score.raw_score:.2f}/10
  Weight: {result.experience_score.weight}
  Weighted Score: {result.experience_score.weighted_score:.2f}
  Rationale: {result.experience_score.rationale}

Education:
  Raw Score: {result.education_score.raw_score:.2f}/10
  Weight: {result.education_score.weight}
  Weighted Score: {result.education_score.weighted_score:.2f}
  Rationale: {result.education_score.rationale}

Certifications:
  Raw Score: {result.certifications_score.raw_score:.2f}/10
  Weight: {result.certifications_score.weight}
  Weighted Score: {result.certifications_score.weighted_score:.2f}
  Rationale: {result.certifications_score.rationale}

Location:
  Raw Score: {result.location_score.raw_score:.2f}/10
  Weight: {result.location_score.weight}
  Weighted Score: {result.location_score.weighted_score:.2f}
  Rationale: {result.location_score.rationale}

Reliability:
  Raw Score: {result.reliability_score.raw_score:.2f}/10
  Weight: {result.reliability_score.weight}
  Weighted Score: {result.reliability_score.weighted_score:.2f}
  Rationale: {result.reliability_score.rationale}

CALCULATION FORMULA
===================
Final Score = (Sum of Weighted Scores) / (Sum of Weights)
Final Score = ({result.skills_score.weighted_score:.2f} + {result.experience_score.weighted_score:.2f} + {result.education_score.weighted_score:.2f} + {result.certifications_score.weighted_score:.2f} + {result.location_score.weighted_score:.2f} + {result.reliability_score.weighted_score:.2f}) / {result.total_credits_used}
Final Score = {result.total_score:.2f}/10
"""

        with open(os.path.join(log_folder, "06_human_readable_summary.txt"), "w", encoding="utf-8") as f:
            f.write(summary_text)

        # 5. Create detailed calculation explanation for transparency
        detailed_calculation_text = f"""
DETAILED CALCULATION BREAKDOWN
==============================
This file provides step-by-step explanations of how each score was calculated.

SKILLS SCORING CALCULATION
==========================
Formula: {getattr(result, '_full_field_scores', {}).get('skills_score', type('obj', (object,), {'details': {}})).details.get('_scoring_formula', 'N/A')}
Explanation: {getattr(result, '_full_field_scores', {}).get('skills_score', type('obj', (object,), {'details': {}})).details.get('_explanation', 'N/A')}

Step-by-step calculation:
"""
        for step in getattr(result, '_full_field_scores', {}).get('skills_score', type('obj', (object,), {'details': {}})).details.get('_calculation_steps', []):
            detailed_calculation_text += f"  {step}\n"

        detailed_calculation_text += f"""

EXPERIENCE SCORING CALCULATION
==============================
Formula: {getattr(result, '_full_field_scores', {}).get('experience_score', type('obj', (object,), {'details': {}})).details.get('_scoring_formula', 'N/A')}
Explanation: {getattr(result, '_full_field_scores', {}).get('experience_score', type('obj', (object,), {'details': {}})).details.get('_explanation', 'N/A')}

Step-by-step calculation:
"""
        for step in getattr(result, '_full_field_scores', {}).get('experience_score', type('obj', (object,), {'details': {}})).details.get('_calculation_steps', []):
            detailed_calculation_text += f"  {step}\n"

        detailed_calculation_text += f"""

EDUCATION SCORING CALCULATION
=============================
Formula: {getattr(result, '_full_field_scores', {}).get('education_score', type('obj', (object,), {'details': {}})).details.get('_scoring_formula', 'N/A')}
Explanation: {getattr(result, '_full_field_scores', {}).get('education_score', type('obj', (object,), {'details': {}})).details.get('_explanation', 'N/A')}

Step-by-step calculation:
"""
        for step in getattr(result, '_full_field_scores', {}).get('education_score', type('obj', (object,), {'details': {}})).details.get('_calculation_steps', []):
            detailed_calculation_text += f"  {step}\n"

        detailed_calculation_text += f"""

CERTIFICATIONS SCORING CALCULATION
==================================
Formula: {getattr(result, '_full_field_scores', {}).get('certifications_score', type('obj', (object,), {'details': {}})).details.get('_scoring_formula', 'N/A')}
Explanation: {getattr(result, '_full_field_scores', {}).get('certifications_score', type('obj', (object,), {'details': {}})).details.get('_explanation', 'N/A')}

Step-by-step calculation:
"""
        for step in getattr(result, '_full_field_scores', {}).get('certifications_score', type('obj', (object,), {'details': {}})).details.get('_calculation_steps', []):
            detailed_calculation_text += f"  {step}\n"

        detailed_calculation_text += f"""

LOCATION SCORING CALCULATION
============================
Formula: {getattr(result, '_full_field_scores', {}).get('location_score', type('obj', (object,), {'details': {}})).details.get('_scoring_formula', 'N/A')}
Explanation: {getattr(result, '_full_field_scores', {}).get('location_score', type('obj', (object,), {'details': {}})).details.get('_explanation', 'N/A')}

Step-by-step calculation:
"""
        for step in getattr(result, '_full_field_scores', {}).get('location_score', type('obj', (object,), {'details': {}})).details.get('_calculation_steps', []):
            detailed_calculation_text += f"  {step}\n"

        detailed_calculation_text += f"""

RELIABILITY SCORING CALCULATION
===============================
Formula: {getattr(result, '_full_field_scores', {}).get('reliability_score', type('obj', (object,), {'details': {}})).details.get('_scoring_formula', 'N/A')}
Explanation: {getattr(result, '_full_field_scores', {}).get('reliability_score', type('obj', (object,), {'details': {}})).details.get('_explanation', 'N/A')}

Step-by-step calculation:
"""
        for step in getattr(result, '_full_field_scores', {}).get('reliability_score', type('obj', (object,), {'details': {}})).details.get('_calculation_steps', []):
            detailed_calculation_text += f"  {step}\n"

        detailed_calculation_text += f"""

FINAL CGPA CALCULATION
=====================
Formula: Final Score = (Sum of Weighted Scores) / (Sum of Weights)

Calculation:
  Skills: {result.skills_score.raw_score:.2f} × {result.skills_score.weight} = {result.skills_score.weighted_score:.2f}
  Experience: {result.experience_score.raw_score:.2f} × {result.experience_score.weight} = {result.experience_score.weighted_score:.2f}
  Education: {result.education_score.raw_score:.2f} × {result.education_score.weight} = {result.education_score.weighted_score:.2f}
  Certifications: {result.certifications_score.raw_score:.2f} × {result.certifications_score.weight} = {result.certifications_score.weighted_score:.2f}
  Location: {result.location_score.raw_score:.2f} × {result.location_score.weight} = {result.location_score.weighted_score:.2f}
  Reliability: {result.reliability_score.raw_score:.2f} × {result.reliability_score.weight} = {result.reliability_score.weighted_score:.2f}

  Total Weighted Score: {result.skills_score.weighted_score:.2f} + {result.experience_score.weighted_score:.2f} + {result.education_score.weighted_score:.2f} + {result.certifications_score.weighted_score:.2f} + {result.location_score.weighted_score:.2f} + {result.reliability_score.weighted_score:.2f} = {result.skills_score.weighted_score + result.experience_score.weighted_score + result.education_score.weighted_score + result.certifications_score.weighted_score + result.location_score.weighted_score + result.reliability_score.weighted_score:.2f}
  Total Credits: {result.total_credits_used}

  Final Score: {result.skills_score.weighted_score + result.experience_score.weighted_score + result.education_score.weighted_score + result.certifications_score.weighted_score + result.location_score.weighted_score + result.reliability_score.weighted_score:.2f} / {result.total_credits_used} = {result.total_score:.2f}/10

CONCLUSION
==========
The candidate scored {result.total_score:.2f}/10, which categorizes them as a "{result.fit_category}".
"""

        with open(os.path.join(log_folder, "07_detailed_calculation_explanation.txt"), "w", encoding="utf-8") as f:
            f.write(detailed_calculation_text)

        logger.info(f"Enhanced intervet_new calculation logged to: {log_folder}")
        logger.info(f"Log files created: input data, calculation breakdown, step-by-step calculations, final result, human-readable summary, detailed explanation")
        return log_folder

    except Exception as e:
        logger.error(f"Failed to log intervet_new calculation: {e}")
        return ""

def generate_jd_only_questions(jd_text: str) -> Dict:
    """Generate interview questions based only on job description."""
    logger.info("Starting JD-only interview question generation")

    # Define the categories
    categories = [
        "technical_questions",
        "past_experience_questions",
        "case_study_questions",
        "situation_handling_questions",
        "personality_test_questions"
    ]

    # Initialize results dictionary
    questions_data = {}

    # Generate 5 questions for each category in parallel
    from concurrent.futures import ThreadPoolExecutor

    def process_category_jd_only(category):
        # Map category to a more readable name
        category_name = {
            "technical_questions": "Technical",
            "past_experience_questions": "Past Experience",
            "case_study_questions": "Case Study",
            "situation_handling_questions": "Situation Handling",
            "personality_test_questions": "Personality Test"
        }.get(category, category)

        # Special handling for personality test questions
        if category == "personality_test_questions":
            # For personality questions, use a more general prompt focused on personality traits
            prompt = f"""
            You are an expert interview question generator. Your task is to create 5 personality assessment questions.

            Generate exactly 5 personality assessment questions that:
            - Focus on general personality traits like teamwork, leadership, adaptability, work ethic, etc.
            - Help understand the candidate's character, values, and working style
            - Are NOT specific to the job description but rather about the person's general traits
            - Reveal how the candidate might fit into different team environments
            - Assess soft skills and interpersonal abilities

            Examples of good personality questions:
            - "How do you handle criticism or feedback from colleagues or supervisors?"
            - "Describe a situation where you had to adapt to a significant change at work. How did you handle it?"
            - "What motivates you the most in your professional life?"
            - "How do you prioritize tasks when facing multiple deadlines?"
            - "Describe your ideal work environment and management style."

            IMPORTANT: Respond ONLY with a JSON array of strings, where each string is a question.
            Example: ["Question 1?", "Question 2?", "Question 3?", "Question 4?", "Question 5?"]

            DO NOT include any explanations, markdown formatting, or code blocks in your response.
            Your entire response should be ONLY the JSON array, nothing else.
            """
        else:
            # For all other categories, use the original prompt
            prompt = f"""
            You are an expert interview question generator. Your task is to create 5 tailored {category_name} questions based on a job description.

            Job Description:
            {jd_text}

            Generate exactly 5 {category_name} questions that are:
            - Relevant to the job description
            - Specific and detailed (not generic)
            - Designed to assess a candidate's fit for the role

            IMPORTANT: Respond ONLY with a JSON array of strings, where each string is a question.
            Example: ["Question 1?", "Question 2?", "Question 3?", "Question 4?", "Question 5?"]

            DO NOT include any explanations, markdown formatting, or code blocks in your response.
            Your entire response should be ONLY the JSON array, nothing else.
            """

        try:
            # Use a shorter timeout for individual category generation
            response = get_response(
                prompt,
                timeout_seconds=45,
                max_tokens=500,
                endpoint="/intervet",
                context=f"{category}_questions",
                call_type="question_generation"
            )

            # Try to extract JSON array from the response
            json_str = response.strip()

            # Remove any markdown formatting if present
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()

            # Try to find the JSON array if there's additional text
            if json_str.find('[') >= 0 and json_str.rfind(']') >= 0:
                start = json_str.find('[')
                end = json_str.rfind(']') + 1
                json_str = json_str[start:end]

            # Parse the JSON string into a Python list
            try:
                questions = json.loads(json_str)

                # Ensure we have a list of strings
                if isinstance(questions, list):
                    # Convert all items to strings and filter out empty ones
                    questions = [str(q) for q in questions if q]
                    return category, questions[:5]  # Limit to 5 questions
                else:
                    logger.warning(f"Expected a list but got {type(questions)} for {category}")
                    return category, []

            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract questions using regex
                # Look for text that looks like questions (ends with ? or has question-like structure)
                question_pattern = re.compile(r'"([^"]+\?)"')
                matches = question_pattern.findall(json_str)

                if matches:
                    return category, matches[:5]  # Limit to 5 questions
                else:
                    logger.warning(f"Could not extract questions for {category}")
                    return category, []

        except Exception as e:
            logger.error(f"Error generating questions for {category}: {e}")
            return category, []

    # Use ThreadPoolExecutor to process categories in parallel
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = {executor.submit(process_category_jd_only, category): category for category in categories}

        for future in futures:
            try:
                category, questions = future.result()
                questions_data[category] = questions
                logger.info(f"Generated {len(questions)} questions for {category}")
            except Exception as e:
                logger.error(f"Error processing category: {e}")
                # Initialize with empty list on error
                questions_data[futures[future]] = []

    # Ensure all categories are present
    for category in categories:
        if category not in questions_data:
            questions_data[category] = []

    logger.info("Successfully generated all JD-only interview questions")
    return questions_data

@app.post("/jd_only", response_model=Dict, summary="Generate interview questions from job description only", description="Upload a job description and get tailored interview questions without needing resume data")
async def generate_jd_only_questions_endpoint(file: UploadFile = File(..., description="Job description file to parse (PDF or DOCX format)")):
    """Generate tailored interview questions based only on a job description.

    - **file**: Upload a PDF or DOCX file containing a job description

    Returns a JSON object with 5 questions for each of these categories:
    - Technical questions
    - Past experience questions
    - Case study questions
    - Situation handling questions
    - Personality test questions

    Note: This endpoint processes each question category in parallel for faster response times.
    """
    try:
        # Check if the file is a supported format (PDF or DOCX)
        file_extension = os.path.splitext(file.filename.lower())[1]

        if file_extension == '.pdf':
            file_type = "pdf"
            suffix = '.pdf'
        elif file_extension == '.docx':
            file_type = "docx"
            suffix = '.docx'
        else:
            logger.warning(f"Unsupported file format: {file.filename}")
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported.")

        logger.info(f"Processing job description file: {file.filename} (type: {file_type})")

        # Create a temporary file to store the uploaded file
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            # Write the uploaded file content to the temporary file
            content = await file.read()
            if not content:
                logger.error("Uploaded file is empty")
                raise HTTPException(status_code=400, detail="The uploaded file is empty.")

            temp_file.write(content)
            temp_file_path = temp_file.name
            logger.info(f"Saved uploaded file to temporary location: {temp_file_path}")

        try:
            # Extract text from the file based on its type
            jd_text = extract_text_from_file(
                temp_file_path,
                file_type,
                source_filename=file.filename,
                context="jd"
            )

            # Generate interview questions based only on the JD
            questions_data = generate_jd_only_questions(jd_text)

            return questions_data
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                logger.info(f"Removing temporary file: {temp_file_path}")
                os.remove(temp_file_path)

    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Unexpected error in JD-only question generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@app.post("/intervet", response_model=Dict, summary="Evaluate candidate-job fit", description="Evaluate how well a candidate's resume matches a job description")
async def evaluate_candidate_job_fit_endpoint(request: IntervetRequest):
    """Evaluate how well a candidate's resume matches a job description.

    - **resume_json**: Resume data in JSON format (typically obtained from the /resume endpoint)
    - **jd_json**: Job description data in JSON format (typically obtained from the /jd_parser endpoint)

    Returns a comprehensive evaluation with:
    - Overall match score (0-100)
    - Fit category (Excellent/Strong/Good/Moderate/Weak Match)
    - Summary of the match
    - Detailed scores for each evaluation criterion
    - Detailed rationale for each score

    The evaluation is based on multiple criteria:
    1. Skills matching (direct and from projects/experience)
    2. Years of experience
    3. Reliability (experience to job ratio)
    4. Location match
    5. Academic qualifications
    6. Alma mater prestige
    7. Relevant certifications
    """
    try:
        logger.info("Starting candidate-job fit evaluation")

        # Ensure we have valid JSON data
        if not request.resume_json:
            raise HTTPException(status_code=400, detail="Missing or invalid resume_json data")

        if not request.jd_json:
            raise HTTPException(status_code=400, detail="Missing or invalid jd_json data")

        # Calculate the match score
        result = calculate_candidate_job_fit(request.resume_json, request.jd_json)

        return result
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error in candidate-job fit evaluation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An error occurred during evaluation: {str(e)}")

@app.post("/intervet2", response_model=Intervet2Response, summary="Enhanced candidate-job fit evaluation from files", description="Upload resume and job description files for comprehensive evaluation using hybrid_resume, jd_parser, and intervet_new endpoints")
async def evaluate_candidate_job_fit_from_files_endpoint(
    request: Request,
    resume_file: UploadFile = File(..., description="Resume file to parse (PDF or DOCX format)"),
    jd_file: UploadFile = File(..., description="Job description file to parse (PDF or DOCX format)"),
    weightage: Optional[WeightageConfig] = None
):
    """Enhanced candidate-job fit evaluation using hybrid_resume, jd_parser, and intervet_new endpoints.

    - **resume_file**: Upload a PDF or DOCX file containing a resume
    - **jd_file**: Upload a PDF or DOCX file containing a job description
    - **weightage**: Optional weightage configuration for CGPA-style scoring

    This endpoint performs a comprehensive 3-step evaluation:
    1. **Resume Parsing**: Uses hybrid_resume endpoint for optimal parsing (regex + LLM)
    2. **JD Parsing**: Uses jd_parser endpoint for structured job description extraction
    3. **Evaluation**: Uses intervet_new endpoint for CGPA-style scoring with detailed rationale

    Returns:
    - Parsed resume data (from hybrid_resume)
    - Parsed job description data (from jd_parser)
    - Comprehensive CGPA-style evaluation (from intervet_new)
    - Processing times for each step
    - Total processing time

    The CGPA-style evaluation includes:
    - Final score out of 10 with configurable field weights
    - Detailed rationale for each evaluation criterion
    - Comprehensive logging for transparency
    - Field-wise scoring (skills, experience, education, certifications, location, reliability)

    Note: This endpoint stores intermediate outputs for GUI testing and provides complete transparency
    in the evaluation process.
    """
    try:
        logger.info("🚀 Starting enhanced candidate-job fit evaluation from files")
        start_time = time.time()

        # Get metrics tracker from request state
        metrics = getattr(request.state, "metrics", None)
        if metrics:
            metrics.add_metric("endpoint_type", "intervet2_enhanced")

        # Initialize processing times tracking
        processing_times = {}

        # Validate file types
        resume_file_extension = os.path.splitext(resume_file.filename.lower())[1]
        jd_file_extension = os.path.splitext(jd_file.filename.lower())[1]

        if resume_file_extension not in ['.pdf', '.docx']:
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported for resume.")
        if jd_file_extension not in ['.pdf', '.docx']:
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported for job description.")

        logger.info(f"📄 Processing files: {resume_file.filename} ({resume_file_extension}) and {jd_file.filename} ({jd_file_extension})")

        # Step 1: Parse resume using hybrid_resume approach
        logger.info("📋 Step 1: Parsing resume using hybrid_resume approach...")
        resume_start_time = time.time()

        # Save resume file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=resume_file_extension) as temp_file:
            resume_content = await resume_file.read()
            if not resume_content:
                raise HTTPException(status_code=400, detail="The uploaded resume file is empty.")
            temp_file.write(resume_content)
            resume_temp_file = temp_file.name

        try:
            # Extract text from resume file
            resume_file_type = "pdf" if resume_file_extension == ".pdf" else "docx"
            extracted_resume_text = extract_text_from_file(
                resume_temp_file,
                resume_file_type,
                request_metrics=metrics,
                source_filename=resume_file.filename,
                context="hybrid_resume_parsing"
            )

            if not extracted_resume_text or len(extracted_resume_text.strip()) < 50:
                raise HTTPException(
                    status_code=400,
                    detail="Could not extract sufficient text from the resume file."
                )

            # Extract sections using regex method
            sections, confidence_scores = extract_sections_regex(extracted_resume_text, resume_file.filename, "")

            # Parse sections with LLM for JSON structuring
            structured_resume_data = parse_sections_with_gemma(sections, resume_file.filename)

            # Normalize the resume data
            resume_data = normalize_resume_data(structured_resume_data, convert_skills_to_dict_format=True)

        finally:
            # Clean up resume temp file
            if os.path.exists(resume_temp_file):
                os.unlink(resume_temp_file)

        processing_times["resume_parsing"] = time.time() - resume_start_time
        logger.info(f"✅ Resume parsing completed in {processing_times['resume_parsing']:.2f}s")

        # Step 2: Parse job description using jd_parser approach
        logger.info("📋 Step 2: Parsing job description using jd_parser approach...")
        jd_start_time = time.time()

        # Save JD file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=jd_file_extension) as temp_file:
            jd_content = await jd_file.read()
            if not jd_content:
                raise HTTPException(status_code=400, detail="The uploaded job description file is empty.")
            temp_file.write(jd_content)
            jd_temp_file = temp_file.name

        try:
            # Extract text from JD file
            jd_file_type = "pdf" if jd_file_extension == ".pdf" else "docx"
            extracted_jd_text = extract_text_from_file(
                jd_temp_file,
                jd_file_type,
                request_metrics=metrics,
                source_filename=jd_file.filename,
                context="jd_parsing"
            )

            if not extracted_jd_text or len(extracted_jd_text.strip()) < 50:
                raise HTTPException(
                    status_code=400,
                    detail="Could not extract sufficient text from the job description file."
                )

            # Parse JD using the jd_parser function
            jd_data = parse_jd(extracted_jd_text, source_filename=jd_file.filename)

        finally:
            # Clean up JD temp file
            if os.path.exists(jd_temp_file):
                os.unlink(jd_temp_file)

        processing_times["jd_parsing"] = time.time() - jd_start_time
        logger.info(f"✅ JD parsing completed in {processing_times['jd_parsing']:.2f}s")

        # Step 3: Evaluate using intervet_new approach
        logger.info("🎯 Step 3: Evaluating candidate-job fit using intervet_new approach...")
        evaluation_start_time = time.time()

        # Call the intervet_new calculation function directly
        evaluation_result_dict = calculate_candidate_job_fit_new(resume_data, jd_data, weightage)

        # Convert the dictionary result to IntervetNewResponse model
        evaluation_result = IntervetNewResponse(**evaluation_result_dict)

        processing_times["evaluation"] = time.time() - evaluation_start_time
        logger.info(f"✅ Evaluation completed in {processing_times['evaluation']:.2f}s")

        # Calculate total processing time
        total_processing_time = time.time() - start_time

        # Log summary
        logger.info(f"📊 Enhanced evaluation summary:")
        logger.info(f"   Resume skills: {len(resume_data.get('skills', []))}")
        logger.info(f"   JD required skills: {len(jd_data.get('required_skills', []))}")
        logger.info(f"   Final score: {evaluation_result.total_score:.2f}/10")
        logger.info(f"   Fit category: {evaluation_result.fit_category}")
        logger.info(f"   Total processing time: {total_processing_time:.2f}s")

        # Create the comprehensive response
        response = Intervet2Response(
            resume_data=resume_data,
            jd_data=jd_data,
            evaluation_result=evaluation_result,
            processing_times=processing_times,
            total_processing_time=total_processing_time
        )

        # Add metrics if available
        if metrics:
            metrics.add_metric("total_processing_time", total_processing_time)
            metrics.add_metric("final_score", evaluation_result.total_score)
            metrics.add_metric("fit_category", evaluation_result.fit_category)

        logger.info("✅ Enhanced candidate-job fit evaluation completed successfully")
        return response

    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error in enhanced candidate-job fit evaluation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An error occurred during enhanced evaluation: {str(e)}")

@app.post("/intervet_new", response_model=IntervetNewResponse, summary="CGPA-style candidate evaluation", description="Evaluate candidate-job fit using configurable CGPA-style scoring system")
async def evaluate_candidate_job_fit_new_endpoint(request: IntervetNewRequest, http_request: Request):
    """
    Evaluate how well a candidate's resume matches a job description using a CGPA-style scoring system.

    This endpoint implements a credit-based scoring system similar to CGPA where:
    - Each field (skills, experience, education, etc.) gets a score from 0-10
    - Each field has a configurable weight/credit value
    - Final score = sum(weight * field_score) / sum(weights)
    - Result is comprehensive with detailed rationale and logging

    **Key Features:**
    - **Configurable Weights**: Customize the importance of each field
    - **CGPA-style Calculation**: Credit-based scoring system
    - **Comprehensive Logging**: Detailed logs for each calculation
    - **Transparent Rationale**: Clear explanation for each score
    - **0-10 Scale**: Easy to understand scoring

    **Input:**
    - **resume_json**: Resume data in JSON format (from /resume endpoint)
    - **jd_json**: Job description data in JSON format (from /jd_parser endpoint)
    - **weightage**: Optional weightage configuration (uses defaults if not provided)

    **Output:**
    - **total_score**: Final CGPA-style score (0-10)
    - **fit_category**: Overall fit category (Excellent/Strong/Good/Moderate/Weak Match)
    - **Individual field scores**: Detailed breakdown for each field
    - **Comprehensive rationale**: Explanation for each score
    - **Calculation metadata**: Processing time, credits used, etc.

    **Default Weights:**
    - Skills: 3.0 credits
    - Experience: 2.5 credits
    - Education: 2.0 credits
    - Certifications: 1.0 credit
    - Location: 1.0 credit
    - Reliability: 0.5 credits
    """
    try:
        start_time = time.time()
        logger.info("Starting CGPA-style candidate-job fit evaluation")

        # Get metrics tracker from request state
        metrics = getattr(http_request.state, "metrics", None)
        if metrics:
            metrics.add_metric("endpoint_type", "intervet_new")

        # Validate input data
        if not request.resume_json:
            raise HTTPException(status_code=400, detail="Missing or invalid resume_json data")

        if not request.jd_json:
            raise HTTPException(status_code=400, detail="Missing or invalid jd_json data")

        # Use default weightage if not provided
        weightage = request.weightage if request.weightage else WeightageConfig()

        logger.info(f"Using weightage configuration: Skills={weightage.skills}, Experience={weightage.experience}, Education={weightage.education}, Certifications={weightage.certifications}, Location={weightage.location}, Reliability={weightage.reliability}")

        # Perform CGPA-style calculation
        result = calculate_candidate_job_fit_new(
            resume_data=request.resume_json,
            jd_data=request.jd_json,
            weightage=weightage
        )

        # Generate unique request ID for logging
        request_id = f"{int(time.time() * 1000)}"

        # Log comprehensive calculation details
        log_folder = log_intervet_new_calculation(
            resume_data=request.resume_json,
            jd_data=request.jd_json,
            weightage=weightage,
            result=result,
            request_id=request_id
        )

        processing_time = time.time() - start_time
        logger.info(f"CGPA-style evaluation completed: {result.total_score:.2f}/10 ({result.fit_category}) in {processing_time:.2f}s")

        if log_folder:
            logger.info(f"Detailed calculation logs saved to: {log_folder}")

        # Log metrics if available
        if metrics:
            metrics.add_metric("final_score", result.total_score)
            metrics.add_metric("fit_category", result.fit_category)
            metrics.add_metric("total_credits_used", result.total_credits_used)
            metrics.add_metric("log_folder", log_folder)

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in CGPA-style candidate-job fit evaluation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An error occurred during CGPA-style evaluation: {str(e)}")

@app.post("/interfix")
async def interfix_endpoint(request_data: InterfixRequest, request: Request):
    """Extract interview info from a summary (VAPI transcript)."""
    try:
        # Get metrics from request state
        metrics = getattr(request.state, 'metrics', None)

        summary = request_data.summary
        logger.info(f"Processing call summary of length: {len(summary)}")

        # Parse the call summary using Gemma
        parsed_data = parse_call_summary_with_gemma(summary, request_metrics=metrics)

        # Create response using the parsed data
        response = InterfixResponse(**parsed_data)

        logger.info("Successfully processed call summary")
        return response

    except Exception as e:
        logger.error(f"Error in interfix endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Interfix failed: {str(e)}")

@app.get("/debug/json-files")
async def list_debug_files():
    """List all debug JSON files created during parsing failures."""
    try:
        debug_files = []
        debug_folder = "debug_json_files"

        # Check if debug folder exists
        if os.path.exists(debug_folder):
            for filename in os.listdir(debug_folder):
                if filename.endswith('.json'):
                    filepath = os.path.join(debug_folder, filename)
                    file_stat = os.stat(filepath)

                    # Try to determine context from filename
                    context = "unknown"
                    if "(" in filename and ")" in filename:
                        # New format: filename(ext).json or filename(ext)1.json
                        base_name = filename.replace('.json', '')
                        if base_name[-1].isdigit():
                            # Remove trailing number
                            base_name = base_name.rstrip('0123456789')
                        context = f"resume ({base_name})"
                    elif 'malformed_' in filename:
                        # Old format: debug_malformed_context_timestamp.json
                        parts = filename.split('_')
                        if len(parts) > 2:
                            context = parts[2]

                    debug_files.append({
                        "filename": filename,
                        "size": file_stat.st_size,
                        "created": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                        "context": context,
                        "path": filepath
                    })

        debug_files.sort(key=lambda x: x['created'], reverse=True)
        return {
            "debug_files": debug_files,
            "total_files": len(debug_files),
            "debug_folder": debug_folder,
            "message": "These files contain malformed JSON that failed to parse. Use /debug/json-files/{filename} to view content."
        }
    except Exception as e:
        logger.error(f"Error listing debug files: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list debug files: {str(e)}")

@app.get("/debug/json-files/{filename}")
async def get_debug_file(filename: str):
    """Get the content of a specific debug JSON file."""
    try:
        # Security check - only allow JSON files
        if not filename.endswith('.json'):
            raise HTTPException(status_code=400, detail="Invalid debug file name - must be a JSON file")

        debug_folder = "debug_json_files"
        filepath = os.path.join(debug_folder, filename)

        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="Debug file not found")

        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        # Try to identify the specific JSON error
        try:
            json.loads(content)
            error_info = "File appears to be valid JSON now"
        except json.JSONDecodeError as e:
            error_info = {
                "error": str(e),
                "line": e.lineno,
                "column": e.colno,
                "position": e.pos
            }

        return {
            "filename": filename,
            "filepath": filepath,
            "content": content,
            "length": len(content),
            "error_analysis": error_info,
            "suggestions": [
                "Check for missing commas between array elements or object properties",
                "Look for unescaped quotes in string values",
                "Verify all brackets and braces are properly balanced",
                "Check for trailing commas before closing brackets/braces",
                "Use the /debug/repair-json endpoint to test LLM repair on this content"
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reading debug file {filename}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to read debug file: {str(e)}")

@app.delete("/debug/json-files")
async def cleanup_debug_files():
    """Clean up all debug JSON files."""
    try:
        deleted_files = []
        debug_folder = "debug_json_files"

        if os.path.exists(debug_folder):
            for filename in os.listdir(debug_folder):
                if filename.endswith('.json'):
                    filepath = os.path.join(debug_folder, filename)
                    try:
                        os.remove(filepath)
                        deleted_files.append(filename)
                    except Exception as e:
                        logger.warning(f"Could not delete {filepath}: {e}")

        return {
            "deleted_files": deleted_files,
            "total_deleted": len(deleted_files),
            "debug_folder": debug_folder,
            "message": f"Cleaned up {len(deleted_files)} debug files from {debug_folder}"
        }
    except Exception as e:
        logger.error(f"Error cleaning up debug files: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cleanup debug files: {str(e)}")

class JSONRepairRequest(BaseModel):
    malformed_json: str = Field(..., description="The malformed JSON string to repair")
    context: str = Field(default="resume", description="Context: 'resume' or 'job_description'")

@app.post("/debug/repair-json")
async def repair_json_endpoint(request_data: JSONRepairRequest):
    """Test the LLM JSON repair functionality directly."""
    try:
        logger.info(f"Testing JSON repair for {request_data.context} context")

        # Test the malformed JSON first
        try:
            json.loads(request_data.malformed_json)
            return {
                "status": "already_valid",
                "message": "The provided JSON is already valid",
                "original_json": request_data.malformed_json
            }
        except json.JSONDecodeError as original_error:
            logger.info(f"JSON is malformed: {original_error}")

        # Attempt LLM repair
        if not ENABLE_LLM_JSON_REPAIR:
            raise HTTPException(status_code=400, detail="LLM JSON repair is disabled")

        fixed_json = llm_fix_malformed_json(request_data.malformed_json, request_data.context)

        if fixed_json:
            # Validate the repaired JSON
            try:
                parsed_data = json.loads(fixed_json)
                return {
                    "status": "repaired",
                    "message": "Successfully repaired JSON using LLM",
                    "original_json": request_data.malformed_json,
                    "repaired_json": fixed_json,
                    "parsed_data": parsed_data,
                    "repair_stats": {
                        "original_length": len(request_data.malformed_json),
                        "repaired_length": len(fixed_json),
                        "context": request_data.context
                    }
                }
            except json.JSONDecodeError as repair_error:
                return {
                    "status": "repair_failed",
                    "message": "LLM repair produced invalid JSON",
                    "original_json": request_data.malformed_json,
                    "attempted_repair": fixed_json,
                    "repair_error": str(repair_error)
                }
        else:
            return {
                "status": "repair_failed",
                "message": "LLM repair returned no result",
                "original_json": request_data.malformed_json
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in JSON repair endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"JSON repair failed: {str(e)}")

@app.get("/extracted_text/list", response_model=Dict, summary="List extracted text files", description="List all extracted text files from resume and JD processing")
async def list_extracted_text_files():
    """List all extracted text files from resume and JD processing.

    Returns a dictionary with lists of files in both resume_extracted_text and jd_extracted_text folders.
    Each file entry includes the filename, size, and creation time.
    """
    try:
        result = {
            "resume_extracted_text": [],
            "jd_extracted_text": [],
            "total_files": 0
        }

        # Check resume extracted text folder
        resume_folder = "resume_extracted_text"
        if os.path.exists(resume_folder):
            for filename in os.listdir(resume_folder):
                if filename.endswith('.txt'):
                    file_path = os.path.join(resume_folder, filename)
                    file_stats = os.stat(file_path)
                    result["resume_extracted_text"].append({
                        "filename": filename,
                        "size_bytes": file_stats.st_size,
                        "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                        "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                    })

        # Check JD extracted text folder
        jd_folder = "jd_extracted_text"
        if os.path.exists(jd_folder):
            for filename in os.listdir(jd_folder):
                if filename.endswith('.txt'):
                    file_path = os.path.join(jd_folder, filename)
                    file_stats = os.stat(file_path)
                    result["jd_extracted_text"].append({
                        "filename": filename,
                        "size_bytes": file_stats.st_size,
                        "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                        "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                    })

        # Calculate total files
        result["total_files"] = len(result["resume_extracted_text"]) + len(result["jd_extracted_text"])

        # Sort files by modification time (newest first)
        result["resume_extracted_text"].sort(key=lambda x: x["modified_time"], reverse=True)
        result["jd_extracted_text"].sort(key=lambda x: x["modified_time"], reverse=True)

        return result

    except Exception as e:
        logger.error(f"Error listing extracted text files: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list extracted text files: {str(e)}")

@app.get("/extracted_text/view/{context}/{filename}", response_model=Dict, summary="View extracted text file", description="View the contents of a specific extracted text file")
async def view_extracted_text_file(
    context: str = Path(..., description="Context: 'resume' or 'jd'"),
    filename: str = Path(..., description="Name of the text file to view")
):
    """View the contents of a specific extracted text file.

    - **context**: Either 'resume' or 'jd' to specify which folder to look in
    - **filename**: Name of the text file to view (should end with .txt)

    Returns the file contents along with metadata.
    """
    try:
        # Validate context
        if context not in ["resume", "jd"]:
            raise HTTPException(status_code=400, detail="Context must be 'resume' or 'jd'")

        # Determine folder based on context
        if context == "resume":
            folder_name = "resume_extracted_text"
        else:
            folder_name = "jd_extracted_text"

        # Validate filename
        if not filename.endswith('.txt'):
            raise HTTPException(status_code=400, detail="Filename must end with .txt")

        # Construct file path
        file_path = os.path.join(folder_name, filename)

        # Check if file exists
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail=f"File not found: {filename}")

        # Read file contents
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Get file stats
        file_stats = os.stat(file_path)

        return {
            "filename": filename,
            "context": context,
            "content": content,
            "metadata": {
                "size_bytes": file_stats.st_size,
                "created_time": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                "character_count": len(content),
                "line_count": content.count('\n') + 1 if content else 0
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error viewing extracted text file: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to view extracted text file: {str(e)}")

@app.delete("/extracted_text/cleanup", response_model=Dict, summary="Cleanup extracted text files", description="Delete old extracted text files to free up space")
async def cleanup_extracted_text_files(
    days_old: int = Query(7, description="Delete files older than this many days", ge=1),
    context: str = Query(None, description="Context to clean: 'resume', 'jd', or None for both")
):
    """Delete old extracted text files to free up space.

    - **days_old**: Delete files older than this many days (default: 7)
    - **context**: Specify 'resume' or 'jd' to clean only that context, or leave empty to clean both

    Returns statistics about the cleanup operation.
    """
    try:
        cutoff_time = time.time() - (days_old * 24 * 60 * 60)  # Convert days to seconds

        deleted_files = {
            "resume_extracted_text": [],
            "jd_extracted_text": [],
            "total_deleted": 0,
            "total_size_freed": 0
        }

        # Determine which folders to clean
        folders_to_clean = []
        if context is None:
            folders_to_clean = [("resume_extracted_text", "resume"), ("jd_extracted_text", "jd")]
        elif context == "resume":
            folders_to_clean = [("resume_extracted_text", "resume")]
        elif context == "jd":
            folders_to_clean = [("jd_extracted_text", "jd")]
        else:
            raise HTTPException(status_code=400, detail="Context must be 'resume', 'jd', or None")

        # Clean each folder
        for folder_name, _ in folders_to_clean:
            if os.path.exists(folder_name):
                for filename in os.listdir(folder_name):
                    if filename.endswith('.txt'):
                        file_path = os.path.join(folder_name, filename)
                        file_stats = os.stat(file_path)

                        # Check if file is older than cutoff
                        if file_stats.st_mtime < cutoff_time:
                            file_size = file_stats.st_size
                            os.remove(file_path)

                            deleted_files[folder_name].append({
                                "filename": filename,
                                "size_bytes": file_size,
                                "age_days": (time.time() - file_stats.st_mtime) / (24 * 60 * 60)
                            })

                            deleted_files["total_deleted"] += 1
                            deleted_files["total_size_freed"] += file_size

        logger.info(f"Cleanup completed: deleted {deleted_files['total_deleted']} files, freed {deleted_files['total_size_freed']} bytes")

        return {
            "status": "success",
            "message": f"Deleted {deleted_files['total_deleted']} files older than {days_old} days",
            "deleted_files": deleted_files,
            "size_freed_mb": round(deleted_files["total_size_freed"] / (1024 * 1024), 2)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        raise HTTPException(status_code=500, detail=f"Cleanup failed: {str(e)}")

@app.get("/prompt_logs/stats")
async def get_prompt_log_stats():
    """Get statistics about prompt logs."""
    try:
        stats = get_log_stats()
        return {
            "status": "success",
            "stats": stats
        }
    except Exception as e:
        logger.error(f"Error getting prompt log stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get prompt log stats: {str(e)}")

@app.post("/prompt_logs/cleanup")
async def cleanup_prompt_logs(days_to_keep: int = Query(7, ge=1, le=30, description="Number of days of logs to keep")):
    """Clean up old prompt log files."""
    try:
        deleted_count = cleanup_old_logs(days_to_keep)
        return {
            "status": "success",
            "message": f"Cleaned up {deleted_count} old prompt log files",
            "deleted_count": deleted_count,
            "days_kept": days_to_keep
        }
    except Exception as e:
        logger.error(f"Error cleaning up prompt logs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cleanup prompt logs: {str(e)}")

@app.post("/section", response_model=SectionExtractionResponse)
async def extract_sections_multiple_calls(
    request: Request,
    file: UploadFile = File(..., description="Resume file (PDF or DOCX)")
):
    """
    Extract resume sections using multiple LLM calls (one call per section).

    This endpoint tests the Gemma model's capability to isolate each section separately
    by making individual calls for each section (summary, education, experience, etc.).
    """
    start_time = time.time()
    request_metrics = getattr(request.state, 'metrics', None)

    try:
        logger.info("🚀 Starting section extraction with multiple calls...")

        # Validate file type
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")

        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in ['.pdf', '.docx']:
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported")

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Extract text from file
            logger.info("📄 Extracting text from uploaded file...")
            file_type = "pdf" if file_extension == ".pdf" else "docx"

            extracted_text = extract_text_from_file(
                temp_file_path,
                file_type,
                request_metrics=request_metrics,
                source_filename=file.filename,
                context="section_extraction"
            )

            if not extracted_text or len(extracted_text.strip()) < 50:
                raise HTTPException(
                    status_code=400,
                    detail="Could not extract sufficient text from the file. The file might be empty, corrupted, or contain only images."
                )

            logger.info(f"✅ Successfully extracted {len(extracted_text)} characters from file")

            # Create conversation folder for tracking all calls
            conv_folder = create_conversation_folder(file.filename, "multiple_calls")

            # Extract each section individually
            sections = {}
            confidence_scores = {}
            errors = []
            total_calls = 0

            section_names = [
                "summary", "education", "experience", "skills",
                "projects", "certifications", "achievements", "languages"
            ]

            logger.info(f"🔍 Extracting {len(section_names)} sections individually...")
            logger.info(f"📁 Conversation folder: {conv_folder}")

            for section_name in section_names:
                try:
                    logger.info(f"Extracting {section_name} section...")
                    total_calls += 1
                    content, confidence = extract_single_section(
                        extracted_text,
                        section_name,
                        file.filename,
                        conv_folder,
                        total_calls
                    )
                    sections[section_name] = content
                    confidence_scores[section_name] = confidence

                    if content.startswith("ERROR:"):
                        errors.append(f"{section_name}: {content}")

                except Exception as e:
                    logger.error(f"Error extracting {section_name}: {e}")
                    sections[section_name] = f"ERROR: {str(e)}"
                    confidence_scores[section_name] = 0.0
                    errors.append(f"{section_name}: {str(e)}")

            # Calculate overall confidence
            valid_confidences = [score for score in confidence_scores.values() if score > 0]
            overall_confidence = sum(valid_confidences) / len(valid_confidences) if valid_confidences else 0.0

            processing_time = time.time() - start_time

            # Prepare statistics
            stats = {
                "processing_time": processing_time,
                "total_calls": total_calls,
                "overall_confidence": overall_confidence,
                "text_length": len(extracted_text),
                "sections_found": len([s for s in sections.values() if s and not s.startswith("ERROR:") and s != "NOT_FOUND"])
            }

            # Save extraction results
            save_section_extraction(sections, file.filename, "multiple_calls", stats)

            # Finalize conversation with summary
            finalize_conversation(conv_folder, sections, confidence_scores, stats, "multiple_calls")

            logger.info(f"✅ Section extraction completed in {processing_time:.2f}s with {total_calls} LLM calls")

            return SectionExtractionResponse(
                filename=file.filename,
                extraction_method="multiple_calls",
                sections_extracted=sections,
                extraction_stats=stats,
                confidence_scores=confidence_scores,
                overall_confidence=overall_confidence,
                processing_time=processing_time,
                errors=errors
            )

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in section extraction: {e}")
        raise HTTPException(status_code=500, detail=f"Section extraction failed: {str(e)}")

@app.post("/section2", response_model=SectionExtractionResponse)
async def extract_sections_single_call(
    request: Request,
    file: UploadFile = File(..., description="Resume file (PDF or DOCX)")
):
    """
    Extract resume sections using a single LLM call.

    This endpoint tests the Gemma model's capability to extract all sections
    in one comprehensive call, which may be more efficient but potentially less accurate.
    """
    start_time = time.time()
    request_metrics = getattr(request.state, 'metrics', None)

    try:
        logger.info("🚀 Starting section extraction with single call...")

        # Validate file type
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")

        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in ['.pdf', '.docx']:
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported")

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Extract text from file
            logger.info("📄 Extracting text from uploaded file...")
            file_type = "pdf" if file_extension == ".pdf" else "docx"

            extracted_text = extract_text_from_file(
                temp_file_path,
                file_type,
                request_metrics=request_metrics,
                source_filename=file.filename,
                context="section_extraction"
            )

            if not extracted_text or len(extracted_text.strip()) < 50:
                raise HTTPException(
                    status_code=400,
                    detail="Could not extract sufficient text from the file. The file might be empty, corrupted, or contain only images."
                )

            logger.info(f"✅ Successfully extracted {len(extracted_text)} characters from file")

            # Create conversation folder for tracking the single call
            conv_folder = create_conversation_folder(file.filename, "single_call")

            # Extract all sections in a single call
            logger.info("🔍 Extracting all sections in single call...")
            logger.info(f"📁 Conversation folder: {conv_folder}")
            sections, confidence_scores = extract_all_sections_single_call(extracted_text, file.filename, conv_folder)

            # Calculate overall confidence
            valid_confidences = [score for score in confidence_scores.values() if score > 0]
            overall_confidence = sum(valid_confidences) / len(valid_confidences) if valid_confidences else 0.0

            processing_time = time.time() - start_time

            # Prepare statistics
            stats = {
                "processing_time": processing_time,
                "total_calls": 1,  # Single call method
                "overall_confidence": overall_confidence,
                "text_length": len(extracted_text),
                "sections_found": len([s for s in sections.values() if s and not s.startswith("ERROR:") and s.strip()])
            }

            # Save extraction results
            save_section_extraction(sections, file.filename, "single_call", stats)

            # Finalize conversation with summary
            finalize_conversation(conv_folder, sections, confidence_scores, stats, "single_call")

            logger.info(f"✅ Section extraction completed in {processing_time:.2f}s with 1 LLM call")

            return SectionExtractionResponse(
                filename=file.filename,
                extraction_method="single_call",
                sections_extracted=sections,
                extraction_stats=stats,
                confidence_scores=confidence_scores,
                overall_confidence=overall_confidence,
                processing_time=processing_time,
                errors=[]  # Single call method doesn't track individual section errors
            )

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in section extraction: {e}")
        raise HTTPException(status_code=500, detail=f"Section extraction failed: {str(e)}")

@app.post("/section3", response_model=SectionExtractionResponse)
async def extract_sections_regex_endpoint(
    request: Request,
    file: UploadFile = File(..., description="Resume file (PDF or DOCX)")
):
    """
    Extract resume sections using regex pattern matching.

    This endpoint uses regex patterns to identify section headers and extract content
    between consecutive sections. It's faster than LLM-based methods but may be less
    flexible with varied formatting.

    The regex approach:
    1. Identifies section headers using common patterns (Summary, Education, Experience, etc.)
    2. Extracts all content between consecutive section headers
    3. Associates extracted content with the section name
    4. Handles the last section by extracting to the end of the document
    5. Compiles skills from the entire document if no dedicated skills section is found
    """
    start_time = time.time()
    request_metrics = getattr(request.state, 'metrics', None)

    logger.info("🚀 Starting section extraction with regex patterns...")

    # Validate file type
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")

    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in ['.pdf', '.docx']:
        raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported")

    # Save uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
        content = await file.read()
        temp_file.write(content)
        temp_file_path = temp_file.name

    try:
        # Extract text from file
        logger.info("📄 Extracting text from uploaded file...")
        file_type = "pdf" if file_extension == ".pdf" else "docx"

        extracted_text = extract_text_from_file(
            temp_file_path,
            file_type,
            request_metrics=request_metrics,
            source_filename=file.filename,
            context="section_extraction"
        )

        if not extracted_text or len(extracted_text.strip()) < 50:
            raise HTTPException(
                status_code=400,
                detail="Could not extract sufficient text from the file. The file might be empty, corrupted, or contain only images."
            )

        logger.info(f"✅ Successfully extracted {len(extracted_text)} characters from file")

        # Create conversation folder for tracking the regex extraction
        conv_folder = create_conversation_folder(file.filename, "regex")

        # Extract sections using regex method
        logger.info("🔍 Extracting sections using regex patterns...")
        logger.info(f"📁 Conversation folder: {conv_folder}")
        sections, confidence_scores = extract_sections_regex(extracted_text, file.filename, conv_folder)

        # Calculate overall confidence
        valid_confidences = [score for score in confidence_scores.values() if score > 0]
        overall_confidence = sum(valid_confidences) / len(valid_confidences) if valid_confidences else 0.0

        processing_time = time.time() - start_time

        # Prepare statistics
        stats = {
            "processing_time": processing_time,
            "total_calls": 0,  # No LLM calls for regex method
            "overall_confidence": overall_confidence,
            "text_length": len(extracted_text),
            "sections_found": len([s for s in sections.values() if s and s.strip()]),
            "extraction_method": "regex_pattern_matching"
        }

        # Save extraction results
        save_section_extraction(sections, file.filename, "regex", stats)

        # Finalize conversation with summary
        finalize_conversation(conv_folder, sections, confidence_scores, stats, "regex")

        logger.info(f"✅ Section extraction completed in {processing_time:.2f}s using regex patterns")

        return SectionExtractionResponse(
            filename=file.filename,
            extraction_method="regex",
            sections_extracted=sections,
            extraction_stats=stats,
            confidence_scores=confidence_scores,
            overall_confidence=overall_confidence,
            processing_time=processing_time,
            errors=[]  # Regex method doesn't track individual section errors
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in regex section extraction: {e}")
        raise HTTPException(status_code=500, detail=f"Regex section extraction failed: {str(e)}")
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def parse_sections_with_gemma(sections: Dict[str, str], source_filename: str = None) -> Dict:
    """Parse extracted sections using the Gemma model to create structured JSON output."""
    logger.info("Parsing extracted sections with Gemma model for JSON structuring")

    # EXTENSIVE LOGGING: Log the extracted sections
    logger.info(f"🔍 EXTENSIVE LOG: Processing sections for {source_filename}")
    logger.info(f"🔍 EXTENSIVE LOG: Total sections extracted: {len(sections)}")
    for section_name, content in sections.items():
        if content and content.strip():
            logger.info(f"🔍 EXTENSIVE LOG: Section '{section_name}': {len(content)} chars")
            logger.info(f"🔍 EXTENSIVE LOG: Section '{section_name}' preview: {repr(content[:100])}")
        else:
            logger.info(f"🔍 EXTENSIVE LOG: Section '{section_name}': EMPTY")

    # Create a formatted text from the extracted sections
    formatted_text = ""

    # Add basic info first if available
    if sections.get('basic_info'):
        formatted_text += f"CONTACT INFORMATION:\n{sections['basic_info']}\n\n"
        logger.info(f"🔍 EXTENSIVE LOG: Added basic_info section")

    # Add other sections in a logical order
    section_order = ['summary', 'education', 'experience', 'skills', 'projects', 'certifications', 'achievements', 'languages']

    for section_name in section_order:
        if sections.get(section_name) and sections[section_name].strip():
            formatted_text += f"{section_name.upper()}:\n{sections[section_name]}\n\n"
            logger.info(f"🔍 EXTENSIVE LOG: Added {section_name} section")

    # Add any remaining sections not in the standard order
    for section_name, content in sections.items():
        if section_name not in section_order and not section_name.startswith('basic_') and content and content.strip():
            formatted_text += f"{section_name.upper()}:\n{content}\n\n"
            logger.info(f"🔍 EXTENSIVE LOG: Added additional section: {section_name}")

    # EXTENSIVE LOGGING: Log the final formatted text
    logger.info(f"🔍 EXTENSIVE LOG: Final formatted text length: {len(formatted_text)}")
    logger.info(f"🔍 EXTENSIVE LOG: Formatted text preview: {repr(formatted_text[:200])}")

    # Save formatted text to debug file
    try:
        debug_dir = "debug_responses"
        os.makedirs(debug_dir, exist_ok=True)
        debug_file = f"{debug_dir}/formatted_text_{source_filename or 'unknown'}_{int(time.time())}.txt"
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(f"=== FORMATTED TEXT FOR PROMPT ===\n")
            f.write(f"Source: {source_filename}\n")
            f.write(f"Length: {len(formatted_text)}\n")
            f.write(f"=== CONTENT ===\n")
            f.write(formatted_text)
        logger.info(f"🔍 EXTENSIVE LOG: Saved formatted text to {debug_file}")
    except Exception as e:
        logger.warning(f"🔍 EXTENSIVE LOG: Failed to save formatted text: {e}")

    prompt = f"""
    🚨 CRITICAL FORMATTING RULES - VIOLATION WILL CAUSE SYSTEM FAILURE:
    - NEVER use ```json or ``` or any markdown formatting
    - NEVER add explanations, comments, or extra text before or after JSON
    - NEVER use code blocks, backticks, or markdown syntax
    - Your response must START IMMEDIATELY with {{ (opening brace)
    - Your response must END IMMEDIATELY with }} (closing brace)
    - Return ONLY the JSON object, absolutely nothing else
    - Any markdown formatting will cause parsing failure and data loss
    - This is a machine-to-machine interface - human formatting is forbidden

    You are an expert resume parser. Extract ALL information from the resume sections below and return it as a clean JSON object.

    CRITICAL SCHEMA REQUIREMENTS:
    1. Extract ALL information that is explicitly mentioned in the resume sections.
    2. Format your response as a valid JSON object with EXACTLY the following structure:

    {{
        "name": "Full Name",
        "email": "<EMAIL>" or null,
        "phone": "+1234567890" or null,
        "education": [
            {{
                "degree": "Full Degree Name (Including Specialization)",
                "institution": "Institution Name",
                "year": "Year or Date Range",
                "grade": "GPA/Grade/Percentage if mentioned (e.g., '3.8/4.0', '85%', 'First Class', 'A Grade') or null if not mentioned"
            }}
        ],
        "highest_education": "Highest level of education qualification (e.g., 'Bachelor of Technology', 'Master of Science', 'PhD in Computer Science') or null if no education found",
        "skills": ["Skill 1", "Skill 2", "Skill 3", ...],
        "experience": [
            {{
                "company_name": "Company Name with Location if mentioned",
                "role": "Job Title",
                "duration": "Date Range",
                "key_responsibilities": "Detailed description of responsibilities and achievements"
            }}
        ],
        "projects": [
            {{
                "name": "Project Name",
                "description": "Detailed project description including technologies used"
            }}
        ],
        "certifications": ["Certification Name 1", "Certification Name 2", ...],
        "domain_of_interest": ["Interest 1", "Interest 2", ...],
        "languages_known": ["Language 1", "Language 2", ...],
        "achievements": ["Achievement 1", "Achievement 2", ...],
        "publications": ["Publication 1", "Publication 2", ...],
        "volunteer_experience": ["Volunteer Experience 1", "Volunteer Experience 2", ...],
        "references": [],
        "summary": "Summary text or null",
        "personal_projects": [],
        "social_media": ["platform1.com/username", "platform2.com/username"]
    }}

    STRICT FORMATTING RULES:
    3. For arrays, if no information is available, use an empty array []
    4. For string fields, if no information is available, use null
    5. Do not make up or infer information that is not explicitly stated in the resume
    6. Ensure the JSON is properly formatted and valid
    7. CRITICAL: Keep skills as a simple array of strings, not as objects or dictionaries
    8. CRITICAL: Keep certifications as a simple array of strings, not as objects
    9. CRITICAL: Keep achievements as a simple array of strings, not as objects
    10. CRITICAL: For experience entries, include all details in the key_responsibilities field as a single string with line breaks (\\n)
    11. CRITICAL: For projects, include all details in the description field as a single string with line breaks (\\n)
    12. CRITICAL: Extract the name, email, and phone from the CONTACT INFORMATION section if available

    CONTENT CLASSIFICATION RULES:
    13. EXPERIENCE section should contain ONLY professional work experience with companies/organizations
    14. EXTRA-CURRICULAR ACTIVITIES, sports, competitions, awards should go in ACHIEVEMENTS array
    15. CERTIFICATIONS should be simple strings like "Python for Beginners - Newton School (2024)"
    16. Do NOT create experience entries for activities that are not professional work
    17. Personal activities, sports, competitions, olympiads should be in achievements, not experience

    EDUCATION EXTRACTION RULES:
    18. For each education entry, extract GPA/grades/percentage if mentioned (e.g., "3.8/4.0", "85%", "First Class", "A Grade", "CGPA: 8.5/10")
    19. If no GPA/grade is mentioned for an education entry, set grade field to null
    20. For highest_education field, determine the highest level of education from all entries:
        - PhD/Doctorate > Master's/Postgraduate > Bachelor's/Undergraduate > Diploma/Certificate > High School
        - Include the full degree name with specialization if available
        - If no education is found, set to null

    Resume Sections:
    {formatted_text}

    JSON object (start with {{ immediately):
    """

    # EXTENSIVE LOGGING: Log prompt details
    logger.info(f"🔍 EXTENSIVE LOG: Created prompt for {source_filename}")
    logger.info(f"🔍 EXTENSIVE LOG: Prompt length: {len(prompt)} chars")
    logger.info(f"🔍 EXTENSIVE LOG: Prompt starts with: {repr(prompt[:200])}")
    logger.info(f"🔍 EXTENSIVE LOG: Prompt ends with: {repr(prompt[-200:])}")

    # Save the full prompt to debug file
    try:
        debug_dir = "debug_responses"
        os.makedirs(debug_dir, exist_ok=True)
        debug_file = f"{debug_dir}/prompt_{source_filename or 'unknown'}_{int(time.time())}.txt"
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(f"=== FULL PROMPT ===\n")
            f.write(f"Source: {source_filename}\n")
            f.write(f"Length: {len(prompt)}\n")
            f.write(f"Timestamp: {time.time()}\n")
            f.write(f"=== CONTENT ===\n")
            f.write(prompt)
        logger.info(f"🔍 EXTENSIVE LOG: Saved full prompt to {debug_file}")
    except Exception as e:
        logger.warning(f"🔍 EXTENSIVE LOG: Failed to save prompt: {e}")

    try:
        # EXTENSIVE LOGGING: Before LLM call
        logger.info(f"🔍 EXTENSIVE LOG: Calling LLM for {source_filename}")
        start_time = time.time()

        # Use a 120 second timeout for resume parsing
        response = get_response(
            prompt,
            timeout_seconds=120,
            endpoint="/hybrid_resume",
            context=source_filename or "unknown_resume",
            call_type="main"
        )

        # EXTENSIVE LOGGING: After LLM call
        end_time = time.time()
        logger.info(f"🔍 EXTENSIVE LOG: LLM call completed for {source_filename}")
        logger.info(f"🔍 EXTENSIVE LOG: LLM call took: {end_time - start_time:.2f} seconds")
        logger.info(f"🔍 EXTENSIVE LOG: LLM response type: {type(response)}")
        logger.info(f"🔍 EXTENSIVE LOG: LLM response length: {len(response) if response else 0}")

        # EXTENSIVE LOGGING: Log the raw response for debugging
        logger.info(f"🔍 EXTENSIVE LOG: Raw response length: {len(response)}")
        logger.info(f"🔍 EXTENSIVE LOG: Raw response starts with: {repr(response[:100])}")
        logger.info(f"🔍 EXTENSIVE LOG: Raw response ends with: {repr(response[-100:])}")
        logger.info(f"🔍 EXTENSIVE LOG: Contains ```json: {'```json' in response}")
        logger.info(f"🔍 EXTENSIVE LOG: Contains ```: {'```' in response}")
        logger.info(f"🔍 EXTENSIVE LOG: Starts with {{: {response.strip().startswith('{')}")

        # Save full response to a debug file for analysis
        import os
        debug_dir = "debug_responses"
        os.makedirs(debug_dir, exist_ok=True)
        debug_file = f"{debug_dir}/response_{source_filename or 'unknown'}_{int(time.time())}.txt"
        try:
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(f"=== RAW RESPONSE ===\n")
                f.write(f"Length: {len(response)}\n")
                f.write(f"Source: {source_filename}\n")
                f.write(f"Timestamp: {time.time()}\n")
                f.write(f"=== CONTENT ===\n")
                f.write(response)
            logger.info(f"🔍 EXTENSIVE LOG: Saved full response to {debug_file}")
        except Exception as e:
            logger.warning(f"🔍 EXTENSIVE LOG: Failed to save debug response: {e}")

        # Try to extract JSON from the response
        json_str = response.strip()

        # NUCLEAR OPTION: Apply aggressive cleanup FIRST before any other attempts
        logger.info("🚀 EXTENSIVE LOG: Applying NUCLEAR option: comprehensive JSON extraction and cleanup")
        import re

        # Step 1: Remove ALL markdown variations aggressively
        nuclear_json = json_str
        logger.info(f"🔍 EXTENSIVE LOG: Before nuclear cleanup: {len(nuclear_json)} chars, starts: {repr(nuclear_json[:50])}")
        nuclear_json = nuclear_json.replace('```json', '').replace('```', '').replace('`', '').strip()
        logger.info(f"🔍 EXTENSIVE LOG: After markdown removal: {len(nuclear_json)} chars, starts: {repr(nuclear_json[:50])}")

        # Step 2: Use regex to find JSON content between braces
        json_pattern = r'\{.*\}'
        matches = re.findall(json_pattern, nuclear_json, re.DOTALL)
        logger.info(f"🔍 EXTENSIVE LOG: Regex found {len(matches)} JSON matches")

        if matches:
            # Take the longest match (most likely to be the complete JSON)
            json_candidate = max(matches, key=len)
            logger.info(f"🚀 EXTENSIVE LOG: NUCLEAR: Regex found JSON candidate: {len(json_candidate)} chars")
            logger.info(f"🔍 EXTENSIVE LOG: JSON candidate starts: {repr(json_candidate[:100])}")
            logger.info(f"🔍 EXTENSIVE LOG: JSON candidate ends: {repr(json_candidate[-50:])}")
        else:
            # Fallback: extract between first { and last }
            start_brace = nuclear_json.find('{')
            end_brace = nuclear_json.rfind('}')
            logger.info(f"🔍 EXTENSIVE LOG: Brace positions: start={start_brace}, end={end_brace}")
            if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                json_candidate = nuclear_json[start_brace:end_brace+1]
                logger.info(f"🚀 EXTENSIVE LOG: NUCLEAR: Fallback brace extraction: {len(json_candidate)} chars")
                logger.info(f"🔍 EXTENSIVE LOG: Extracted JSON starts: {repr(json_candidate[:100])}")
            else:
                json_candidate = nuclear_json
                logger.warning("⚠️ EXTENSIVE LOG: NUCLEAR: No braces found, using original")

        # Step 3: Fix character encoding issues (especially "fi" ligature)
        logger.info("🔍 EXTENSIVE LOG: Applying character encoding fixes")
        char_replacements = {
            'ﬁ': 'fi',
            'ﬂ': 'fl',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '–': '-',
            '—': '-',
            '…': '...',
            'Procient': 'Proficient',
            'nal': 'final',
            'ltering': 'filtering',
            'eciency': 'efficiency',
            'Certicate': 'Certificate',
            'Identied': 'Identified',
            'ineciencies': 'inefficiencies',
            'Oce': 'Office'
        }

        for old_char, new_char in char_replacements.items():
            if old_char in json_candidate:
                json_candidate = json_candidate.replace(old_char, new_char)
                logger.info(f"🔍 EXTENSIVE LOG: Replaced '{old_char}' with '{new_char}'")

        # Step 4: Check for incomplete JSON and try to fix it
        json_candidate = json_candidate.strip()
        logger.info(f"🔍 EXTENSIVE LOG: JSON candidate before completion check: {len(json_candidate)} chars")

        # Check if JSON is incomplete (missing closing brace)
        if json_candidate.startswith('{') and not json_candidate.endswith('}'):
            logger.warning("🔧 EXTENSIVE LOG: JSON appears incomplete - missing closing brace")
            # Count opening and closing braces
            open_braces = json_candidate.count('{')
            close_braces = json_candidate.count('}')
            missing_braces = open_braces - close_braces

            if missing_braces > 0:
                logger.info(f"🔧 EXTENSIVE LOG: Adding {missing_braces} missing closing braces")
                json_candidate += '}' * missing_braces
                logger.info(f"🔧 EXTENSIVE LOG: JSON after completion: {len(json_candidate)} chars, ends with: {repr(json_candidate[-10:])}")

        # Step 5: Final cleanup
        logger.info(f"🔍 EXTENSIVE LOG: Final JSON candidate: {len(json_candidate)} chars")

        # Save the nuclear-cleaned JSON for analysis
        try:
            debug_file = f"{debug_dir}/nuclear_cleaned_{source_filename or 'unknown'}_{int(time.time())}.json"
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(json_candidate)
            logger.info(f"🔍 EXTENSIVE LOG: Saved nuclear-cleaned JSON to {debug_file}")
        except Exception as e:
            logger.warning(f"🔍 EXTENSIVE LOG: Failed to save nuclear-cleaned JSON: {e}")

        # Step 5: Try parsing the nuclear-cleaned JSON FIRST
        try:
            parsed_data = json.loads(json_candidate)
            logger.info("🎉 EXTENSIVE LOG: SUCCESS: NUCLEAR option worked - JSON parsed on FIRST attempt!")
            logger.info(f"🎉 EXTENSIVE LOG: NUCLEAR SUCCESS: Returning parsed data with name: {parsed_data.get('name', 'Unknown')}")
            logger.info(f"🎉 EXTENSIVE LOG: NUCLEAR SUCCESS: Data has {len(parsed_data.get('education', []))} education entries")
            logger.info(f"🎉 EXTENSIVE LOG: NUCLEAR SUCCESS: Data has {len(parsed_data.get('skills', []))} skills")
            logger.info(f"🎉 EXTENSIVE LOG: NUCLEAR SUCCESS: Data has {len(parsed_data.get('experience', []))} experience entries")
            logger.info(f"🎉 EXTENSIVE LOG: NUCLEAR SUCCESS: Data has {len(parsed_data.get('projects', []))} projects")

            # Validate that we have meaningful data
            has_meaningful_data = (
                parsed_data.get('name') and parsed_data.get('name') != 'Unknown' and
                (len(parsed_data.get('education', [])) > 0 or
                 len(parsed_data.get('skills', [])) > 0 or
                 len(parsed_data.get('experience', [])) > 0)
            )

            if has_meaningful_data:
                # NUCLEAR SUCCESS: Force immediate return - bypass all other logic
                logger.info("🚀 NUCLEAR SUCCESS: MEANINGFUL DATA FOUND - FORCING IMMEDIATE RETURN")
                return parsed_data
            else:
                logger.warning("⚠️ NUCLEAR WARNING: JSON parsed but contains minimal data - will try nuclear safety net")
                raise ValueError("Parsed JSON contains minimal data")

        except json.JSONDecodeError as nuclear_error:
            logger.warning(f"❌ EXTENSIVE LOG: NUCLEAR option failed on first attempt: {nuclear_error}")
            logger.warning(f"❌ EXTENSIVE LOG: NUCLEAR error position: {nuclear_error.pos}")
            logger.warning(f"❌ EXTENSIVE LOG: NUCLEAR JSON preview: {repr(json_candidate[:100])}")
            if nuclear_error.pos < len(json_candidate):
                char_at_error = json_candidate[nuclear_error.pos]
                logger.warning(f"❌ EXTENSIVE LOG: Character at error: '{char_at_error}' (ord: {ord(char_at_error)})")
                context = json_candidate[max(0, nuclear_error.pos-30):nuclear_error.pos+30]
                logger.warning(f"❌ EXTENSIVE LOG: Error context: {repr(context)}")

            # NUCLEAR FALLBACK: Try with character replacement
            logger.info("🔧 NUCLEAR FALLBACK: Trying character replacement")
            char_replacements = {
                'ecient': 'efficient', 'simplies': 'simplifies', 'Qualied': 'Qualified',
                'inBadminton': 'in Badminton', 'inScouts': 'in Scouts', 'dierent': 'different',
                'ﬁ': 'fi', 'ﬂ': 'fl', 'ﬃ': 'ffi', 'ﬄ': 'ffl', 'ﬀ': 'ff',
                '"': '"', '"': '"', ''': "'", ''': "'", '–': '-', '—': '-'
            }

            fixed_candidate = json_candidate
            for old_char, new_char in char_replacements.items():
                if old_char in fixed_candidate:
                    fixed_candidate = fixed_candidate.replace(old_char, new_char)
                    logger.info(f"🔧 NUCLEAR: Replaced '{old_char}' with '{new_char}'")

            try:
                parsed_data = json.loads(fixed_candidate)
                logger.info("🎉 NUCLEAR FALLBACK SUCCESS: Character replacement worked!")
                logger.info(f"🎉 NUCLEAR FALLBACK: Returning data with name: {parsed_data.get('name', 'Unknown')}")

                # NUCLEAR FALLBACK SUCCESS: Force immediate return
                logger.info("🚀 NUCLEAR FALLBACK SUCCESS: FORCING IMMEDIATE RETURN")
                return parsed_data

            except json.JSONDecodeError as fallback_error:
                logger.warning(f"❌ NUCLEAR FALLBACK also failed: {fallback_error}")
                json_str = fixed_candidate  # Use the fixed candidate for further processing



        # Try JSON self-healing before falling back to old repair
        logger.info("Attempting JSON self-healing...")
        healed_json = llm_json_self_healing(json_str, "resume")

        if healed_json:
                    # NUCLEAR MARKDOWN REMOVAL - LLM keeps ignoring instructions
                    logger.info("Applying NUCLEAR markdown removal to self-healing response")

                    # Step 1: Strip whitespace
                    healed_json = healed_json.strip()

                    # Step 2: Remove ALL possible markdown variations
                    healed_json = healed_json.replace('```json', '').replace('```', '').strip()

                    # Step 3: Force extract between first { and last }
                    start_brace = healed_json.find('{')
                    end_brace = healed_json.rfind('}')
                    if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                        healed_json = healed_json[start_brace:end_brace+1]
                        logger.info(f"NUCLEAR: Extracted JSON between braces, length: {len(healed_json)}")

                    # Step 4: Additional cleanup
                    healed_json = healed_json.replace('`', '').strip()

                    logger.info(f"NUCLEAR cleanup result: {len(healed_json)} chars, starts with: {repr(healed_json[:30])}")

                    try:
                        parsed_data = json.loads(healed_json)
                        logger.info("SUCCESS: NUCLEAR JSON self-healing worked!")
                        logger.info(f"NUCLEAR SUCCESS: Returning parsed data with name: {parsed_data.get('name', 'Unknown')}")
                        logger.info(f"NUCLEAR SUCCESS: Data has {len(parsed_data.get('education', []))} education entries")
                        logger.info(f"NUCLEAR SUCCESS: Data has {len(parsed_data.get('skills', []))} skills")
                        return parsed_data
                    except json.JSONDecodeError as heal_error:
                        logger.warning(f"NUCLEAR JSON self-healing still failed: {heal_error}")
                        logger.warning(f"Problematic JSON preview: {repr(healed_json[:100])}")
                        logger.warning(f"NUCLEAR FAILURE: Error at position {heal_error.pos}")
                        # Continue to old repair system
        else:
            logger.warning("JSON self-healing returned empty result")

        # Parse the JSON (fallback to original logic)
        try:
            logger.info(f"Attempting to parse JSON. Length: {len(json_str)}, starts with: {repr(json_str[:50])}")
            parsed_data = json.loads(json_str)
            logger.info("Successfully parsed JSON response from Gemma")
            return parsed_data
        except json.JSONDecodeError as json_error:
            logger.warning(f"JSON parsing failed: {json_error}")
            logger.warning(f"JSON error position: {json_error.pos}")
            logger.warning(f"JSON content preview: {repr(json_str[:200])}")

            # Try multiple aggressive cleanup methods before going to LLM repair
            logger.info("Trying aggressive cleanup before LLM repair...")

            # Method 1: Extract between first { and last }
            start_brace = json_str.find('{')
            end_brace = json_str.rfind('}')
            if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                aggressive_json = json_str[start_brace:end_brace+1]
                try:
                    parsed_data = json.loads(aggressive_json)
                    logger.info("Successfully parsed JSON using aggressive brace extraction")
                    return parsed_data
                except json.JSONDecodeError as e2:
                    logger.info(f"Aggressive cleanup failed: {e2}")

                    # Method 2: Try fixing special characters
                    logger.info("Trying special character normalization...")
                    normalized_json = aggressive_json.encode('ascii', 'ignore').decode('ascii')
                    try:
                        parsed_data = json.loads(normalized_json)
                        logger.info("Successfully parsed JSON after character normalization")
                        return parsed_data
                    except json.JSONDecodeError as e3:
                        logger.info(f"Character normalization failed: {e3}")

                        # Method 3: Try comprehensive character replacement
                        logger.info("Trying comprehensive character replacement...")
                        fixed_json = aggressive_json

                        # Replace common problematic characters
                        char_replacements = {
                            'ﬁ': 'fi',
                            'ﬂ': 'fl',
                            '"': '"',
                            '"': '"',
                            ''': "'",
                            ''': "'",
                            '–': '-',
                            '—': '-',
                            'Ö': 'O',
                            'ö': 'o',
                            'Ü': 'U',
                            'ü': 'u',
                            'Ä': 'A',
                            'ä': 'a',
                            '…': '...',
                            '€': 'EUR',
                            '£': 'GBP',
                            '¢': 'cents',
                            '©': '(c)',
                            '®': '(r)',
                            '™': '(tm)',
                            '°': ' degrees',
                            '±': '+/-',
                            '×': 'x',
                            '÷': '/',
                            '≤': '<=',
                            '≥': '>=',
                            '≠': '!=',
                            '≈': '~=',
                            'α': 'alpha',
                            'β': 'beta',
                            'γ': 'gamma',
                            'δ': 'delta',
                            'π': 'pi',
                            'σ': 'sigma',
                            'μ': 'mu',
                            'λ': 'lambda'
                        }

                        for old_char, new_char in char_replacements.items():
                            fixed_json = fixed_json.replace(old_char, new_char)

                        try:
                            parsed_data = json.loads(fixed_json)
                            logger.info("Successfully parsed JSON after comprehensive character replacement")
                            return parsed_data
                        except json.JSONDecodeError as e4:
                            logger.info(f"Comprehensive character replacement failed: {e4}")

                            # Method 4: Last resort - encode/decode to remove all non-ASCII
                            logger.info("Trying ASCII-only encoding as last resort...")
                            try:
                                ascii_json = fixed_json.encode('ascii', 'ignore').decode('ascii')
                                parsed_data = json.loads(ascii_json)
                                logger.info("Successfully parsed JSON after ASCII encoding")
                                return parsed_data
                            except json.JSONDecodeError as e5:
                                logger.info(f"ASCII encoding failed: {e5}")

            logger.info("All aggressive cleanup methods failed, proceeding to LLM repair")

            logger.info("Attempting to repair malformed JSON using LLM")

            # Try to fix the JSON using the LLM
            try:
                logger.info("Calling JSON repair function...")
                fixed_json = llm_fix_malformed_json(json_str, "resume")
                logger.info(f"JSON repair returned: {len(fixed_json) if fixed_json else 0} characters")

                # Check if repair returned None or empty string
                if not fixed_json or fixed_json.strip() == "":
                    logger.error("JSON repair returned empty or None result")
                    raise ValueError("JSON repair returned empty result")

                # Aggressive cleanup for repaired JSON
                fixed_json = fixed_json.strip()

                # Remove markdown code blocks - multiple approaches
                if "```json" in fixed_json:
                    start_marker = "```json"
                    end_marker = "```"
                    start_idx = fixed_json.find(start_marker)
                    if start_idx != -1:
                        start_idx += len(start_marker)
                        end_idx = fixed_json.find(end_marker, start_idx)
                        if end_idx != -1:
                            fixed_json = fixed_json[start_idx:end_idx].strip()
                elif "```" in fixed_json:
                    parts = fixed_json.split("```")
                    if len(parts) >= 3:
                        fixed_json = parts[1].strip()

                # Additional cleanup
                if fixed_json.startswith('```json'):
                    fixed_json = fixed_json[7:].strip()
                if fixed_json.startswith('```'):
                    fixed_json = fixed_json[3:].strip()
                if fixed_json.endswith('```'):
                    fixed_json = fixed_json[:-3].strip()

                # Remove any remaining markdown artifacts
                fixed_json = fixed_json.replace('```json', '').replace('```', '').strip()

                # Final aggressive cleanup for repair response
                if '```' in fixed_json or not fixed_json.startswith('{'):
                    logger.info("Applying final aggressive cleanup to repair response")
                    start_brace = fixed_json.find('{')
                    end_brace = fixed_json.rfind('}')
                    if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                        fixed_json = fixed_json[start_brace:end_brace+1]
                        logger.info("Final repair cleanup: extracted JSON between braces")

                logger.info(f"Final repaired JSON length: {len(fixed_json)}, starts with: {repr(fixed_json[:50])}")
                parsed_data = json.loads(fixed_json)
                logger.info("Successfully repaired and parsed JSON using LLM")
                return parsed_data
            except Exception as repair_error:
                logger.error(f"JSON repair also failed: {repair_error}")
                logger.error(f"Original JSON error: {json_error}")
                logger.error(f"Failed JSON content preview: {json_str[:200]}...")

                # FINAL NUCLEAR SAFETY NET: Check if we have nuclear-cleaned data in debug files
                logger.info("🚨 FINAL NUCLEAR SAFETY NET: Checking for nuclear-cleaned data")
                try:
                    debug_dir = "debug_responses"
                    import glob
                    pattern = f"{debug_dir}/nuclear_cleaned_{source_filename or 'unknown'}*.json"
                    nuclear_files = glob.glob(pattern)

                    if nuclear_files:
                        # Get the most recent nuclear file
                        latest_nuclear_file = max(nuclear_files, key=os.path.getmtime)
                        logger.info(f"🚨 NUCLEAR SAFETY NET: Found nuclear file: {latest_nuclear_file}")

                        with open(latest_nuclear_file, 'r', encoding='utf-8') as f:
                            nuclear_data = json.load(f)

                        # Check if this nuclear data is valid and has meaningful content
                        if (nuclear_data.get('name') != 'Unknown' and nuclear_data.get('name') and
                            (len(nuclear_data.get('education', [])) > 0 or
                             len(nuclear_data.get('skills', [])) > 0 or
                             len(nuclear_data.get('experience', [])) > 0)):

                            logger.info("🎉 NUCLEAR SAFETY NET SUCCESS: Found valid nuclear data!")
                            logger.info(f"🎉 NUCLEAR SAFETY NET: Name: {nuclear_data.get('name')}")
                            logger.info(f"🎉 NUCLEAR SAFETY NET: Education: {len(nuclear_data.get('education', []))}")
                            logger.info(f"🎉 NUCLEAR SAFETY NET: Skills: {len(nuclear_data.get('skills', []))}")
                            logger.info(f"🎉 NUCLEAR SAFETY NET: Experience: {len(nuclear_data.get('experience', []))}")

                            # Add metadata to indicate this was recovered
                            nuclear_data["nuclear_recovery"] = True
                            nuclear_data["nuclear_recovery_source"] = latest_nuclear_file

                            return nuclear_data
                        else:
                            logger.info("🚨 NUCLEAR SAFETY NET: Nuclear data exists but is empty/invalid")
                    else:
                        logger.info("🚨 NUCLEAR SAFETY NET: No nuclear files found")

                except Exception as nuclear_safety_error:
                    logger.warning(f"🚨 NUCLEAR SAFETY NET failed: {nuclear_safety_error}")

                # Return a structured error response instead of raising exception
                return {
                    "name": "Unknown",
                    "email": None,
                    "phone": None,
                    "education": [],
                    "highest_education": None,
                    "skills": [],
                    "experience": [],
                    "projects": [],
                    "certifications": [],
                    "domain_of_interest": [],
                    "languages_known": [],
                    "social_media": [],
                    "achievements": [],
                    "publications": [],
                    "volunteer_experience": [],
                    "references": [],
                    "summary": None,
                    "personal_projects": [],
                    "error": "JSON parsing failed",
                    "error_details": f"Original: {str(json_error)}, Repair: {str(repair_error)}",
                    "raw_response_preview": json_str[:200] if json_str else "No response"
                }

    except Exception as e:
        logger.error(f"Error in Gemma parsing: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Return a minimal valid structure instead of raising an exception
        return {
            "name": "Unknown",
            "email": None,
            "phone": None,
            "education": [],
            "highest_education": None,
            "skills": [],
            "experience": [],
            "projects": [],
            "certifications": [],
            "domain_of_interest": [],
            "languages_known": [],
            "social_media": [],
            "achievements": [],
            "publications": [],
            "volunteer_experience": [],
            "references": [],
            "summary": None,
            "personal_projects": [],
            "error": "Failed to parse resume data",
            "error_type": type(e).__name__,
            "error_details": str(e)
        }

@app.post("/hybrid_resume", response_model=Dict, summary="Hybrid resume parsing", description="Extract sections using regex then parse with LLM for structured JSON output")
async def hybrid_resume_parsing(
    request: Request,
    file: UploadFile = File(..., description="Resume file to parse (PDF or DOCX format)")
):
    """
    Hybrid resume parsing that combines regex section extraction with LLM JSON structuring.

    This endpoint:
    1. Uses fast regex-based section extraction (/section3 method)
    2. Passes extracted sections to LLM for intelligent JSON structuring
    3. Returns structured JSON output like /resume endpoint

    Benefits:
    - Faster than pure LLM parsing (pre-structured sections)
    - More accurate than pure regex (LLM handles JSON structuring)
    - Cost-effective (reduced LLM token usage)
    - Combines best of both approaches
    """
    start_time = time.time()
    request_metrics = getattr(request.state, 'metrics', None)

    logger.info("🚀 Starting hybrid resume parsing (regex + LLM)")

    # Validate file type
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")

    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in ['.pdf', '.docx']:
        raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported")

    # Save uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
        content = await file.read()
        temp_file.write(content)
        temp_file_path = temp_file.name

    try:
        # Step 1: Extract text from file
        logger.info("📄 Extracting text from uploaded file...")
        file_type = "pdf" if file_extension == ".pdf" else "docx"

        extracted_text = extract_text_from_file(
            temp_file_path,
            file_type,
            request_metrics=request_metrics,
            source_filename=file.filename,
            context="hybrid_resume_parsing"
        )

        if not extracted_text or len(extracted_text.strip()) < 50:
            raise HTTPException(
                status_code=400,
                detail="Could not extract sufficient text from the file. The file might be empty, corrupted, or contain only images."
            )

        logger.info(f"✅ Successfully extracted {len(extracted_text)} characters from file")

        # Step 2: Extract sections using regex method
        logger.info("🔍 Extracting sections using regex patterns...")
        sections, confidence_scores = extract_sections_regex(extracted_text, file.filename, "")

        logger.info(f"📊 Regex extraction completed: {len(sections)} sections found")
        sections_with_content = [s for s in sections.values() if s and s.strip()]
        logger.info(f"📋 Sections with content: {len(sections_with_content)}")

        # Step 3: Parse sections with LLM for JSON structuring
        logger.info("🤖 Processing sections with LLM for JSON structuring...")
        try:
            structured_data = parse_sections_with_gemma(sections, file.filename)

            # Check if we got an error response
            if structured_data.get("error"):
                logger.warning(f"LLM parsing returned error: {structured_data.get('error')}")
                # Continue with the error response - it's still valid JSON

        except Exception as llm_error:
            logger.error(f"LLM processing failed completely: {llm_error}")
            # Create a fallback response
            structured_data = {
                "name": "Unknown",
                "email": None,
                "phone": None,
                "education": [],
                "highest_education": None,
                "skills": [],
                "experience": [],
                "projects": [],
                "certifications": [],
                "domain_of_interest": [],
                "languages_known": [],
                "social_media": [],
                "achievements": [],
                "publications": [],
                "volunteer_experience": [],
                "references": [],
                "summary": None,
                "personal_projects": [],
                "error": "LLM processing failed",
                "error_details": str(llm_error)
            }

        # Step 4: Normalize the data
        logger.info("📊 Normalizing structured data...")
        try:
            normalized_data = normalize_resume_data(structured_data, convert_skills_to_dict_format=True)
        except Exception as norm_error:
            logger.error(f"Data normalization failed: {norm_error}")
            # Use the structured data as-is if normalization fails
            normalized_data = structured_data

        # Calculate processing time
        processing_time = time.time() - start_time

        # Add metadata
        normalized_data["processing_time"] = processing_time
        normalized_data["extraction_method"] = "hybrid_regex_llm"
        normalized_data["sections_extracted"] = len(sections_with_content)
        normalized_data["regex_confidence"] = sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0.0

        logger.info(f"✅ Hybrid resume parsing completed in {processing_time:.2f}s")
        logger.info(f"📊 Final result: {len(normalized_data.get('skills', []))} skills, {len(normalized_data.get('experience', []))} experience entries")

        return normalized_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in hybrid resume parsing: {e}")
        raise HTTPException(status_code=500, detail=f"Hybrid resume parsing failed: {str(e)}")
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
