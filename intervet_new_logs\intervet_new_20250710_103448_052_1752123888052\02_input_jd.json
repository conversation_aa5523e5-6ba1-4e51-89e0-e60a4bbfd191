{"job_title": "Full Stack Developer", "company_name": null, "location": "Bengaluru", "job_type": "Flexible working hours (full -time / part-time)", "work_mode": "On-site", "department": null, "summary": "Build highly responsive web applications with engaging user experience.", "responsibilities": ["Build highly responsive web applications with engaging user experience", "Design client-side and server-side architecture", "Write production ready code for both front end and back end systems", "Develop user friendly web front end based on visual designs and branding guidelines", "Conduct thorough testing of the developed applications and fix identified bugs", "Create documentation for applications developed"], "required_skills": ["HTML", "CSS", "PHP", "Python", "JavaScript", "REST API", "Git", "MySQL", "MongoDB", "Apache", "Wordpress"], "preferred_skills": ["React framework", "Mobile app / progressive web app development experience", "A flair for creativity and out of the box thinking"], "required_experience": "Experience will be considered, but not necessary", "education_requirements": ["Students in third or final year of engineering in Computer Science / Electronics / IT", "Should have consistent marks above 75% or GPA 3.0/4.0 or above in all previous semesters"], "education_details": {"degree_level": "Engineering", "field_of_study": "Computer Science / Electronics / IT", "is_required": true, "alternatives": "Bachelor's degree"}, "salary_range": "monthly remuneration between Rs.15,000 to Rs.30,000 depending on hours of working and education level", "benefits": [], "requirements": [], "application_deadline": null, "posting_date": null, "contact_information": null, "company_description": null, "industry": null, "career_level": "Entry", "confidence_score": 0.82, "confidence_details": {}}