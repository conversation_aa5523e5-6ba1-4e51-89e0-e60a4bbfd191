{"total_score": 3.15, "fit_category": "Weak Match", "summary": "The candidate is a weak match for this position with a CGPA-style score of 3.1/10. Areas for improvement: Skills, Certifications.", "skills_score": {"raw_score": 0.5, "weight": 4.0, "weighted_score": 2.0, "rationale": "Limited skills match - has 1/16 required skills, major skill development needed"}, "experience_score": {"raw_score": 5.0, "weight": 3.0, "weighted_score": 15.0, "rationale": "Moderate experience level - 0 years, some experience gaps"}, "education_score": {"raw_score": 6.0, "weight": 2.0, "weighted_score": 12.0, "rationale": "Good education match - has related degree with some relevance"}, "certifications_score": {"raw_score": 0.0, "weight": 0.5, "weighted_score": 0.0, "rationale": "No relevant certifications - 1 certifications but none match job requirements"}, "location_score": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate location match - some geographic alignment"}, "reliability_score": {"raw_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Moderate job stability - average 0.0 years per company"}, "detailed_rationale": {"skills_match_direct": "Matched 1/16 required skills. Matched required skills: Statistical programming tools (e.g., R, Python, SQL). Missing required skills: Operating standard office equipment including utilizing pertinent software applications (e.g., Word, Excel, databases, PowerPoint), Planning and managing projects and programs, Operating within established financial parameters, Developing effective working relationships, Preparing and maintaining accurate records and dashboards, Administering personnel policies and procedures as assigned, Applying program evaluation and assessment techniques, Advanced data mining and analysis techniques, Student data security protocols, Data visualization tools (e.g., Tableau, D3, AJAX, or jQuery), Complex mathematical calculations and projections, Administrative and technical expertise with Customer Relationship Management (CRM) platforms, Data warehouse tools, Machine learning techniques, including classification and outlier detection of data, Business process improvement tools (e.g., LEAN/Six Sigma or similar)", "experience_match": "No specific experience requirement found in job description", "reliability": "Poor stability: frequent job changes with 0.0 years per company", "location_match": "No location match found with job location (san diego, ca)", "academic_match": "Education requirements not met. Candidate: Bachelor of Technology (AI ML), Intermediate, Matriculation, Required: A master’s degree from an accredited college or university in computer science, mathematics, statistics, analytics, economics, or other closely related field.", "alma_mater": "No top-ranked universities found in education history", "certifications": "No relevant certifications found"}, "total_credits_used": 10.0, "calculation_method": "CGPA", "processing_time": 0.011001348495483398}