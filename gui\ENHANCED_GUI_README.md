# Enhanced Intervet2 GUI

A comprehensive GUI application for testing the enhanced `/intervet2` endpoint with bulk upload capabilities and visual result display.

## Features

### 🚀 Core Functionality
- **Bulk Resume Upload**: Select and process multiple resume files at once
- **Single JD Upload**: Upload one job description to compare against all resumes
- **Real-time Processing**: Visual progress indication during processing
- **Comprehensive Results**: Display parsing results, evaluation scores, and processing times

### 📊 Visual Display
- **Evaluation Summary**: Final scores, fit categories, and summaries
- **Processing Times**: Breakdown of time taken for each processing step
- **Parsing Results**: Detailed information extracted from resumes and JDs
- **Error Handling**: Clear display of any processing errors

### 💾 Data Management
- **Export Results**: Save all results to JSON format for further analysis
- **Clear Functions**: Easy clearing of files and results
- **Status Updates**: Real-time status updates during processing

## Installation

### Prerequisites
- Python 3.7 or higher
- API server running on `http://localhost:8000`

### Quick Start
1. **Install dependencies**:
   ```bash
   pip install -r enhanced_gui_requirements.txt
   ```

2. **Start the API server** (in the main project directory):
   ```bash
   python main.py
   ```

3. **Launch the GUI**:
   ```bash
   python launch_enhanced_gui.py
   ```

### Alternative Launch Methods
- **Direct launch**:
  ```bash
  python enhanced_intervet2_gui.py
  ```

- **Using the launcher** (recommended):
  ```bash
  python launch_enhanced_gui.py
  ```

## Usage Guide

### 1. File Selection
1. **Select Resume Files**: Click "Select Resume Files" to choose multiple PDF or DOCX resume files
2. **Select JD File**: Click "Select JD File" to choose one job description file
3. **Verify Selection**: Check that all files are listed correctly

### 2. Processing
1. **Start Processing**: Click "Process All Files" to begin evaluation
2. **Monitor Progress**: Watch the progress bar and status updates
3. **Wait for Completion**: Processing may take several minutes depending on file count

### 3. Viewing Results
- **Results Display**: Scroll through the results panel to see detailed information
- **Evaluation Scores**: View CGPA-style scores and fit categories
- **Processing Times**: See how long each step took
- **Parsing Details**: Review extracted information from resumes and JDs

### 4. Export and Management
- **Export Results**: Save all results to a JSON file for external analysis
- **Clear Results**: Remove all current results from the display
- **Clear Files**: Remove selected files to start fresh

## Result Structure

Each processed resume-JD pair provides:

### Evaluation Result
- **Total Score**: CGPA-style score out of 10
- **Fit Category**: Excellent/Strong/Good/Moderate/Weak Match
- **Summary**: Human-readable evaluation summary
- **Field Scores**: Detailed scores for skills, experience, education, etc.

### Processing Information
- **Resume Parsing Time**: Time taken to parse the resume
- **JD Parsing Time**: Time taken to parse the job description
- **Evaluation Time**: Time taken to calculate the match score
- **Total Processing Time**: Overall time for the entire process

### Parsed Data
- **Resume Data**: Extracted information (name, skills, experience, education)
- **JD Data**: Extracted job requirements (title, skills, experience requirements)

## Supported File Formats
- **PDF files** (`.pdf`)
- **Word documents** (`.docx`)

## Error Handling
The GUI handles various error scenarios:
- **Server Connection Issues**: Checks server status and provides clear feedback
- **File Processing Errors**: Displays specific error messages for failed files
- **Invalid File Formats**: Warns about unsupported file types
- **API Errors**: Shows detailed error information from the server

## Performance Notes
- **Processing Time**: Each resume-JD pair takes 30-60 seconds to process
- **Bulk Processing**: Files are processed sequentially for stability
- **Memory Usage**: Large files or many files may require significant memory
- **Server Load**: Heavy processing may impact server responsiveness

## Troubleshooting

### Common Issues

1. **Server Not Running**
   - Error: "Server Status: ❌ Offline"
   - Solution: Start the API server with `python main.py`

2. **Processing Fails**
   - Check file formats (only PDF and DOCX supported)
   - Ensure files are not corrupted
   - Check server logs for detailed error information

3. **Slow Processing**
   - Large files take longer to process
   - Multiple files are processed sequentially
   - Server performance depends on available resources

4. **GUI Not Responding**
   - Processing happens in background threads
   - GUI should remain responsive during processing
   - If frozen, restart the application

### Getting Help
- Check the server logs for detailed error information
- Ensure all dependencies are installed correctly
- Verify file formats and integrity
- Test with smaller files first

## Technical Details

### API Integration
- Uses the enhanced `/intervet2` endpoint
- Supports the new response format with separate parsing results
- Handles the comprehensive evaluation structure

### GUI Framework
- Built with tkinter (Python standard library)
- Threaded processing to maintain responsiveness
- Scrollable results display for large datasets

### Data Flow
1. File selection and validation
2. Sequential processing through `/intervet2` endpoint
3. Real-time result display and status updates
4. Optional export to JSON format

## Future Enhancements
- Parallel processing for faster bulk operations
- Advanced filtering and sorting of results
- Graphical charts and visualizations
- Custom weightage configuration in GUI
- Resume-to-resume comparison features
