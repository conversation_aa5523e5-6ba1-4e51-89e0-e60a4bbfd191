{"timestamp": "20250708_141410_942", "calculation_method": "CGPA", "total_credits_used": 10.0, "processing_time_seconds": 0.018135547637939453, "final_score": 4.***************, "fit_category": "Moderate Match", "summary": "The candidate is a moderate match for this position with a CGPA-style score of 4.4/10. Key strengths: Education. Areas for improvement: Skills, Certifications.", "field_scores": {"skills": {"raw_score": 1.6842105263157894, "weight": 4.0, "weighted_score": 6.7368421052631575, "rationale": "Limited skills match - has 12/57 required skills, major skill development needed", "details": {"resume_skills_count": 17, "required_skills_count": 57, "preferred_skills_count": 0, "matched_required": ["Python", "Java", "JavaScript", "SQL", "Communication", "Leadership", "And Ability To Use Java In The Development", "And Ability To Use Python In The Development", "And Ability To Use Sql", "And Ability To Use Javascript In The Development", "Effective Leadership Theories", "And Ability To Use Various Techniques For Conducting Research"], "missing_required": ["AWS", "Agile", "Problem Solving", "All Major Talentguard Confidential", "Computer Science", "The Ability To Apply The Knowledge In The Design", "Development Of Computer Software", "And Ability To Carry Out The Processes", "Agile Software Development Methodology", "Ability To Apply It To Diverse Situations", "And Ability To Utilize A Variety Of Specific Technical Tools", "Platforms To Develop", "Support Applications", "Maintenance Of Application Programs", "Systems", "Maintenance Of Production Software", "And Ability To Use Amazon Web Services", "Maintenance Of Websites", "Web Applications", "The Full Range Of Activities Related To Designing", "The Communication Strategies", "Ability To Effectively Inform", "Approaches", "And Ability To Plan", "Techniques", "And Ability To Use The Day", "The Importance Of Innovation", "The Full Spectrum Of Written Communications", "Ability To Effectively Write Various Technical", "Business Documents", "And Ability To Develop A Blueprint For Achieving Desired Results", "And Recommended Proficiency Level Technical Skills Common Skills Skills Target Proficiency Level Skills Target Proficiency Level Computer Science 3 Communication 2 Software Engineering 3 Problem Solving 2 Agile Methodology 2 Management 2 Software Development 3 Troubleshooting", "And Proficiency Level S Skills Title Computer Science Knowledge Of Computer Science", "And Proficiency Levels Communication Knowledge Of The Communication Strategies", "Explain", "Advise Through Written", "Oral Communication In A Clear", "Concise", "Professional Manner", "Makes Recommendations For The Organization", "For Operations Management", "Job Responsibility 2 Collaborate With Team Members To Design", "Implement Software Solutions Job Responsibility 3 Conduct Code Reviews", "Provide Feedback To Improve Code Quality Job Responsibility 4 Troubleshoot", "Debug Software Applications To Ensure Optimal Performance Talentguard Confidential"], "matched_preferred": [], "required_match_ratio": 0.21052631578947367, "preferred_match_ratio": 0, "_calculation_steps": ["Step 1: Required skills score = 12/57 × 8 = 1.68", "Step 2: No preferred skills specified, bonus = 0.00", "Step 3: Total score = min(10, 1.68 + 0.00) = 1.68"], "_scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "_explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)"}}, "experience": {"raw_score": 5.0, "weight": 3.0, "weighted_score": 15.0, "rationale": "Moderate experience level - 0 years, some experience gaps", "details": {"candidate_yoe": 0, "required_yoe": null, "experience_ratio": null, "experience_entries_count": 1, "_experience_breakdown": [{"company": "Unknown Company", "position": "Unknown Position", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}], "_calculation_steps": ["Step 1: No required experience specified in job description", "Step 2: Analyzing 1 experience entries", "  Entry 1: Unknown Company - Unknown Position (Present) = 0 years (could not parse)", "Step 2 Result: Total candidate experience = 0 years", "Step 3: Calculating experience score", "  ~ No experience requirement specified: Score = 5.0 (neutral)"], "_scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "_explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification"}}, "education": {"raw_score": 10.0, "weight": 2.0, "weighted_score": 20.0, "rationale": "Perfect education match - meets all degree requirements", "details": {"education_entries_count": 3, "education_requirements_count": 13, "education_match": true, "partial_match": false, "matched_degree": "Bachelor of Technology (AI ML)", "matched_requirement": "Ba", "match_type": "exact_match", "candidate_degrees": ["Bachelor of Technology (AI ML)", "Intermediate", "Matriculation"], "required_degrees": ["Ms In A Timely And Efficient <PERSON>", "Ma", "Ba", "Bas", "Ms", "Bs", "BS", "BA", "MS", "MA", "Computer Science", "Engineering", "Business"], "education_requirements": ["Ms In A Timely And Efficient <PERSON>", "Ma", "Ba", "Bas", "Ms", "Bs", "BS", "BA", "MS", "MA", "Computer Science", "Engineering", "Business"], "top_universities_found": [], "_calculation_steps": ["Step 1: Checking 13 education requirement(s)", "  - Analyzing requirement: 'Ms In A Timely And Efficient Manner'", "    Required degree type: master", "    Required field: Not specified", "    Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)", "    Candidate degree: 'Intermediate' (Type: intermediate, Field: Unknown)", "    Candidate degree: 'Matriculation' (Type: matriculation, Field: Unknown)", "  - Analyzing requirement: 'Ma'", "    Required degree type: master", "    Required field: Not specified", "    Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)", "    Candidate degree: 'Intermediate' (Type: intermediate, Field: Unknown)", "    Candidate degree: 'Matriculation' (Type: matriculation, Field: Unknown)", "  - Analyzing requirement: 'Ba'", "    Required degree type: bachelor", "    Required field: Not specified", "    Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)", "    ✓ EXACT MATCH FOUND: Degree type and field match", "Step 2: Applying binary scoring system", "  ✓ Education requirement met: Score = 10.0"], "_scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "_explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)"}}, "certifications": {"raw_score": 0.0, "weight": 0.5, "weighted_score": 0.0, "rationale": "No relevant certifications - 1 certifications but none match job requirements", "details": {"total_certifications": 1, "relevant_certifications": [], "relevant_count": 0, "all_certifications": ["Robotics Workshop , Newton School of Technology, Rishihood University ( Link ) January 2024: Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype."], "_irrelevant_certifications": ["Robotics Workshop , Newton School of Technology, Rishihood University ( Link ) January 2024: Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype."], "_certification_analysis": [{"certification": "Robotics Workshop , Newton School of Technology, Rishihood University ( Link ) January 2024: Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype.", "relevant": false, "matched_skills": [], "points_awarded": 0}], "_calculation_steps": ["Step 1: Found 1 certifications in resume", "Step 2: Checking relevance against 57 job skills (57 required + 0 preferred)", "  Cert 1: 'Robotics Workshop , Newton School of Technology, Rishihood University ( Link ) January 2024: Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype.' - NOT RELEVANT = +0 points", "Step 3: Score calculation", "  Base score: 0 relevant certs × 2 points = 0", "  Final score: min(10, 0) = 0"], "_scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "_explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10"}}, "location": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate location match - some geographic alignment", "details": {"jd_location": "", "resume_location": "", "experience_locations": [], "location_match_found": false, "has_location_info": false, "_calculation_steps": ["Step 1: Location extraction", "  Job location: '' (from JD)", "  Resume location: '' (from resume)", "  Experience locations: [] (from work history)", "Step 2: Location matching analysis", "  ~ Insufficient location data: Score = 5.0 (neutral)"], "_scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "_explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}}, "reliability": {"raw_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Moderate job stability - average 0.0 years per company", "details": {"candidate_yoe": 0, "num_companies": 1, "avg_tenure": 0.0, "has_experience_data": false, "_tenure_breakdown": [{"company": "Unknown Company", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}], "_calculation_steps": ["Step 1: Analyzing 1 experience entries for tenure calculation", "  Entry 1: Unknown Company (Present) = 0 years (could not parse)", "Step 1 Result: Total experience = 0 years", "Step 2: Calculating job stability/reliability", "  Total companies: 1", "  Total years: 0", "  ~ Insufficient data for calculation: Score = 5.0 (neutral)"], "_scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "_explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history"}}}}