
DETAILED CALCULATION BREAKDOWN
==============================
This file provides step-by-step explanations of how each score was calculated.

SKILLS SCORING CALCULATION
==========================
Formula: Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))
Explanation: Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)

Step-by-step calculation:
  Step 1: Required skills score = 2/3 × 8 = 5.33
  Step 2: No preferred skills specified, bonus = 0.00
  Step 3: Total score = min(10, 5.33 + 0.00) = 5.33


EXPERIENCE SCORING CALCULATION
==============================
Formula: Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x
Explanation: Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification

Step-by-step calculation:
  Step 1: No required experience specified in job description
  Step 2: Analyzing 0 experience entries
  Step 2 Result: Total candidate experience = 0 years
  Step 3: Calculating experience score
    ~ No experience requirement specified: Score = 5.0 (neutral)


EDUCATION SCORING CALCULATION
=============================
Formula: Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements
Explanation: Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)

Step-by-step calculation:
  Step 1: Checking 1 education requirement(s)
    - Analyzing requirement: 'Complete understanding of application development life cycle'
      Required degree type: Not specified
      Required field: Not specified
      Candidate degree: 'Intermediate' (Type: intermediate, Field: Unknown)
      ✗ SKIPPED: 'Complete understanding of application development life cycle' is not an education requirement (no degree-related terms found)
      ~ PARTIAL MATCH: Keyword similarity found
  Step 2: Applying binary scoring system
    ✗ Education requirements not met: Score = 0.0


CERTIFICATIONS SCORING CALCULATION
==================================
Formula: Score = min(10, relevant_certifications_count × 2)
Explanation: Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10

Step-by-step calculation:
  Step 1: Found 0 certifications in resume
  Step 2: Checking relevance against 3 job skills (3 required + 0 preferred)
  Step 3: Score calculation
    Base score: 0 relevant certs × 2 points = 0
    Final score: min(10, 0) = 0


LOCATION SCORING CALCULATION
============================
Formula: 10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data
Explanation: Location scoring prioritizes current location match, gives credit for previous work experience in the job location

Step-by-step calculation:
  Step 1: Location extraction
    Job location: '' (from JD)
    Resume location: '' (from resume)
    Experience locations: [] (from work history)
  Step 2: Location matching analysis
    ~ Insufficient location data: Score = 5.0 (neutral)


RELIABILITY SCORING CALCULATION
===============================
Formula: Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year
Explanation: Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history

Step-by-step calculation:
  Step 1: Analyzing 0 experience entries for tenure calculation
  Step 1 Result: Total experience = 0 years
  Step 2: Calculating job stability/reliability
    Total companies: 0
    Total years: 0
    ~ Insufficient data for calculation: Score = 5.0 (neutral)


FINAL CGPA CALCULATION
=====================
Formula: Final Score = (Sum of Weighted Scores) / (Sum of Weights)

Calculation:
  Skills: 5.33 × 3.0 = 16.00
  Experience: 5.00 × 2.5 = 12.50
  Education: 0.00 × 2.0 = 0.00
  Certifications: 0.00 × 1.0 = 0.00
  Location: 5.00 × 1.0 = 5.00
  Reliability: 5.00 × 0.5 = 2.50

  Total Weighted Score: 16.00 + 12.50 + 0.00 + 0.00 + 5.00 + 2.50 = 36.00
  Total Credits: 10.0

  Final Score: 36.00 / 10.0 = 3.60/10

CONCLUSION
==========
The candidate scored 3.60/10, which categorizes them as a "Weak Match".
