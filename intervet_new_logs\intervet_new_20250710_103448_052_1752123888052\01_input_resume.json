{"name": "<PERSON><PERSON>", "email": null, "phone": null, "education": [{"degree": "Bachelor of Technology (AI ML)", "institution": "Newton School of Technology, Rishihood University", "year": "2023 - 2027", "grade": "9.18/10.0"}, {"degree": "Intermediate", "institution": "Mother India Sr Sec School Marot, Jhajjar, Haryana", "year": "2021 - 2022", "grade": "90.0%"}, {"degree": "Matriculation", "institution": "R E D Sr Sec School Chhuchhakwas, Jhajjar, Haryana", "year": "2019 - 2020", "grade": "95.8%"}], "highest_education": "Bachelor of Technology (AI ML)", "skills": {"SQL": "Mentioned in resume", "Java": "Used in project: Zee5 Clone", "JavaScript": "Used in project: Zee5 Clone", "CSS": "Used in project: Zee5 Clone", "HTML": "Used in project: Zee5 Clone", "Python": "Mentioned in resume", "React": "Used in project: Zee5 Clone", "MySQL": "Mentioned in resume", "NodeJS": "Used in project: Tech Talks", "Prisma ORM": "Mentioned in resume", "Tailwind": "Mentioned in resume", "Data Structure": "Mentioned in resume", "Communication Skills": "Mentioned in resume", "Research": "Mentioned in resume", "Decision-making": "Mentioned in resume", "Team Building": "Mentioned in resume", "Leadership": "Mentioned in resume"}, "experience": [{"company_name": "Google Developer Groups (GDG) Rishihood University", "role": "Tech Lead", "duration": "Present", "key_responsibilities": "Led a team of developers in organizing and hosting tech events. Managed project timelines, resources, and member engagement. Facilitated workshops and training sessions on various technologies."}], "projects": [{"name": "Tech Talks", "description": "Developed \"Tech Talks\", a blogging site for tech content. Users can register, log in, create, access and comment on tech blogs. Tech Stack: React, NodeJS, HTML/CSS. Features: Authentication, blog management, categorized content, CRUD operations on Blogs."}, {"name": "Zee5 Clone", "description": "Created a Zee5 clone using React, HTML/CSS, and JavaScript. Users register, sign in securely, and access a library of movies with intuitive UI. Tech Stack: React, HTML/CSS, JavaScript. Features: Movie rendering, Authentication."}], "certifications": [], "domain_of_interest": [], "languages_known": [], "social_media": [], "achievements": [], "publications": [], "volunteer_experience": [], "references": [], "summary": null, "confidence_score": 0.59, "confidence_details": {}, "processing_time": 43.76831102371216, "extraction_method": "hybrid_regex_llm", "sections_extracted": 7, "regex_confidence": 0.9285714285714286}