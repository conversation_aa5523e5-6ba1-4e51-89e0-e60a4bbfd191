#!/usr/bin/env python3
"""
Enhanced GUI Application for Testing /intervet2 Endpoint
Features:
- Bulk resume upload
- Single JD upload
- Visual display of parsing results
- Comprehensive evaluation results
- Export functionality
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests
import json
import threading
import time
from pathlib import Path
import os
from typing import List, Dict, Any

class EnhancedIntervet2GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Intervet2 Testing GUI")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')
        
        # API Configuration
        self.api_base_url = "http://localhost:8000"
        self.intervet2_url = f"{self.api_base_url}/intervet2"
        
        # Data storage
        self.resume_files = []
        self.jd_file = None
        self.results = []
        
        # Create GUI
        self.create_widgets()
        
        # Check server status on startup
        self.check_server_status()
    
    def create_widgets(self):
        """Create the main GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Enhanced Intervet2 Testing GUI", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Server status
        self.server_status_label = ttk.Label(main_frame, text="Server Status: Checking...", 
                                           font=('Arial', 10))
        self.server_status_label.grid(row=1, column=0, columnspan=3, pady=(0, 10))
        
        # File upload section
        upload_frame = ttk.LabelFrame(main_frame, text="File Upload", padding="10")
        upload_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        upload_frame.columnconfigure(0, weight=1)
        
        # Resume upload
        ttk.Label(upload_frame, text="Resume Files:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        resume_button_frame = ttk.Frame(upload_frame)
        resume_button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        resume_button_frame.columnconfigure(1, weight=1)
        
        ttk.Button(resume_button_frame, text="Select Resume Files", 
                  command=self.select_resume_files).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(resume_button_frame, text="Clear Resumes", 
                  command=self.clear_resumes).grid(row=0, column=1, sticky=tk.W)
        
        # Resume list
        self.resume_listbox = tk.Listbox(upload_frame, height=6)
        self.resume_listbox.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # JD upload
        ttk.Label(upload_frame, text="Job Description:", font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        
        jd_button_frame = ttk.Frame(upload_frame)
        jd_button_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        jd_button_frame.columnconfigure(1, weight=1)
        
        ttk.Button(jd_button_frame, text="Select JD File", 
                  command=self.select_jd_file).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(jd_button_frame, text="Clear JD", 
                  command=self.clear_jd).grid(row=0, column=1, sticky=tk.W)
        
        self.jd_label = ttk.Label(upload_frame, text="No JD file selected", foreground='gray')
        self.jd_label.grid(row=5, column=0, sticky=tk.W, pady=(0, 10))
        
        # Processing controls
        control_frame = ttk.Frame(upload_frame)
        control_frame.grid(row=6, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        control_frame.columnconfigure(1, weight=1)
        
        self.process_button = ttk.Button(control_frame, text="Process All Files", 
                                       command=self.process_files, state='disabled')
        self.process_button.grid(row=0, column=0, padx=(0, 10))
        
        self.progress_bar = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(1, weight=1)
        
        # Results controls
        results_control_frame = ttk.Frame(results_frame)
        results_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        results_control_frame.columnconfigure(2, weight=1)
        
        ttk.Button(results_control_frame, text="Clear Results", 
                  command=self.clear_results).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(results_control_frame, text="Export Results", 
                  command=self.export_results).grid(row=0, column=1, padx=(0, 10))
        
        self.results_count_label = ttk.Label(results_control_frame, text="Results: 0")
        self.results_count_label.grid(row=0, column=2, sticky=tk.E)
        
        # Results display
        self.results_text = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, 
                                                     font=('Consolas', 9))
        self.results_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Status bar
        self.status_label = ttk.Label(main_frame, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def check_server_status(self):
        """Check if the API server is running"""
        def check():
            try:
                response = requests.get(f"{self.api_base_url}/", timeout=5)
                if response.status_code == 200:
                    self.server_status_label.config(text="Server Status: ✅ Online", foreground='green')
                    self.process_button.config(state='normal' if self.can_process() else 'disabled')
                else:
                    self.server_status_label.config(text="Server Status: ❌ Error", foreground='red')
            except Exception as e:
                self.server_status_label.config(text="Server Status: ❌ Offline", foreground='red')
                self.process_button.config(state='disabled')
        
        threading.Thread(target=check, daemon=True).start()
    
    def select_resume_files(self):
        """Select multiple resume files"""
        files = filedialog.askopenfilenames(
            title="Select Resume Files",
            filetypes=[("PDF files", "*.pdf"), ("Word files", "*.docx"), ("All files", "*.*")]
        )
        
        if files:
            self.resume_files = list(files)
            self.update_resume_list()
            self.update_process_button()
            self.update_status(f"Selected {len(files)} resume files")
    
    def clear_resumes(self):
        """Clear selected resume files"""
        self.resume_files = []
        self.update_resume_list()
        self.update_process_button()
        self.update_status("Cleared resume files")
    
    def update_resume_list(self):
        """Update the resume listbox"""
        self.resume_listbox.delete(0, tk.END)
        for file_path in self.resume_files:
            filename = Path(file_path).name
            self.resume_listbox.insert(tk.END, filename)
    
    def select_jd_file(self):
        """Select a job description file"""
        file = filedialog.askopenfilename(
            title="Select Job Description File",
            filetypes=[("PDF files", "*.pdf"), ("Word files", "*.docx"), ("All files", "*.*")]
        )
        
        if file:
            self.jd_file = file
            filename = Path(file).name
            self.jd_label.config(text=f"Selected: {filename}", foreground='black')
            self.update_process_button()
            self.update_status(f"Selected JD file: {filename}")
    
    def clear_jd(self):
        """Clear selected JD file"""
        self.jd_file = None
        self.jd_label.config(text="No JD file selected", foreground='gray')
        self.update_process_button()
        self.update_status("Cleared JD file")
    
    def can_process(self):
        """Check if we can start processing"""
        return len(self.resume_files) > 0 and self.jd_file is not None
    
    def update_process_button(self):
        """Update the process button state"""
        if self.can_process():
            self.process_button.config(state='normal')
        else:
            self.process_button.config(state='disabled')
    
    def update_status(self, message):
        """Update the status bar"""
        self.status_label.config(text=message)
    
    def process_files(self):
        """Process all selected files"""
        if not self.can_process():
            messagebox.showerror("Error", "Please select resume files and a JD file")
            return
        
        # Disable the process button and start progress bar
        self.process_button.config(state='disabled')
        self.progress_bar.start()
        
        # Start processing in a separate thread
        threading.Thread(target=self._process_files_thread, daemon=True).start()
    
    def _process_files_thread(self):
        """Process files in a separate thread"""
        try:
            self.results = []
            total_files = len(self.resume_files)
            
            for i, resume_file in enumerate(self.resume_files):
                self.update_status(f"Processing {i+1}/{total_files}: {Path(resume_file).name}")
                
                try:
                    result = self.process_single_file(resume_file, self.jd_file)
                    self.results.append({
                        'resume_file': Path(resume_file).name,
                        'jd_file': Path(self.jd_file).name,
                        'result': result,
                        'success': True,
                        'error': None
                    })
                except Exception as e:
                    self.results.append({
                        'resume_file': Path(resume_file).name,
                        'jd_file': Path(self.jd_file).name,
                        'result': None,
                        'success': False,
                        'error': str(e)
                    })
                
                # Update results display
                self.root.after(0, self.update_results_display)
            
            # Processing complete
            self.root.after(0, self._processing_complete)
            
        except Exception as e:
            self.root.after(0, lambda: self._processing_error(str(e)))
    
    def process_single_file(self, resume_file: str, jd_file: str) -> Dict[str, Any]:
        """Process a single resume-JD pair"""
        with open(resume_file, 'rb') as rf, open(jd_file, 'rb') as jf:
            files = {
                'resume_file': (Path(resume_file).name, rf, 
                              'application/pdf' if resume_file.endswith('.pdf') else 
                              'application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
                'jd_file': (Path(jd_file).name, jf,
                           'application/pdf' if jd_file.endswith('.pdf') else 
                           'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            }
            
            response = requests.post(self.intervet2_url, files=files, timeout=300)
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"API Error {response.status_code}: {response.text}")
    
    def _processing_complete(self):
        """Called when processing is complete"""
        self.progress_bar.stop()
        self.process_button.config(state='normal')
        
        successful = sum(1 for r in self.results if r['success'])
        failed = len(self.results) - successful
        
        self.update_status(f"Processing complete: {successful} successful, {failed} failed")
        self.update_results_display()
        
        if failed > 0:
            messagebox.showwarning("Processing Complete", 
                                 f"Processing complete with {failed} failures. Check results for details.")
        else:
            messagebox.showinfo("Processing Complete", 
                              f"All {successful} files processed successfully!")
    
    def _processing_error(self, error_message):
        """Called when processing encounters an error"""
        self.progress_bar.stop()
        self.process_button.config(state='normal')
        self.update_status(f"Processing failed: {error_message}")
        messagebox.showerror("Processing Error", f"Processing failed: {error_message}")
    
    def update_results_display(self):
        """Update the results display"""
        self.results_text.delete(1.0, tk.END)
        self.results_count_label.config(text=f"Results: {len(self.results)}")
        
        if not self.results:
            self.results_text.insert(tk.END, "No results yet. Process some files to see results here.")
            return
        
        for i, result_data in enumerate(self.results):
            self.results_text.insert(tk.END, f"{'='*80}\n")
            self.results_text.insert(tk.END, f"RESULT {i+1}: {result_data['resume_file']} vs {result_data['jd_file']}\n")
            self.results_text.insert(tk.END, f"{'='*80}\n\n")
            
            if not result_data['success']:
                self.results_text.insert(tk.END, f"❌ ERROR: {result_data['error']}\n\n")
                continue
            
            result = result_data['result']
            
            # Display evaluation summary
            eval_result = result.get('evaluation_result', {})
            self.results_text.insert(tk.END, f"🎯 EVALUATION SUMMARY:\n")
            self.results_text.insert(tk.END, f"   Final Score: {eval_result.get('total_score', 0):.2f}/10\n")
            self.results_text.insert(tk.END, f"   Fit Category: {eval_result.get('fit_category', 'Unknown')}\n")
            self.results_text.insert(tk.END, f"   Summary: {eval_result.get('summary', 'No summary')}\n\n")
            
            # Display processing times
            processing_times = result.get('processing_times', {})
            self.results_text.insert(tk.END, f"⏱️  PROCESSING TIMES:\n")
            for step, time_taken in processing_times.items():
                self.results_text.insert(tk.END, f"   {step}: {time_taken:.2f}s\n")
            self.results_text.insert(tk.END, f"   Total: {result.get('total_processing_time', 0):.2f}s\n\n")
            
            # Display resume parsing results
            resume_data = result.get('resume_data', {})
            self.results_text.insert(tk.END, f"📋 RESUME PARSING:\n")
            self.results_text.insert(tk.END, f"   Name: {resume_data.get('name', 'Unknown')}\n")
            self.results_text.insert(tk.END, f"   Email: {resume_data.get('email', 'Not found')}\n")
            self.results_text.insert(tk.END, f"   Skills: {len(resume_data.get('skills', []))}\n")
            self.results_text.insert(tk.END, f"   Experience: {len(resume_data.get('experience', []))}\n")
            self.results_text.insert(tk.END, f"   Education: {len(resume_data.get('education', []))}\n\n")
            
            # Display JD parsing results
            jd_data = result.get('jd_data', {})
            self.results_text.insert(tk.END, f"📋 JD PARSING:\n")
            self.results_text.insert(tk.END, f"   Job Title: {jd_data.get('job_title', 'Unknown')}\n")
            self.results_text.insert(tk.END, f"   Company: {jd_data.get('company_name', 'Not specified')}\n")
            self.results_text.insert(tk.END, f"   Required Skills: {len(jd_data.get('required_skills', []))}\n")
            self.results_text.insert(tk.END, f"   Preferred Skills: {len(jd_data.get('preferred_skills', []))}\n")
            self.results_text.insert(tk.END, f"   Required Experience: {jd_data.get('required_experience', 'Not specified')}\n\n")
        
        # Scroll to the end
        self.results_text.see(tk.END)
    
    def clear_results(self):
        """Clear all results"""
        self.results = []
        self.update_results_display()
        self.update_status("Results cleared")
    
    def export_results(self):
        """Export results to a JSON file"""
        if not self.results:
            messagebox.showwarning("No Results", "No results to export")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="Export Results",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.results, f, indent=2, ensure_ascii=False)
                
                self.update_status(f"Results exported to: {file_path}")
                messagebox.showinfo("Export Complete", f"Results exported successfully to:\n{file_path}")
                
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export results:\n{str(e)}")

def main():
    """Main function to run the GUI"""
    root = tk.Tk()
    app = EnhancedIntervet2GUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
