@echo off
echo Resume Parser GUI Launcher
echo ========================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python is available

REM Check if required files exist
if not exist "resume_parser_gui.py" (
    echo Error: resume_parser_gui.py not found
    echo Make sure all files are in the same directory
    pause
    exit /b 1
)

if not exist "gui_requirements.txt" (
    echo Error: gui_requirements.txt not found
    echo Make sure all files are in the same directory
    pause
    exit /b 1
)

echo Checking dependencies...
python launch_gui.py

pause
