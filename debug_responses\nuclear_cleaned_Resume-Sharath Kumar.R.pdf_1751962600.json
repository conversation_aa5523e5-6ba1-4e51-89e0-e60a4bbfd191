{
    "name": "<PERSON><PERSON><PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Science (Data Science)",
            "institution": "Sarada Vilas College",
            "year": "2021 - 2025",
            "grade": "9.0/10.0"
        },
        {
            "degree": "Intermediate",
            "institution": "Soundarya Composite PU College",
            "year": "2018 - 2020",
            "grade": "71.33%"
        },
        {
            "degree": "Matriculation",
            "institution": "Guru Shree Vidya Kendra",
            "year": "2008 - 2018",
            "grade": "83.68%"
        }
    ],
    "highest_education": "Bachelor of Science (Data Science)",
    "skills": [
        "C++",
        "Python",
        "Machine Learning",
        "Power BI",
        "MySQL",
        "Excel",
        "Matplotlib",
        "Pandas",
        "Data Structure",
        "Communication Skills",
        "Decision-making",
        "Afinalytics",
        "Spreadsheet",
        "Microsoft Office"
    ],
    "experience": [
        {
            "company_name": "Think Beyond Mysore",
            "role": "Assistant Robotic Intern",
            "duration": "April 2024 - October 2024",
            "key_responsibilities": "Supported the development of IoT-based projects and final-year academic projects for various colleges, and assisted in designing and implementing IoT solutions for real-world applications. Provided technical guidance and collaborated with students and teams of several colleges that ensured project success around 80%. Conducted testing and troubleshooting of IoT devices and systems, thus documented project details and outcomes for reference and knowledge sharing."
        }
    ],
    "projects": [
        {
            "name": "IPL RCB Stratergy",
            "description": "Evaluated IPL players based on key performance metrics such as batting average, strike rate, economy rate, and wickets. Utilized SQL queries to retrieve, clean, and preprocess IPL player data for afinalysis. Applied ranking functions and conditiofinal filtering to identify undervalued players. Provided data-driven recommendations for optimal batting order and bowling combinations."
        },
        {
            "name": "IT Ticket Afinalysis",
            "description": "Conducted data afinalysis using Excel to assess IT agent performance and identify high and low performers. Utilized Pivot Tables, Charts, and Slicers to evaluate ticket resolution eciency and detect bottlenecks. Afinalyzed employee satisfaction ratings and workload distribution through data visualization. Provided actionable insights for hiring, training, and resource allocation to enhance service quality. Recommended process optimizations to improve overall IT support eciency. Identied workload imbalances and  ineciencies, supporting data-driven decisions for IT support improvements."
        }
    ],
    "certifications": [
        "Professiofinal Certicate in Data Science , Newton School September 2024"
    ],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Enthusiastic about problem-solving and data-driven decision-making, leveraging tools like Excel, SQL, and Power BI to optimize processes. Proficient in data afinalysis and database management, with a strong ability to transform raw data into actionable insights. Eager to apply technical expertise and afinalytical skills in a dynamic team, driving impactful outcomes and contributing to organizatiofinal success.",
    "persofinal_projects": [],
    "social_media": [
        "LinkedIn",
        "Github"
    ]
}