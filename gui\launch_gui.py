#!/usr/bin/env python3
"""
Simple launcher for Resume Parser GUI
Checks dependencies and launches the application
"""

import sys
import subprocess
import importlib.util

def check_dependency(module_name, install_name=None):
    """Check if a module is installed"""
    if install_name is None:
        install_name = module_name
    
    spec = importlib.util.find_spec(module_name)
    if spec is None:
        print(f"❌ {module_name} is not installed")
        print(f"   Install with: pip install {install_name}")
        return False
    else:
        print(f"✅ {module_name} is available")
        return True

def check_tkinter():
    """Check if tkinter is available"""
    try:
        import tkinter
        print("✅ tkinter is available")
        return True
    except ImportError:
        print("❌ tkinter is not available")
        print("   Install with:")
        print("   - Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   - macOS: brew install python-tk")
        print("   - Windows: tkinter should be included with Python")
        return False

def main():
    print("Resume Parser GUI - Dependency Check")
    print("=" * 40)
    
    # Check required dependencies
    required_deps = [
        ("requests", "requests"),
        ("pyperclip", "pyperclip"),
    ]
    
    # Check optional dependencies
    optional_deps = [
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
    ]
    
    all_required_ok = True
    
    # Check tkinter first
    if not check_tkinter():
        all_required_ok = False
    
    # Check required dependencies
    print("\nRequired Dependencies:")
    for module, install in required_deps:
        if not check_dependency(module, install):
            all_required_ok = False
    
    # Check optional dependencies
    print("\nOptional Dependencies (for enhanced features):")
    for module, install in optional_deps:
        check_dependency(module, install)
    
    print("\n" + "=" * 40)
    
    if all_required_ok:
        print("✅ All required dependencies are available!")
        print("🚀 Launching Resume Parser GUI...")
        
        try:
            from resume_parser_gui import main as gui_main
            gui_main()
        except ImportError as e:
            print(f"❌ Error importing GUI: {e}")
            print("Make sure resume_parser_gui.py is in the same directory")
        except Exception as e:
            print(f"❌ Error launching GUI: {e}")
    else:
        print("❌ Some required dependencies are missing.")
        print("Please install them and try again.")
        print("\nQuick install command:")
        print("pip install -r gui_requirements.txt")

if __name__ == "__main__":
    main()
