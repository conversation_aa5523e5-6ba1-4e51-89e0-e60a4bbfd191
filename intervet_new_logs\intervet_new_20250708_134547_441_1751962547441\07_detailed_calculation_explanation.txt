
DETAILED CALCULATION BREAKDOWN
==============================
This file provides step-by-step explanations of how each score was calculated.

SKILLS SCORING CALCULATION
==========================
Formula: Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))
Explanation: Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)

Step-by-step calculation:
  Step 1: Required skills score = 3/5 × 8 = 4.80
  Step 2: Preferred skills bonus = 1/1 × 2 = 2.00
  Step 3: Total score = min(10, 4.80 + 2.00) = 6.80


EXPERIENCE SCORING CALCULATION
==============================
Formula: Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x
Explanation: Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification

Step-by-step calculation:
  Step 1: No required experience specified in job description
  Step 2: Analyzing 1 experience entries
    Entry 1: Unknown Company - Unknown Position (Present) = 0 years (could not parse)
  Step 2 Result: Total candidate experience = 0 years
  Step 3: Calculating experience score
    ~ No experience requirement specified: Score = 5.0 (neutral)


EDUCATION SCORING CALCULATION
=============================
Formula: Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements
Explanation: Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)

Step-by-step calculation:
  Step 1: Checking 1 education requirement(s)
    - Analyzing requirement: 'Bachelor's in IT, Computer Science, Software Engineering, or a related field'
      Required degree type: bachelor
      Required field: computer science
      Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)
      ✓ EXACT MATCH FOUND: Degree type and field match
  Step 2: Applying binary scoring system
    ✓ Education requirement met: Score = 10.0


CERTIFICATIONS SCORING CALCULATION
==================================
Formula: Score = min(10, relevant_certifications_count × 2)
Explanation: Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10

Step-by-step calculation:
  Step 1: Found 1 certifications in resume
  Step 2: Checking relevance against 6 job skills (5 required + 1 preferred)
    Cert 1: 'Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024: Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype.' - NOT RELEVANT = +0 points
  Step 3: Score calculation
    Base score: 0 relevant certs × 2 points = 0
    Final score: min(10, 0) = 0


LOCATION SCORING CALCULATION
============================
Formula: 10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data
Explanation: Location scoring prioritizes current location match, gives credit for previous work experience in the job location

Step-by-step calculation:
  Step 1: Location extraction
    Job location: 'san francisco, ca' (from JD)
    Resume location: '' (from resume)
    Experience locations: [] (from work history)
  Step 2: Location matching analysis
    ~ Insufficient location data: Score = 5.0 (neutral)


RELIABILITY SCORING CALCULATION
===============================
Formula: Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year
Explanation: Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history

Step-by-step calculation:
  Step 1: Analyzing 1 experience entries for tenure calculation
    Entry 1: Unknown Company (Present) = 0 years (could not parse)
  Step 1 Result: Total experience = 0 years
  Step 2: Calculating job stability/reliability
    Total companies: 1
    Total years: 0
    ~ Insufficient data for calculation: Score = 5.0 (neutral)


FINAL CGPA CALCULATION
=====================
Formula: Final Score = (Sum of Weighted Scores) / (Sum of Weights)

Calculation:
  Skills: 6.80 × 3.0 = 20.40
  Experience: 5.00 × 2.5 = 12.50
  Education: 10.00 × 2.0 = 20.00
  Certifications: 0.00 × 1.0 = 0.00
  Location: 5.00 × 1.0 = 5.00
  Reliability: 5.00 × 0.5 = 2.50

  Total Weighted Score: 20.40 + 12.50 + 20.00 + 0.00 + 5.00 + 2.50 = 60.40
  Total Credits: 10.0

  Final Score: 60.40 / 10.0 = 6.04/10

CONCLUSION
==========
The candidate scored 6.04/10, which categorizes them as a "Good Match".
