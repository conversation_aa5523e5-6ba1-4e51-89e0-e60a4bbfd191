# Extracted Text Debug File
# Source File: Gaja_Prasanth Resume.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-07-14 19:08:13
# Text Length: 2617 characters
# ================================================

V Gaja Prasanth
Software Developer
Chennai,Tamil Nadu ·
<EMAIL> ·9080540705 ·Github: Gaja1595
LinkedIn: Gaja Prasanth ·
Summary
• Passionate developer focused on building user-friendly artificial intelligence applications, driven
to deliver innovative and impactful solutions.
EDUCATION
•B.Tech: CSE (Artificial Intelligence And Machine Learning) (2021-2025)
Sri Ramachandra Engineering And Technology - Porur, India
CGPA: [9.0]
Work History
•Student Intern, SRET JUN ’22 - JUL ’22
I implemented a leaf disease detection project using VGG19, employing data augmentation and
transfer learning to enhance model performance.
•Student Intern, Qwings IIT Madras RP AUG ’23 - OCT ’23
Integrated multiple machine learning projects into website using Flask for seamless model de-
ployment and user interaction.
•Web Development Instructor, Qwings IIT Madras RP MAY ’24 - JUN ’24
Taught fundamental web development concepts to children, focusing on HTML and CSS.
Received positive feedback from students and parents, contributing to the company’s community
outreach and educational initiatives.
•AI Intern, Novaautomata MARCH ’25 -
Working in this startup company has given me exposure to multiple new tools and technologies
to integrate with ai models.
-Developed an AI-powered Resume Screening System using FastAPI and multiple LLMs. It
parses resumes, matches them with job descriptions, and returns structured JSON insights
with NLP-based smart filtering and scoring.
-Built a multimodal Interview Persona Chatbot using self-hosted open-source LLMs and Kokoro
TTS, enabling voice-based mock interviews with dynamic question generation and real-time
audio responses.
PROJECTS
•Leaf Disease Detection
I developed a leaf disease detection model using VGG19, achieving high accuracy in classifying
plant diseases for improved agricultural monitoring.
•Neural Style Transfer
I implemented Arbitrary Neural Style Transfer using TensorFlow Hub’s model to apply diverse
artistic styles to images.
•Medical Transcript Classification
I developed a medical transcript classification system using N-grams and various supervised
machine learning algorithms, achieving accurate categorization of clinical texts.
SKILLS
•Hard Skills
Programming & Scripting: Python, SQL
Machine Learning & AI: Machine Learning, Deep Learning, NLP , Text Analytics, Hugging Face,
LLM Integration, Prompt Engineering
Frameworks & Tools: FastAPI, Kokoro TTS, Ollama, REST APIs
Deployment & DevOps: AWS (EC2, Nginx), Model Deployment
•Soft Skills
Effective Communication, Adaptability, Attention to Detail, Creative Thinking, Team Collabora-
tion
