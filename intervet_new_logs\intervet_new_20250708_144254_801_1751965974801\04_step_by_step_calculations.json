{"timestamp": "20250708_144254_801", "calculation_transparency": "This file contains detailed step-by-step calculations for each scoring field", "field_calculations": {"skills": {"calculation_steps": ["Step 1: Required skills score = 1/16 × 8 = 0.50", "Step 2: No preferred skills specified, bonus = 0.00", "Step 3: Total score = min(10, 0.50 + 0.00) = 0.50"], "scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)", "matching_details": []}, "experience": {"calculation_steps": ["Step 1: Could not extract numeric experience from 'Three (3) years of professional-level experience in data mining/analysis, including conducting large-scale data analyses to evaluate and/or present recommendations to innovate processes or resolve issues.'", "Step 2: Analyzing 1 experience entries", "  Entry 1: Unknown Company - Unknown Position (Present) = 0 years (could not parse)", "Step 2 Result: Total candidate experience = 0 years", "Step 3: Calculating experience score", "  ~ No experience requirement specified: Score = 5.0 (neutral)"], "scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification", "experience_breakdown": [{"company": "Unknown Company", "position": "Unknown Position", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}]}, "education": {"calculation_steps": ["Step 1: Checking 1 education requirement(s)", "  - Analyzing requirement: 'A master’s degree from an accredited college or university in computer science, mathematics, statistics, analytics, economics, or other closely related field.'", "    Required degree type: master", "    Required field: computer science", "    Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)", "    ~ PARTIAL MATCH: Same field (computer science) but different degree level", "    Candidate degree: 'Intermediate' (Type: intermediate, Field: Unknown)", "    Candidate degree: 'Matriculation' (Type: matriculation, Field: Unknown)", "Step 2: Applying binary scoring system", "  ~ Partial education match (benefit of doubt): Score = 6.0"], "scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)", "match_type": "field_match"}, "certifications": {"calculation_steps": ["Step 1: Found 1 certifications in resume", "Step 2: Checking relevance against 16 job skills (16 required + 0 preferred)", "  Cert 1: 'Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024. Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype.' - NOT RELEVANT = +0 points", "Step 3: Score calculation", "  Base score: 0 relevant certs × 2 points = 0", "  Final score: min(10, 0) = 0"], "scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10", "certification_analysis": [{"certification": "Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024. Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functiofinal gesture-controlled wheelchair prototype.", "relevant": false, "matched_skills": [], "points_awarded": 0}]}, "location": {"calculation_steps": ["Step 1: Location extraction", "  Job location: 'san diego, ca' (from JD)", "  Resume location: '' (from resume)", "  Experience locations: [] (from work history)", "Step 2: Location matching analysis", "  ~ Insufficient location data: Score = 5.0 (neutral)"], "scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}, "reliability": {"calculation_steps": ["Step 1: Analyzing 1 experience entries for tenure calculation", "  Entry 1: Unknown Company (Present) = 0 years (could not parse)", "Step 1 Result: Total experience = 0 years", "Step 2: Calculating job stability/reliability", "  Total companies: 1", "  Total years: 0", "  ~ Insufficient data for calculation: Score = 5.0 (neutral)"], "scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history", "tenure_breakdown": [{"company": "Unknown Company", "duration": "Present", "years_calculated": 0, "calculation_method": "Could not parse duration"}]}}, "final_calculation": {"formula": "Final Score = (Sum of Weighted Scores) / (Sum of Weights)", "calculation": "(2.00 + 15.00 + 12.00 + 0.00 + 2.50 + 0.00) / 10.0", "result": "3.15/10"}}