{"total_score": 3.6, "fit_category": "Weak Match", "summary": "The candidate is a weak match for this position with a CGPA-style score of 3.6/10. Areas for improvement: Education, Certifications.", "skills_score": {"raw_score": 5.333333333333333, "weight": 3.0, "weighted_score": 16.0, "rationale": "Moderate skills match - has 2/3 required skills, significant skill gaps"}, "experience_score": {"raw_score": 5.0, "weight": 2.5, "weighted_score": 12.5, "rationale": "Moderate experience level - 0 years, some experience gaps"}, "education_score": {"raw_score": 0.0, "weight": 2.0, "weighted_score": 0.0, "rationale": "Education requirements not met - lacks required degree"}, "certifications_score": {"raw_score": 0.0, "weight": 1.0, "weighted_score": 0.0, "rationale": "No certifications listed"}, "location_score": {"raw_score": 5.0, "weight": 1.0, "weighted_score": 5.0, "rationale": "Moderate location match - some geographic alignment"}, "reliability_score": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate job stability - average 0.0 years per company"}, "detailed_rationale": {"skills_match_direct": "Matched 2/3 required skills. Matched required skills: HTML, CSS. Missing required skills: JavaScript", "experience_match": "No specific experience requirement found in job description", "reliability": "Could not calculate job stability due to missing experience data", "location_match": "Location information not available for comparison", "academic_match": "Education requirements not met. Candidate: Intermediate, Required: Complete understanding of application development life cycle", "alma_mater": "No top-ranked universities found in education history", "certifications": "No relevant certifications found"}, "total_credits_used": 10.0, "calculation_method": "CGPA", "processing_time": 0.0025017261505126953}