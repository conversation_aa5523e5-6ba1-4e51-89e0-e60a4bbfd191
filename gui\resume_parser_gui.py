#!/usr/bin/env python3
"""
Resume Parser GUI - Lightweight Tkinter Application
Uploads resumes to Gemma API and displays parsed results
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests
import json
import os
import threading
from datetime import datetime
import pyperclip
import webbrowser
from pathlib import Path

class ResumeParserGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Resume Parser - Gemma API Client")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Configuration
        self.api_base_url = "http://localhost:8000"  # Default to localhost
        self.last_result = None
        self.processing = False
        
        # Create GUI
        self.create_widgets()
        self.load_settings()
        
    def create_widgets(self):
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Resume Parser", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # API URL Configuration
        ttk.Label(main_frame, text="API URL:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.api_url_var = tk.StringVar(value=self.api_base_url)
        api_url_entry = ttk.Entry(main_frame, textvariable=self.api_url_var, width=50)
        api_url_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        test_btn = ttk.Button(main_frame, text="Test Connection", 
                             command=self.test_connection)
        test_btn.grid(row=1, column=2, pady=5, padx=(5, 0))
        
        # File Selection
        ttk.Label(main_frame, text="Resume File:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(main_frame, textvariable=self.file_path_var, width=50)
        file_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        browse_btn = ttk.Button(main_frame, text="Browse", command=self.browse_file)
        browse_btn.grid(row=2, column=2, pady=5, padx=(5, 0))
        
        # Options Frame
        options_frame = ttk.LabelFrame(main_frame, text="Options", padding="5")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        options_frame.columnconfigure(1, weight=1)
        
        # Skills format option
        self.skills_dict_var = tk.BooleanVar(value=True)
        skills_check = ttk.Checkbutton(options_frame, 
                                      text="Convert skills to dictionary format",
                                      variable=self.skills_dict_var)
        skills_check.grid(row=0, column=0, sticky=tk.W, pady=2)
        
        # Auto-copy option
        self.auto_copy_var = tk.BooleanVar(value=True)
        copy_check = ttk.Checkbutton(options_frame, 
                                    text="Auto-copy result to clipboard",
                                    variable=self.auto_copy_var)
        copy_check.grid(row=0, column=1, sticky=tk.W, pady=2)
        
        # Process Button
        self.process_btn = ttk.Button(main_frame, text="Parse Resume", 
                                     command=self.process_resume, style="Accent.TButton")
        self.process_btn.grid(row=3, column=2, pady=10, padx=(5, 0))
        
        # Progress Bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Results Area
        results_frame = ttk.LabelFrame(main_frame, text="Parsed Results", padding="5")
        results_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Text area with scrollbar
        self.results_text = scrolledtext.ScrolledText(results_frame, height=15, width=80,
                                                     font=("Consolas", 10))
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Buttons Frame
        buttons_frame = ttk.Frame(results_frame)
        buttons_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # Action Buttons
        ttk.Button(buttons_frame, text="Copy All", 
                  command=self.copy_all_results).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Copy JSON", 
                  command=self.copy_json_only).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Save to File", 
                  command=self.save_results).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Clear", 
                  command=self.clear_results).pack(side=tk.LEFT, padx=(0, 5))
        
        # Export buttons
        ttk.Separator(buttons_frame, orient='vertical').pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(buttons_frame, text="Export CSV",
                  command=self.export_csv).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Export Excel",
                  command=self.export_excel).pack(side=tk.LEFT, padx=(0, 5))

        # Innovative features
        ttk.Separator(buttons_frame, orient='vertical').pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(buttons_frame, text="Batch Process",
                  command=self.batch_process).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Compare Resumes",
                  command=self.compare_resumes).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Skills Analysis",
                  command=self.analyze_skills).pack(side=tk.LEFT, padx=(0, 5))
        
        # Status Bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))
        
    def browse_file(self):
        """Open file dialog to select resume file"""
        file_types = [
            ("All Supported", "*.pdf;*.docx;*.doc;*.txt"),
            ("PDF files", "*.pdf"),
            ("Word documents", "*.docx;*.doc"),
            ("Text files", "*.txt"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Select Resume File",
            filetypes=file_types
        )
        
        if filename:
            self.file_path_var.set(filename)
            self.status_var.set(f"Selected: {os.path.basename(filename)}")
    
    def test_connection(self):
        """Test connection to the API"""
        def test():
            try:
                self.status_var.set("Testing connection...")
                url = self.api_url_var.get().rstrip('/')
                response = requests.get(f"{url}/", timeout=10)
                
                if response.status_code == 200:
                    self.status_var.set("✓ Connection successful")
                    messagebox.showinfo("Success", "API connection successful!")
                else:
                    self.status_var.set(f"✗ Connection failed: {response.status_code}")
                    messagebox.showerror("Error", f"API returned status code: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                self.status_var.set("✗ Connection failed: Cannot reach API")
                messagebox.showerror("Error", "Cannot connect to API. Check if the server is running.")
            except Exception as e:
                self.status_var.set(f"✗ Connection failed: {str(e)}")
                messagebox.showerror("Error", f"Connection test failed: {str(e)}")
        
        threading.Thread(target=test, daemon=True).start()
    
    def process_resume(self):
        """Process the selected resume file"""
        if self.processing:
            return
            
        file_path = self.file_path_var.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("Error", "Please select a valid resume file")
            return
        
        self.processing = True
        self.process_btn.config(state='disabled')
        self.progress.start()
        
        def process():
            try:
                self.status_var.set("Processing resume...")
                
                # Prepare the request
                url = self.api_url_var.get().rstrip('/') + '/resume'
                
                with open(file_path, 'rb') as file:
                    files = {'file': file}
                    data = {
                        'convert_skills_to_dict_format': str(self.skills_dict_var.get()).lower()
                    }
                    
                    response = requests.post(url, files=files, data=data, timeout=120)
                
                if response.status_code == 200:
                    result = response.json()
                    self.display_results(result, file_path)
                    self.status_var.set("✓ Resume processed successfully")
                    
                    if self.auto_copy_var.get():
                        self.copy_json_only()
                        
                else:
                    error_msg = f"API Error {response.status_code}: {response.text}"
                    self.results_text.delete(1.0, tk.END)
                    self.results_text.insert(tk.END, error_msg)
                    self.status_var.set(f"✗ Processing failed: {response.status_code}")
                    
            except Exception as e:
                error_msg = f"Error processing resume: {str(e)}"
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, error_msg)
                self.status_var.set(f"✗ Error: {str(e)}")
                
            finally:
                self.processing = False
                self.process_btn.config(state='normal')
                self.progress.stop()
        
        threading.Thread(target=process, daemon=True).start()
    
    def display_results(self, result, file_path):
        """Display the parsed results in the text area"""
        self.last_result = result
        
        # Clear previous results
        self.results_text.delete(1.0, tk.END)
        
        # Add header
        header = f"Resume Analysis Results\n"
        header += f"File: {os.path.basename(file_path)}\n"
        header += f"Processed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        header += "=" * 60 + "\n\n"
        
        self.results_text.insert(tk.END, header)
        
        # Add formatted JSON
        formatted_json = json.dumps(result, indent=2, ensure_ascii=False)
        self.results_text.insert(tk.END, formatted_json)
        
        # Scroll to top
        self.results_text.see(1.0)
    
    def copy_all_results(self):
        """Copy all results to clipboard"""
        content = self.results_text.get(1.0, tk.END).strip()
        if content:
            pyperclip.copy(content)
            self.status_var.set("✓ All results copied to clipboard")
        else:
            messagebox.showwarning("Warning", "No results to copy")
    
    def copy_json_only(self):
        """Copy only the JSON data to clipboard"""
        if self.last_result:
            json_str = json.dumps(self.last_result, indent=2, ensure_ascii=False)
            pyperclip.copy(json_str)
            self.status_var.set("✓ JSON data copied to clipboard")
        else:
            messagebox.showwarning("Warning", "No JSON data to copy")
    
    def save_results(self):
        """Save results to a file"""
        if not self.last_result:
            messagebox.showwarning("Warning", "No results to save")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Save Results",
            defaultextension=".json",
            filetypes=[
                ("JSON files", "*.json"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        
        if filename:
            try:
                content = self.results_text.get(1.0, tk.END).strip()
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.status_var.set(f"✓ Results saved to {os.path.basename(filename)}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {str(e)}")
    
    def clear_results(self):
        """Clear the results area"""
        self.results_text.delete(1.0, tk.END)
        self.last_result = None
        self.status_var.set("Results cleared")
    
    def export_csv(self):
        """Export results to CSV format"""
        if not self.last_result:
            messagebox.showwarning("Warning", "No results to export")
            return
        
        try:
            import csv
            import io
            
            filename = filedialog.asksaveasfilename(
                title="Export to CSV",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv")]
            )
            
            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # Write header
                    writer.writerow(['Field', 'Value'])
                    
                    # Flatten the JSON and write rows
                    def flatten_dict(d, parent_key='', sep='_'):
                        items = []
                        for k, v in d.items():
                            new_key = f"{parent_key}{sep}{k}" if parent_key else k
                            if isinstance(v, dict):
                                items.extend(flatten_dict(v, new_key, sep=sep).items())
                            elif isinstance(v, list):
                                items.append((new_key, '; '.join(map(str, v))))
                            else:
                                items.append((new_key, str(v) if v is not None else ''))
                        return dict(items)
                    
                    flat_data = flatten_dict(self.last_result)
                    for key, value in flat_data.items():
                        writer.writerow([key, value])
                
                self.status_var.set(f"✓ Exported to {os.path.basename(filename)}")
                
        except ImportError:
            messagebox.showerror("Error", "CSV export requires Python csv module")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export CSV: {str(e)}")
    
    def export_excel(self):
        """Export results to Excel format"""
        if not self.last_result:
            messagebox.showwarning("Warning", "No results to export")
            return
        
        try:
            import pandas as pd
            
            filename = filedialog.asksaveasfilename(
                title="Export to Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )
            
            if filename:
                # Convert to DataFrame
                df = pd.json_normalize(self.last_result)
                df.to_excel(filename, index=False)
                self.status_var.set(f"✓ Exported to {os.path.basename(filename)}")
                
        except ImportError:
            messagebox.showinfo("Info", 
                               "Excel export requires pandas and openpyxl.\n"
                               "Install with: pip install pandas openpyxl")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export Excel: {str(e)}")
    
    def load_settings(self):
        """Load settings from config file"""
        try:
            config_file = "resume_parser_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    self.api_url_var.set(config.get('api_url', self.api_base_url))
                    self.skills_dict_var.set(config.get('skills_dict_format', True))
                    self.auto_copy_var.set(config.get('auto_copy', True))
        except:
            pass  # Use defaults if config loading fails
    
    def save_settings(self):
        """Save settings to config file"""
        try:
            config = {
                'api_url': self.api_url_var.get(),
                'skills_dict_format': self.skills_dict_var.get(),
                'auto_copy': self.auto_copy_var.get()
            }
            with open("resume_parser_config.json", 'w') as f:
                json.dump(config, f, indent=2)
        except:
            pass  # Ignore save errors

    def batch_process(self):
        """Process multiple resume files at once"""
        files = filedialog.askopenfilenames(
            title="Select Multiple Resume Files",
            filetypes=[
                ("All Supported", "*.pdf;*.docx;*.doc;*.txt"),
                ("PDF files", "*.pdf"),
                ("Word documents", "*.docx;*.doc"),
                ("Text files", "*.txt")
            ]
        )

        if not files:
            return

        # Create batch processing window
        batch_window = tk.Toplevel(self.root)
        batch_window.title("Batch Processing")
        batch_window.geometry("600x400")

        # Progress frame
        progress_frame = ttk.Frame(batch_window, padding="10")
        progress_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(progress_frame, text=f"Processing {len(files)} files...",
                 font=("Arial", 12, "bold")).pack(pady=(0, 10))

        # Progress bar
        batch_progress = ttk.Progressbar(progress_frame, mode='determinate',
                                        maximum=len(files))
        batch_progress.pack(fill=tk.X, pady=(0, 10))

        # Results list
        results_frame = ttk.Frame(progress_frame)
        results_frame.pack(fill=tk.BOTH, expand=True)

        # Treeview for results
        columns = ('File', 'Status', 'Name', 'Email', 'Skills Count')
        tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100)

        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Export button
        export_btn = ttk.Button(progress_frame, text="Export All Results",
                               state='disabled')
        export_btn.pack(pady=(10, 0))

        # Process files in background
        def process_batch():
            results = []
            for i, file_path in enumerate(files):
                try:
                    batch_progress['value'] = i
                    batch_window.update()

                    # Process file
                    url = self.api_url_var.get().rstrip('/') + '/resume'
                    with open(file_path, 'rb') as file:
                        files_data = {'file': file}
                        data = {'convert_skills_to_dict_format': 'true'}
                        response = requests.post(url, files=files_data, data=data, timeout=120)

                    if response.status_code == 200:
                        result = response.json()
                        results.append(result)

                        # Extract key info
                        name = result.get('name', 'N/A')
                        email = result.get('email', 'N/A')
                        skills = result.get('skills', [])
                        skills_count = len(skills) if isinstance(skills, list) else 'N/A'

                        tree.insert('', tk.END, values=(
                            os.path.basename(file_path), 'Success', name, email, skills_count
                        ))
                    else:
                        tree.insert('', tk.END, values=(
                            os.path.basename(file_path), f'Error {response.status_code}',
                            'N/A', 'N/A', 'N/A'
                        ))

                except Exception as e:
                    tree.insert('', tk.END, values=(
                        os.path.basename(file_path), f'Error: {str(e)[:20]}...',
                        'N/A', 'N/A', 'N/A'
                    ))

            batch_progress['value'] = len(files)
            export_btn.config(state='normal')

            # Export function
            def export_batch():
                filename = filedialog.asksaveasfilename(
                    title="Export Batch Results",
                    defaultextension=".json",
                    filetypes=[("JSON files", "*.json"), ("Excel files", "*.xlsx")]
                )
                if filename:
                    if filename.endswith('.xlsx'):
                        try:
                            import pandas as pd
                            df = pd.json_normalize(results)
                            df.to_excel(filename, index=False)
                            messagebox.showinfo("Success", f"Exported to {filename}")
                        except ImportError:
                            messagebox.showerror("Error", "Excel export requires pandas")
                    else:
                        with open(filename, 'w') as f:
                            json.dump(results, f, indent=2)
                        messagebox.showinfo("Success", f"Exported to {filename}")

            export_btn.config(command=export_batch)

        threading.Thread(target=process_batch, daemon=True).start()

    def compare_resumes(self):
        """Compare two resumes side by side"""
        files = filedialog.askopenfilenames(
            title="Select 2 Resume Files to Compare",
            filetypes=[
                ("All Supported", "*.pdf;*.docx;*.doc;*.txt"),
                ("PDF files", "*.pdf"),
                ("Word documents", "*.docx;*.doc")
            ]
        )

        if len(files) != 2:
            messagebox.showwarning("Warning", "Please select exactly 2 files to compare")
            return

        # Create comparison window
        compare_window = tk.Toplevel(self.root)
        compare_window.title("Resume Comparison")
        compare_window.geometry("1200x700")

        # Main frame
        main_frame = ttk.Frame(compare_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="Resume Comparison",
                 font=("Arial", 14, "bold")).pack(pady=(0, 10))

        # Comparison frame
        comp_frame = ttk.Frame(main_frame)
        comp_frame.pack(fill=tk.BOTH, expand=True)

        # Left panel
        left_frame = ttk.LabelFrame(comp_frame, text=f"Resume 1: {os.path.basename(files[0])}",
                                   padding="5")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        left_text = scrolledtext.ScrolledText(left_frame, height=25, width=50)
        left_text.pack(fill=tk.BOTH, expand=True)

        # Right panel
        right_frame = ttk.LabelFrame(comp_frame, text=f"Resume 2: {os.path.basename(files[1])}",
                                    padding="5")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        right_text = scrolledtext.ScrolledText(right_frame, height=25, width=50)
        right_text.pack(fill=tk.BOTH, expand=True)

        # Process both files
        def process_comparison():
            for i, (file_path, text_widget) in enumerate([(files[0], left_text), (files[1], right_text)]):
                try:
                    url = self.api_url_var.get().rstrip('/') + '/resume'
                    with open(file_path, 'rb') as file:
                        files_data = {'file': file}
                        data = {'convert_skills_to_dict_format': 'true'}
                        response = requests.post(url, files=files_data, data=data, timeout=120)

                    if response.status_code == 200:
                        result = response.json()
                        formatted = json.dumps(result, indent=2, ensure_ascii=False)
                        text_widget.insert(tk.END, formatted)
                    else:
                        text_widget.insert(tk.END, f"Error: {response.status_code}\n{response.text}")

                except Exception as e:
                    text_widget.insert(tk.END, f"Error processing file: {str(e)}")

        threading.Thread(target=process_comparison, daemon=True).start()

    def analyze_skills(self):
        """Analyze and visualize skills from the last processed resume"""
        if not self.last_result:
            messagebox.showwarning("Warning", "No resume data to analyze. Process a resume first.")
            return

        skills = self.last_result.get('skills', [])
        if not skills:
            messagebox.showwarning("Warning", "No skills found in the last processed resume.")
            return

        # Create analysis window
        analysis_window = tk.Toplevel(self.root)
        analysis_window.title("Skills Analysis")
        analysis_window.geometry("800x600")

        # Main frame
        main_frame = ttk.Frame(analysis_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="Skills Analysis",
                 font=("Arial", 14, "bold")).pack(pady=(0, 10))

        # Notebook for different views
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # Skills list tab
        list_frame = ttk.Frame(notebook)
        notebook.add(list_frame, text="Skills List")

        skills_listbox = tk.Listbox(list_frame, font=("Arial", 10))
        skills_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Populate skills list
        if isinstance(skills, list):
            for skill in skills:
                skills_listbox.insert(tk.END, skill)
        elif isinstance(skills, dict):
            for category, skill_list in skills.items():
                skills_listbox.insert(tk.END, f"=== {category.upper()} ===")
                if isinstance(skill_list, list):
                    for skill in skill_list:
                        skills_listbox.insert(tk.END, f"  • {skill}")

        # Statistics tab
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Statistics")

        stats_text = scrolledtext.ScrolledText(stats_frame, height=20, width=60)
        stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Generate statistics
        stats = f"Skills Analysis Report\n"
        stats += f"{'='*50}\n\n"

        if isinstance(skills, list):
            stats += f"Total Skills: {len(skills)}\n"
            stats += f"Skills per category: N/A (flat list)\n\n"
            stats += "All Skills:\n"
            for i, skill in enumerate(skills, 1):
                stats += f"{i:2d}. {skill}\n"
        elif isinstance(skills, dict):
            total_skills = sum(len(v) if isinstance(v, list) else 1 for v in skills.values())
            stats += f"Total Skills: {total_skills}\n"
            stats += f"Categories: {len(skills)}\n\n"

            for category, skill_list in skills.items():
                count = len(skill_list) if isinstance(skill_list, list) else 1
                stats += f"{category}: {count} skills\n"

            stats += "\nDetailed Breakdown:\n"
            for category, skill_list in skills.items():
                stats += f"\n{category.upper()}:\n"
                if isinstance(skill_list, list):
                    for skill in skill_list:
                        stats += f"  • {skill}\n"
                else:
                    stats += f"  • {skill_list}\n"

        stats_text.insert(tk.END, stats)

        # Export skills button
        export_frame = ttk.Frame(main_frame)
        export_frame.pack(fill=tk.X, pady=(10, 0))

        def export_skills():
            filename = filedialog.asksaveasfilename(
                title="Export Skills",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("JSON files", "*.json")]
            )
            if filename:
                if filename.endswith('.json'):
                    with open(filename, 'w') as f:
                        json.dump(skills, f, indent=2)
                else:
                    with open(filename, 'w') as f:
                        f.write(stats_text.get(1.0, tk.END))
                messagebox.showinfo("Success", f"Skills exported to {filename}")

        ttk.Button(export_frame, text="Export Skills", command=export_skills).pack()

def main():
    root = tk.Tk()
    app = ResumeParserGUI(root)
    
    # Save settings on close
    def on_closing():
        app.save_settings()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
