=== RAW RESPONSE ===
Length: 4190
Source: Gaja_Prasanth Resume.pdf
Timestamp: 1752500312.5394955
=== CONTENT ===
```json
{
    "name": "V Gaja Prasanth",
    "email": "<EMAIL>",
    "phone": "+************",
    "education": [
        {
            "degree": "B.Tech: CSE (Artificial Intelligence And Machine Learning)",
            "institution": "Sri Ramachandra Engineering And Technology - Porur, India",
            "year": "2021-2025",
            "grade": "9.0/10"
        }
    ],
    "highest_education": "B.Tech in Computer Science (Artificial Intelligence And Machine Learning)",
    "skills": [
        "Python",
        "SQL",
        "Machine Learning",
        "Deep Learning",
        "NLP",
        "Text Analytics",
        "Hugging Face",
        "LLM Integration",
        "Prompt Engineering",
        "FastAPI",
        "Kokoro TTS",
        "Ollama",
        "REST APIs",
        "AWS (EC2, Nginx)",
        "Model Deployment"
    ],
    "experience": [
        {
            "company_name": "SRET JUN ’22 - JUL ’22",
            "role": "Student Intern",
            "duration": "JUN ’22 - JUL ’22",
            "key_responsibilities": "Implemented a leaf disease detection project using VGG19, employing data augmentation and transfer learning to enhance model performance."
        },
        {
            "company_name": "Qwings IIT Madras RP AUG ’23 - OCT ’23",
            "role": "Student Intern",
            "duration": "AUG ’23 - OCT ’23",
            "key_responsibilities": "Integrated multiple machine learning projects into website using Flask for seamless model de-ployment and user interaction."
        },
        {
            "company_name": "Qwings IIT Madras RP MAY ’24 - JUN ’24",
            "role": "Web Development Instructor",
            "duration": "MAY ’24 - JUN ’24",
            "key_responsibilities": "Taught fundamental web development concepts to children, focusing on HTML and CSS. Received positive feedback from students and parents, contributing to the company’s community outreach and educational initiatives."
        },
        {
            "company_name": "Novaautomata MARCH ’25 -",
            "role": "AI Intern",
            "duration": "MARCH ’25 -",
            "key_responsibilities": "Working in this startup company has given me exposure to multiple new tools and technologies to integrate with ai models.\n-Developed an AI-powered Resume Screening System using FastAPI and multiple LLMs. It parses resumes, matches them with job descriptions, and returns structured JSON insights with NLP-based smart filtering and scoring.\n-Built a multimodal Interview Persona Chatbot using self-hosted open-source LLMs and Kokoro TTS, enabling voice-based mock interviews with dynamic question generation and real-time audio responses."
        }
    ],
    "projects": [
        {
            "name": "Leaf Disease Detection",
            "description": "I developed a leaf disease detection model using VGG19, achieving high accuracy in classifying plant diseases for improved agricultural monitoring."
        },
        {
            "name": "Neural Style Transfer",
            "description": "I implemented Arbitrary Neural Style Transfer using TensorFlow Hub’s model to apply diverse artistic styles to images."
        },
        {
            "name": "Medical Transcript Classification",
            "description": "I developed a medical transcript classification system using N-grams and various supervised machine learning algorithms, achieving accurate categorization of clinical texts."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Artificial Intelligence",
        "Machine Learning",
        "NLP"
    ],
    "languages_known": [
        "English",
        "Tamil"
    ],
    "achievements": [
        "Received positive feedback from students and parents, contributing to the company’s community outreach and educational initiatives"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Passionate developer focused on building user-friendly artificial intelligence applications, driven to deliver innovative and impactful solutions.",
    "personal_projects": [],
    