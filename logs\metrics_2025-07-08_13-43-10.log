{"event": "session_start", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "timestamp": "2025-07-08T13:43:10.763240", "message": "New API session started"}
{"event": "request_start", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "8c588914-eb02-4863-8d9f-928db93203ee", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:43:27.738237", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "8c588914-eb02-4863-8d9f-928db93203ee", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:43:27.760134", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "8c588914-eb02-4863-8d9f-928db93203ee", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:43:27.761128", "file_size_bytes": 72406, "message": "Custom metric: file_size_bytes=72406"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "8c588914-eb02-4863-8d9f-928db93203ee", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:43:27.761128", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "8c588914-eb02-4863-8d9f-928db93203ee", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:43:27.761128", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "8c588914-eb02-4863-8d9f-928db93203ee", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:43:27.761128", "file_processing_time": 0.01838397979736328, "message": "Custom metric: file_processing_time=0.01838397979736328"}
{"event": "request_complete", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "8c588914-eb02-4863-8d9f-928db93203ee", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:15.658767", "total_time_seconds": 47.920530796051025, "status_code": 200, "message": "Request completed in 47.9205s with status 200"}
{"event": "request_start", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "91e6e850-ac52-489d-9d05-dfc4011377cf", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:29.055448", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "91e6e850-ac52-489d-9d05-dfc4011377cf", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:29.079488", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "91e6e850-ac52-489d-9d05-dfc4011377cf", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:29.079488", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "91e6e850-ac52-489d-9d05-dfc4011377cf", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:29.080488", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "91e6e850-ac52-489d-9d05-dfc4011377cf", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:29.080488", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "91e6e850-ac52-489d-9d05-dfc4011377cf", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:29.080488", "file_processing_time": 0.015028953552246094, "message": "Custom metric: file_processing_time=0.015028953552246094"}
{"event": "request_complete", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "91e6e850-ac52-489d-9d05-dfc4011377cf", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:45.732497", "total_time_seconds": 16.677048444747925, "status_code": 200, "message": "Request completed in 16.6770s with status 200"}
{"event": "request_start", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "eb37f1b3-0444-496f-a548-04bd798fb7f6", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:47.785067", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "eb37f1b3-0444-496f-a548-04bd798fb7f6", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:47.804083", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "eb37f1b3-0444-496f-a548-04bd798fb7f6", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:47.804083", "file_size_bytes": 81408, "message": "Custom metric: file_size_bytes=81408"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "eb37f1b3-0444-496f-a548-04bd798fb7f6", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:47.804083", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "eb37f1b3-0444-496f-a548-04bd798fb7f6", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:47.804083", "extracted_text_length": 2452, "message": "Custom metric: extracted_text_length=2452"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "eb37f1b3-0444-496f-a548-04bd798fb7f6", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:44:47.804083", "file_processing_time": 0.014509439468383789, "message": "Custom metric: file_processing_time=0.014509439468383789"}
{"event": "request_complete", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "eb37f1b3-0444-496f-a548-04bd798fb7f6", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:45:00.725395", "total_time_seconds": 12.940327882766724, "status_code": 200, "message": "Request completed in 12.9403s with status 200"}
{"event": "request_start", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b9d11a92-5365-4c43-b122-7f83fcaa0b66", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:45:02.773226", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b9d11a92-5365-4c43-b122-7f83fcaa0b66", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:45:02.790240", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b9d11a92-5365-4c43-b122-7f83fcaa0b66", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:45:02.790240", "file_size_bytes": 72406, "message": "Custom metric: file_size_bytes=72406"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b9d11a92-5365-4c43-b122-7f83fcaa0b66", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:45:02.791241", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b9d11a92-5365-4c43-b122-7f83fcaa0b66", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:45:02.791241", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b9d11a92-5365-4c43-b122-7f83fcaa0b66", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:45:02.791241", "file_processing_time": 0.0150146484375, "message": "Custom metric: file_processing_time=0.0150146484375"}
{"event": "request_complete", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b9d11a92-5365-4c43-b122-7f83fcaa0b66", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:45:43.336443", "total_time_seconds": 40.56321740150452, "status_code": 200, "message": "Request completed in 40.5632s with status 200"}
{"event": "request_start", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "4dc1ce14-6099-4b9f-b037-4e62366a77fa", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:45.382817", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "4dc1ce14-6099-4b9f-b037-4e62366a77fa", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:45.383816", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "4dc1ce14-6099-4b9f-b037-4e62366a77fa", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:45.394326", "final_score": 3.6, "message": "Custom metric: final_score=3.6"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "4dc1ce14-6099-4b9f-b037-4e62366a77fa", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:45.394326", "fit_category": "Weak Match", "message": "Custom metric: fit_category=Weak Match"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "4dc1ce14-6099-4b9f-b037-4e62366a77fa", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:45.394326", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "4dc1ce14-6099-4b9f-b037-4e62366a77fa", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:45.394326", "log_folder": "intervet_new_logs\\intervet_new_20250708_134545_387_1751962545387", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_134545_387_1751962545387"}
{"event": "request_complete", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "4dc1ce14-6099-4b9f-b037-4e62366a77fa", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:45.395326", "total_time_seconds": 0.012509822845458984, "status_code": 200, "message": "Request completed in 0.0125s with status 200"}
{"event": "request_start", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "54aa3811-6df0-435c-858c-e2fe50ddc3ac", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:47.437131", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "54aa3811-6df0-435c-858c-e2fe50ddc3ac", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:47.438133", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "54aa3811-6df0-435c-858c-e2fe50ddc3ac", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:47.449647", "final_score": 6.04, "message": "Custom metric: final_score=6.04"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "54aa3811-6df0-435c-858c-e2fe50ddc3ac", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:47.449647", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "54aa3811-6df0-435c-858c-e2fe50ddc3ac", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:47.449647", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "54aa3811-6df0-435c-858c-e2fe50ddc3ac", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:47.449647", "log_folder": "intervet_new_logs\\intervet_new_20250708_134547_441_1751962547441", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_134547_441_1751962547441"}
{"event": "request_complete", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "54aa3811-6df0-435c-858c-e2fe50ddc3ac", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:45:47.450647", "total_time_seconds": 0.013516902923583984, "status_code": 200, "message": "Request completed in 0.0135s with status 200"}
{"event": "request_start", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b6796989-0680-4b41-9e2a-7601d8ff4864", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:46:25.308243", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b6796989-0680-4b41-9e2a-7601d8ff4864", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:46:25.320241", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b6796989-0680-4b41-9e2a-7601d8ff4864", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:46:25.321242", "file_size_bytes": 72406, "message": "Custom metric: file_size_bytes=72406"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b6796989-0680-4b41-9e2a-7601d8ff4864", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:46:25.321242", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b6796989-0680-4b41-9e2a-7601d8ff4864", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:46:25.321242", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b6796989-0680-4b41-9e2a-7601d8ff4864", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:46:25.321242", "file_processing_time": 0.009999513626098633, "message": "Custom metric: file_processing_time=0.009999513626098633"}
{"event": "request_complete", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "request_id": "b6796989-0680-4b41-9e2a-7601d8ff4864", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:47:09.954447", "total_time_seconds": 44.64620399475098, "status_code": 200, "message": "Request completed in 44.6462s with status 200"}
{"event": "session_end", "session_id": "5ee0bd80-1aa7-48f0-9af7-0bef2ef9e17c", "timestamp": "2025-07-08T13:48:10.355350", "message": "API session ended"}
