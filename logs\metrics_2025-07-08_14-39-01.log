{"event": "session_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "timestamp": "2025-07-08T14:39:01.137346", "message": "New API session started"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "ca1fd8de-51e8-410c-9be3-4db32671d988", "endpoint": "/", "timestamp": "2025-07-08T14:39:05.601597", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "ca1fd8de-51e8-410c-9be3-4db32671d988", "endpoint": "/", "timestamp": "2025-07-08T14:39:05.603103", "total_time_seconds": 0.0015056133270263672, "status_code": 200, "message": "Request completed in 0.0015s with status 200"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "84b3114e-4639-41f7-972a-425db0b56f7d", "endpoint": "/docs", "timestamp": "2025-07-08T14:39:14.460933", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "84b3114e-4639-41f7-972a-425db0b56f7d", "endpoint": "/docs", "timestamp": "2025-07-08T14:39:14.461933", "total_time_seconds": 0.0010004043579101562, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "77631c47-2162-436e-9399-ace6ec1e7528", "endpoint": "/openapi.json", "timestamp": "2025-07-08T14:39:14.538683", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "77631c47-2162-436e-9399-ace6ec1e7528", "endpoint": "/openapi.json", "timestamp": "2025-07-08T14:39:14.552686", "total_time_seconds": 0.014002561569213867, "status_code": 200, "message": "Request completed in 0.0140s with status 200"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.811133", "message": "Request started for endpoint: /resume"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "resume", "timestamp": "2025-07-08T14:39:30.812458", "message": "Custom metric: endpoint=resume"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.813460", "file_name": "Resume-Raman Luhach.pdf", "message": "Custom metric: file_name=Resume-Raman Luhach.pdf"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.813460", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.814458", "file_size": 73845, "message": "Custom metric: file_size=73845"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.837464", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.837464", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.837464", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.837464", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.837464", "file_processing_time": 0.022003173828125, "message": "Custom metric: file_processing_time=0.022003173828125"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.837464", "text_extraction_time": 0.022003173828125, "message": "Custom metric: text_extraction_time=0.022003173828125"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:30.837464", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:46.012758", "parsing_time": 15.174297094345093, "message": "Custom metric: parsing_time=15.174297094345093"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:46.012758", "confidence_score": 0.55, "message": "Custom metric: confidence_score=0.55"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:46.012758", "fields_extracted": 19, "message": "Custom metric: fields_extracted=19"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:46.012758", "skills_count": 12, "message": "Custom metric: skills_count=12"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:46.012758", "education_count": 3, "message": "Custom metric: education_count=3"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:46.013757", "experience_count": 0, "message": "Custom metric: experience_count=0"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:46.013757", "total_processing_time": 15.196300268173218, "message": "Custom metric: total_processing_time=15.196300268173218"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "d394462d-ef1c-4793-ae0e-780837543277", "endpoint": "/resume", "timestamp": "2025-07-08T14:39:46.014758", "total_time_seconds": 15.203625202178955, "status_code": 200, "message": "Request completed in 15.2036s with status 200"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.888408", "message": "Request started for endpoint: /resume"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "resume", "timestamp": "2025-07-08T14:40:10.889408", "message": "Custom metric: endpoint=resume"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.890408", "file_name": "Resume-Raman Luhach.pdf", "message": "Custom metric: file_name=Resume-Raman Luhach.pdf"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.890408", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.891409", "file_size": 73845, "message": "Custom metric: file_size=73845"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.904406", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.905408", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.905408", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.905408", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.905408", "file_processing_time": 0.012997150421142578, "message": "Custom metric: file_processing_time=0.012997150421142578"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.905408", "text_extraction_time": 0.01399850845336914, "message": "Custom metric: text_extraction_time=0.01399850845336914"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:10.905408", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:21.798283", "parsing_time": 10.89287519454956, "message": "Custom metric: parsing_time=10.89287519454956"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:21.798283", "confidence_score": 0.55, "message": "Custom metric: confidence_score=0.55"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:21.798283", "fields_extracted": 19, "message": "Custom metric: fields_extracted=19"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:21.798283", "skills_count": 12, "message": "Custom metric: skills_count=12"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:21.798283", "education_count": 3, "message": "Custom metric: education_count=3"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:21.798283", "experience_count": 0, "message": "Custom metric: experience_count=0"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:21.798283", "total_processing_time": 10.90687370300293, "message": "Custom metric: total_processing_time=10.90687370300293"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "44c93296-6de3-4300-9fb9-3d1293df6088", "endpoint": "/resume", "timestamp": "2025-07-08T14:40:21.799282", "total_time_seconds": 10.910874366760254, "status_code": 200, "message": "Request completed in 10.9109s with status 200"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "b1ef0389-1f32-4002-96f8-8bb443fa1bac", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:41:21.149277", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "b1ef0389-1f32-4002-96f8-8bb443fa1bac", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:41:21.167265", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "b1ef0389-1f32-4002-96f8-8bb443fa1bac", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:41:21.167265", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "b1ef0389-1f32-4002-96f8-8bb443fa1bac", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:41:21.167265", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "b1ef0389-1f32-4002-96f8-8bb443fa1bac", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:41:21.167265", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "b1ef0389-1f32-4002-96f8-8bb443fa1bac", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:41:21.168266", "file_processing_time": 0.016000986099243164, "message": "Custom metric: file_processing_time=0.016000986099243164"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "b1ef0389-1f32-4002-96f8-8bb443fa1bac", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T14:41:37.282047", "total_time_seconds": 16.13276982307434, "status_code": 200, "message": "Request completed in 16.1328s with status 200"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "4bdf1f07-60a7-4c39-9c15-cd5655de2110", "endpoint": "/jd", "timestamp": "2025-07-08T14:41:48.256255", "message": "Request started for endpoint: /jd"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "4bdf1f07-60a7-4c39-9c15-cd5655de2110", "endpoint": "/jd", "timestamp": "2025-07-08T14:41:48.260255", "total_time_seconds": 0.003999948501586914, "status_code": 400, "message": "Request completed in 0.0040s with status 400"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "7a7a1ca9-d347-49dd-896d-de742bbf5cc3", "endpoint": "/jd", "timestamp": "2025-07-08T14:41:58.455031", "message": "Request started for endpoint: /jd"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "7a7a1ca9-d347-49dd-896d-de742bbf5cc3", "endpoint": "/jd", "timestamp": "2025-07-08T14:41:58.457036", "total_time_seconds": 0.002004384994506836, "status_code": 400, "message": "Request completed in 0.0020s with status 400"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "c390d558-8417-49b1-9ab9-405c42331e37", "endpoint": "/jd_parser", "timestamp": "2025-07-08T14:42:09.571343", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "c390d558-8417-49b1-9ab9-405c42331e37", "endpoint": "/jd_parser", "timestamp": "2025-07-08T14:42:25.573451", "total_time_seconds": 16.002107620239258, "status_code": 200, "message": "Request completed in 16.0021s with status 200"}
{"event": "request_start", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "11d74a48-896e-4eff-a402-914a72d89cf2", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:42:54.788547", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "11d74a48-896e-4eff-a402-914a72d89cf2", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:42:54.790541", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "11d74a48-896e-4eff-a402-914a72d89cf2", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:42:54.806543", "final_score": 3.15, "message": "Custom metric: final_score=3.15"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "11d74a48-896e-4eff-a402-914a72d89cf2", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:42:54.806543", "fit_category": "Weak Match", "message": "Custom metric: fit_category=Weak Match"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "11d74a48-896e-4eff-a402-914a72d89cf2", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:42:54.806543", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "11d74a48-896e-4eff-a402-914a72d89cf2", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:42:54.806543", "log_folder": "intervet_new_logs\\intervet_new_20250708_144254_801_1751965974801", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_144254_801_1751965974801"}
{"event": "request_complete", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "request_id": "11d74a48-896e-4eff-a402-914a72d89cf2", "endpoint": "/intervet_new", "timestamp": "2025-07-08T14:42:54.807543", "total_time_seconds": 0.018996000289916992, "status_code": 200, "message": "Request completed in 0.0190s with status 200"}
{"event": "session_end", "session_id": "d378ccb3-d302-4694-87fa-ea37c61b9f20", "timestamp": "2025-07-08T14:47:43.707477", "message": "API session ended"}
