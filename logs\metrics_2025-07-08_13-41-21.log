{"event": "session_start", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "timestamp": "2025-07-08T13:41:21.344244", "message": "New API session started"}
{"event": "request_start", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "request_id": "7f27eabd-e331-48df-a184-1d1c0e89d0d3", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:41:23.926981", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "request_id": "7f27eabd-e331-48df-a184-1d1c0e89d0d3", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:41:23.927982", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "request_id": "7f27eabd-e331-48df-a184-1d1c0e89d0d3", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:41:23.938005", "final_score": 3.6, "message": "Custom metric: final_score=3.6"}
{"event": "custom_metric", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "request_id": "7f27eabd-e331-48df-a184-1d1c0e89d0d3", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:41:23.938005", "fit_category": "Weak Match", "message": "Custom metric: fit_category=Weak Match"}
{"event": "custom_metric", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "request_id": "7f27eabd-e331-48df-a184-1d1c0e89d0d3", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:41:23.938005", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "request_id": "7f27eabd-e331-48df-a184-1d1c0e89d0d3", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:41:23.938005", "log_folder": "intervet_new_logs\\intervet_new_20250708_134123_931_1751962283931", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_134123_931_1751962283931"}
{"event": "request_complete", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "request_id": "7f27eabd-e331-48df-a184-1d1c0e89d0d3", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:41:23.939004", "total_time_seconds": 0.01202249526977539, "status_code": 200, "message": "Request completed in 0.0120s with status 200"}
{"event": "session_end", "session_id": "2e99c654-dad1-4aaf-b6b5-d105aa6e90d9", "timestamp": "2025-07-08T13:43:10.114602", "message": "API session ended"}
