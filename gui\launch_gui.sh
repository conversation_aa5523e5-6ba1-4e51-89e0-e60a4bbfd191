#!/bin/bash

echo "Resume Parser GUI Launcher"
echo "=========================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3 first"
    exit 1
fi

echo "Python 3 is available"

# Check if required files exist
if [ ! -f "resume_parser_gui.py" ]; then
    echo "Error: resume_parser_gui.py not found"
    echo "Make sure all files are in the same directory"
    exit 1
fi

if [ ! -f "gui_requirements.txt" ]; then
    echo "Error: gui_requirements.txt not found"
    echo "Make sure all files are in the same directory"
    exit 1
fi

echo "Checking dependencies and launching..."
python3 launch_gui.py
