{"event": "session_start", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "timestamp": "2025-07-10T10:32:47.178849", "message": "New API session started"}
{"event": "request_start", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "2cb198d4-5467-4914-869a-494619173756", "endpoint": "/", "timestamp": "2025-07-10T10:32:48.987828", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "2cb198d4-5467-4914-869a-494619173756", "endpoint": "/", "timestamp": "2025-07-10T10:32:48.990362", "total_time_seconds": 0.0025339126586914062, "status_code": 200, "message": "Request completed in 0.0025s with status 200"}
{"event": "request_start", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "c36bde05-79c1-4666-b9d7-8e20299857be", "endpoint": "/favicon.ico", "timestamp": "2025-07-10T10:32:49.123066", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "c36bde05-79c1-4666-b9d7-8e20299857be", "endpoint": "/favicon.ico", "timestamp": "2025-07-10T10:32:49.124065", "total_time_seconds": 0.0009996891021728516, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "0150a8fb-172c-407e-9efc-2d2f99cba57d", "endpoint": "/docs", "timestamp": "2025-07-10T10:32:53.423163", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "0150a8fb-172c-407e-9efc-2d2f99cba57d", "endpoint": "/docs", "timestamp": "2025-07-10T10:32:53.424167", "total_time_seconds": 0.0010037422180175781, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "58ce6b87-ad15-417d-a967-548498d194d8", "endpoint": "/openapi.json", "timestamp": "2025-07-10T10:32:53.554940", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "58ce6b87-ad15-417d-a967-548498d194d8", "endpoint": "/openapi.json", "timestamp": "2025-07-10T10:32:53.582038", "total_time_seconds": 0.02709817886352539, "status_code": 200, "message": "Request completed in 0.0271s with status 200"}
{"event": "request_start", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "9f4bebdd-5f66-470c-8056-f7bf7ee7b8cc", "endpoint": "/hybrid_resume", "timestamp": "2025-07-10T10:33:12.923337", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "9f4bebdd-5f66-470c-8056-f7bf7ee7b8cc", "endpoint": "/hybrid_resume", "timestamp": "2025-07-10T10:33:12.960057", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "9f4bebdd-5f66-470c-8056-f7bf7ee7b8cc", "endpoint": "/hybrid_resume", "timestamp": "2025-07-10T10:33:12.960057", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "9f4bebdd-5f66-470c-8056-f7bf7ee7b8cc", "endpoint": "/hybrid_resume", "timestamp": "2025-07-10T10:33:12.960057", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "9f4bebdd-5f66-470c-8056-f7bf7ee7b8cc", "endpoint": "/hybrid_resume", "timestamp": "2025-07-10T10:33:12.960057", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "9f4bebdd-5f66-470c-8056-f7bf7ee7b8cc", "endpoint": "/hybrid_resume", "timestamp": "2025-07-10T10:33:12.961066", "file_processing_time": 0.030153751373291016, "message": "Custom metric: file_processing_time=0.030153751373291016"}
{"event": "request_complete", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "9f4bebdd-5f66-470c-8056-f7bf7ee7b8cc", "endpoint": "/hybrid_resume", "timestamp": "2025-07-10T10:33:56.696258", "total_time_seconds": 43.772921323776245, "status_code": 200, "message": "Request completed in 43.7729s with status 200"}
{"event": "request_start", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "3f07d41c-4c0b-4cc2-b484-fd44f597dabe", "endpoint": "/jd_parser", "timestamp": "2025-07-10T10:33:56.697887", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "3f07d41c-4c0b-4cc2-b484-fd44f597dabe", "endpoint": "/jd_parser", "timestamp": "2025-07-10T10:34:09.041824", "total_time_seconds": 12.343936204910278, "status_code": 200, "message": "Request completed in 12.3439s with status 200"}
{"event": "request_start", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "633e05a0-b9f3-4850-8668-9d524ff9407f", "endpoint": "/intervet_new", "timestamp": "2025-07-10T10:34:48.036087", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "633e05a0-b9f3-4850-8668-9d524ff9407f", "endpoint": "/intervet_new", "timestamp": "2025-07-10T10:34:48.039130", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "633e05a0-b9f3-4850-8668-9d524ff9407f", "endpoint": "/intervet_new", "timestamp": "2025-07-10T10:34:48.059576", "final_score": 3.471212121212121, "message": "Custom metric: final_score=3.471212121212121"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "633e05a0-b9f3-4850-8668-9d524ff9407f", "endpoint": "/intervet_new", "timestamp": "2025-07-10T10:34:48.059576", "fit_category": "Weak Match", "message": "Custom metric: fit_category=Weak Match"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "633e05a0-b9f3-4850-8668-9d524ff9407f", "endpoint": "/intervet_new", "timestamp": "2025-07-10T10:34:48.059576", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "633e05a0-b9f3-4850-8668-9d524ff9407f", "endpoint": "/intervet_new", "timestamp": "2025-07-10T10:34:48.059576", "log_folder": "intervet_new_logs\\intervet_new_20250710_103448_052_1752123888052", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250710_103448_052_1752123888052"}
{"event": "request_complete", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "request_id": "633e05a0-b9f3-4850-8668-9d524ff9407f", "endpoint": "/intervet_new", "timestamp": "2025-07-10T10:34:48.060581", "total_time_seconds": 0.024494171142578125, "status_code": 200, "message": "Request completed in 0.0245s with status 200"}
{"event": "session_end", "session_id": "90b7cc4c-74cf-4409-82be-439307a8209f", "timestamp": "2025-07-10T10:36:59.535949", "message": "API session ended"}
