{"event": "session_start", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "timestamp": "2025-07-08T13:37:40.904323", "message": "New API session started"}
{"event": "request_start", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "0509af40-6f4b-4a76-94e2-7fc07fd8a705", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:37:43.340095", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "0509af40-6f4b-4a76-94e2-7fc07fd8a705", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:37:43.341095", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "0509af40-6f4b-4a76-94e2-7fc07fd8a705", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:37:43.349187", "final_score": 5.6, "message": "Custom metric: final_score=5.6"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "0509af40-6f4b-4a76-94e2-7fc07fd8a705", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:37:43.349187", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "0509af40-6f4b-4a76-94e2-7fc07fd8a705", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:37:43.349187", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "0509af40-6f4b-4a76-94e2-7fc07fd8a705", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:37:43.349187", "log_folder": "intervet_new_logs\\intervet_new_20250708_133743_344_1751962063344", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_133743_344_1751962063344"}
{"event": "request_complete", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "0509af40-6f4b-4a76-94e2-7fc07fd8a705", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:37:43.350187", "total_time_seconds": 0.010091304779052734, "status_code": 200, "message": "Request completed in 0.0101s with status 200"}
{"event": "request_start", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "c09ba417-304b-4f22-a895-dc2817267659", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:16.279758", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "c09ba417-304b-4f22-a895-dc2817267659", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:16.280757", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "c09ba417-304b-4f22-a895-dc2817267659", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:16.286778", "final_score": 5.6, "message": "Custom metric: final_score=5.6"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "c09ba417-304b-4f22-a895-dc2817267659", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:16.286778", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "c09ba417-304b-4f22-a895-dc2817267659", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:16.286778", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "c09ba417-304b-4f22-a895-dc2817267659", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:16.286778", "log_folder": "intervet_new_logs\\intervet_new_20250708_133816_280_1751962096280", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_133816_280_1751962096280"}
{"event": "request_complete", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "c09ba417-304b-4f22-a895-dc2817267659", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:16.287780", "total_time_seconds": 0.008021831512451172, "status_code": 200, "message": "Request completed in 0.0080s with status 200"}
{"event": "request_start", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "6fbef21b-f2ed-4608-9917-8be304c729ae", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:30.972840", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "6fbef21b-f2ed-4608-9917-8be304c729ae", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:30.974354", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "6fbef21b-f2ed-4608-9917-8be304c729ae", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:30.980360", "final_score": 5.6, "message": "Custom metric: final_score=5.6"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "6fbef21b-f2ed-4608-9917-8be304c729ae", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:30.981360", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "6fbef21b-f2ed-4608-9917-8be304c729ae", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:30.981360", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "6fbef21b-f2ed-4608-9917-8be304c729ae", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:30.981360", "log_folder": "intervet_new_logs\\intervet_new_20250708_133830_975_1751962110975", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_133830_975_1751962110975"}
{"event": "request_complete", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "request_id": "6fbef21b-f2ed-4608-9917-8be304c729ae", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:38:30.981360", "total_time_seconds": 0.008519411087036133, "status_code": 200, "message": "Request completed in 0.0085s with status 200"}
{"event": "session_end", "session_id": "74a4dd18-12a9-4415-8bbd-ff31f6988a3f", "timestamp": "2025-07-08T13:41:20.655710", "message": "API session ended"}
