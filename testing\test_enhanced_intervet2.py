#!/usr/bin/env python3
"""
Test script for the enhanced /intervet2 endpoint
Tests the new functionality that uses hybrid_resume, jd_parser, and intervet_new internally
"""

import requests
import json
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
INTERVET2_URL = f"{API_BASE_URL}/intervet2"

def test_enhanced_intervet2_endpoint():
    """Test the enhanced /intervet2 endpoint with real files"""
    print("🧪 Testing Enhanced /intervet2 Endpoint")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("Start server with: python main.py")
        return False
    
    # Get test files
    resume_dir = Path("resumes for testing")
    jd_dir = Path("jds for testing")
    
    if not resume_dir.exists():
        print(f"❌ Resume directory not found: {resume_dir}")
        return False
    
    if not jd_dir.exists():
        print(f"❌ JD directory not found: {jd_dir}")
        return False
    
    # Get first available resume and JD files
    resume_files = list(resume_dir.glob("*.pdf")) + list(resume_dir.glob("*.docx"))
    jd_files = list(jd_dir.glob("*.pdf")) + list(jd_dir.glob("*.docx"))
    
    if not resume_files:
        print("❌ No resume files found")
        return False
    
    if not jd_files:
        print("❌ No JD files found")
        return False
    
    # Test with first available files
    resume_file = resume_files[0]
    jd_file = jd_files[0]
    
    print(f"📄 Testing with:")
    print(f"   Resume: {resume_file.name}")
    print(f"   JD: {jd_file.name}")
    
    try:
        # Test the enhanced endpoint
        start_time = time.time()
        
        with open(resume_file, 'rb') as rf, open(jd_file, 'rb') as jf:
            files = {
                'resume_file': (resume_file.name, rf, 'application/pdf' if resume_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
                'jd_file': (jd_file.name, jf, 'application/pdf' if jd_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            }
            
            response = requests.post(INTERVET2_URL, files=files, timeout=300)
        
        processing_time = time.time() - start_time
        
        print(f"⏱️  Total processing time: {processing_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Enhanced /intervet2 endpoint test successful!")
            print("\n📋 Response Structure:")
            print(f"   Resume data keys: {list(result.get('resume_data', {}).keys())}")
            print(f"   JD data keys: {list(result.get('jd_data', {}).keys())}")
            print(f"   Evaluation result keys: {list(result.get('evaluation_result', {}).keys())}")
            
            # Display processing times
            processing_times = result.get('processing_times', {})
            print(f"\n⏱️  Processing Times:")
            for step, time_taken in processing_times.items():
                print(f"   {step}: {time_taken:.2f}s")
            print(f"   Total: {result.get('total_processing_time', 0):.2f}s")
            
            # Display evaluation summary
            eval_result = result.get('evaluation_result', {})
            print(f"\n🎯 Evaluation Summary:")
            print(f"   Final Score: {eval_result.get('total_score', 0):.2f}/10")
            print(f"   Fit Category: {eval_result.get('fit_category', 'Unknown')}")
            print(f"   Summary: {eval_result.get('summary', 'No summary')}")
            
            # Display resume parsing results
            resume_data = result.get('resume_data', {})
            print(f"\n📋 Resume Parsing Results:")
            print(f"   Name: {resume_data.get('name', 'Unknown')}")
            print(f"   Email: {resume_data.get('email', 'Not found')}")
            print(f"   Skills count: {len(resume_data.get('skills', []))}")
            print(f"   Experience entries: {len(resume_data.get('experience', []))}")
            print(f"   Education entries: {len(resume_data.get('education', []))}")
            
            # Display JD parsing results
            jd_data = result.get('jd_data', {})
            print(f"\n📋 JD Parsing Results:")
            print(f"   Job Title: {jd_data.get('job_title', 'Unknown')}")
            print(f"   Company: {jd_data.get('company_name', 'Not specified')}")
            print(f"   Required skills count: {len(jd_data.get('required_skills', []))}")
            print(f"   Preferred skills count: {len(jd_data.get('preferred_skills', []))}")
            print(f"   Required experience: {jd_data.get('required_experience', 'Not specified')}")
            
            # Save detailed results for inspection
            output_file = f"testing/enhanced_intervet2_result_{int(time.time())}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Detailed results saved to: {output_file}")
            
            return True
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_with_custom_weightage():
    """Test the enhanced endpoint with custom weightage"""
    print("\n🧪 Testing Enhanced /intervet2 with Custom Weightage")
    print("=" * 60)
    
    # Get test files
    resume_dir = Path("resumes for testing")
    jd_dir = Path("jds for testing")
    
    resume_files = list(resume_dir.glob("*.pdf")) + list(resume_dir.glob("*.docx"))
    jd_files = list(jd_dir.glob("*.pdf")) + list(jd_dir.glob("*.docx"))
    
    if not resume_files or not jd_files:
        print("❌ Test files not found")
        return False
    
    resume_file = resume_files[0]
    jd_file = jd_files[0]
    
    # Custom weightage configuration
    custom_weightage = {
        "skills": 4.0,
        "experience": 3.0,
        "education": 2.0,
        "certifications": 1.0,
        "location": 0.5,
        "reliability": 0.5
    }
    
    print(f"📄 Testing with custom weightage:")
    print(f"   Skills: {custom_weightage['skills']}")
    print(f"   Experience: {custom_weightage['experience']}")
    print(f"   Education: {custom_weightage['education']}")
    
    try:
        start_time = time.time()
        
        with open(resume_file, 'rb') as rf, open(jd_file, 'rb') as jf:
            files = {
                'resume_file': (resume_file.name, rf, 'application/pdf' if resume_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
                'jd_file': (jd_file.name, jf, 'application/pdf' if jd_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            }
            
            data = {'weightage': json.dumps(custom_weightage)}
            
            response = requests.post(INTERVET2_URL, files=files, data=data, timeout=300)
        
        processing_time = time.time() - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            eval_result = result.get('evaluation_result', {})
            
            print("✅ Custom weightage test successful!")
            print(f"🎯 Final Score: {eval_result.get('total_score', 0):.2f}/10")
            print(f"🎯 Fit Category: {eval_result.get('fit_category', 'Unknown')}")
            
            return True
        else:
            print(f"❌ Custom weightage test failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Custom weightage test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Enhanced /intervet2 Endpoint Tests")
    print("=" * 70)
    
    # Test basic functionality
    success1 = test_enhanced_intervet2_endpoint()
    
    # Test with custom weightage
    success2 = test_with_custom_weightage()
    
    print("\n" + "=" * 70)
    print("📊 Test Summary:")
    print(f"   Basic functionality: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"   Custom weightage: {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! Enhanced /intervet2 endpoint is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the logs above for details.")
