{"event": "session_start", "session_id": "2c091048-d9d5-41ca-8510-2358472db87e", "timestamp": "2025-07-08T14:10:59.217825", "message": "New API session started"}
{"event": "request_start", "session_id": "2c091048-d9d5-41ca-8510-2358472db87e", "request_id": "963940d3-812c-4f2d-80a8-84e2764d8acc", "endpoint": "/", "timestamp": "2025-07-08T14:11:00.946774", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "2c091048-d9d5-41ca-8510-2358472db87e", "request_id": "963940d3-812c-4f2d-80a8-84e2764d8acc", "endpoint": "/", "timestamp": "2025-07-08T14:11:00.948283", "total_time_seconds": 0.0015099048614501953, "status_code": 200, "message": "Request completed in 0.0015s with status 200"}
{"event": "request_start", "session_id": "2c091048-d9d5-41ca-8510-2358472db87e", "request_id": "1115d698-1931-4aa7-8561-bc604e7277af", "endpoint": "/docs", "timestamp": "2025-07-08T14:11:04.780087", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "2c091048-d9d5-41ca-8510-2358472db87e", "request_id": "1115d698-1931-4aa7-8561-bc604e7277af", "endpoint": "/docs", "timestamp": "2025-07-08T14:11:04.781097", "total_time_seconds": 0.0010101795196533203, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "2c091048-d9d5-41ca-8510-2358472db87e", "request_id": "16424a9b-cc77-489c-aa4d-5ec63d2cc6ce", "endpoint": "/openapi.json", "timestamp": "2025-07-08T14:11:04.848940", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "2c091048-d9d5-41ca-8510-2358472db87e", "request_id": "16424a9b-cc77-489c-aa4d-5ec63d2cc6ce", "endpoint": "/openapi.json", "timestamp": "2025-07-08T14:11:04.872530", "total_time_seconds": 0.023590564727783203, "status_code": 200, "message": "Request completed in 0.0236s with status 200"}
{"event": "session_end", "session_id": "2c091048-d9d5-41ca-8510-2358472db87e", "timestamp": "2025-07-08T14:12:15.357719", "message": "API session ended"}
